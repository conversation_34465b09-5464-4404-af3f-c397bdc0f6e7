#!/usr/bin/env python3
"""
MonkeyOCR 快速测试脚本
"""

import time

def main():
    print("🚀 开始MonkeyOCR快速测试...")
    
    try:
        print("正在导入MonkeyOCR...")
        from magic_pdf.model.custom_model import MonkeyOCR
        print("✅ 成功导入MonkeyOCR模块")
        
        print("正在加载模型...")
        start_time = time.time()
        model = MonkeyOCR('model_configs.yaml')
        load_time = time.time() - start_time
        
        print(f"✅ 模型加载成功！耗时: {load_time:.2f}秒")
        print(f"   - 设备: {model.device}")
        print(f"   - 布局模型: {model.layout_model.__class__.__name__}")
        print(f"   - 布局读取模型: {model.layoutreader_model.__class__.__name__}")
        print(f"   - 聊天模型: {model.chat_model.__class__.__name__}")
        
        print("\n🎉 MonkeyOCR测试完成！")
        print("✅ 模型已成功加载并可以使用")
        print("📝 您现在可以使用MonkeyOCR进行文档解析了")
        print("⚠️  注意: 在CPU模式下处理速度较慢，请耐心等待")
        
        return 0
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return 1

if __name__ == "__main__":
    exit(main())
