# This file is autogenerated by the command `make fix-copies`, do not edit.
from ..utils import DummyObject, requires_backends


class Pop2PianoFeatureExtractor(metaclass=DummyObject):
    _backends = ["music"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["music"])


class Pop2PianoTokenizer(metaclass=DummyObject):
    _backends = ["music"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["music"])
