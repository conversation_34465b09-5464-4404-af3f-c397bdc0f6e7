/**
This file was auto-generated by mupdfwrap.py.
*/

#ifndef MUPDF_FUNCTIONS_H
#define MUPDF_FUNCTIONS_H


#include "mupdf/extra.h"

#include "mupdf/fitz.h"
#include "mupdf/pdf.h"

#include <iostream>
#include <string>
#include <vector>


namespace mupdf
{

/*
The keys that are defined for fz_lookup_metadata().
*/
FZ_DATA extern const std::vector<std::string> metadata_keys;

/** Low-level wrapper for `::fz_aa_level()`. */
/**
	Get the number of bits of antialiasing we are
	using (for graphics). Between 0 and 8.
*/
FZ_FUNCTION int ll_fz_aa_level();

/** Low-level wrapper for `::fz_abs()`. */
/**
	Some standard math functions, done as static inlines for speed.
	People with compilers that do not adequately implement inline
	may like to reimplement these using macros.
*/
FZ_FUNCTION float ll_fz_abs(float f);

/** Low-level wrapper for `::fz_absi()`. */
FZ_FUNCTION int ll_fz_absi(int i);

/** Low-level wrapper for `::fz_add_layout_char()`. */
/**
	Add a new char to the line at the end of the layout block.
*/
FZ_FUNCTION void ll_fz_add_layout_char(::fz_layout_block *block, float x, float w, const char *p);

/** Low-level wrapper for `::fz_add_layout_line()`. */
/**
	Add a new line to the end of the layout block.
*/
FZ_FUNCTION void ll_fz_add_layout_line(::fz_layout_block *block, float x, float y, float h, const char *p);

/** Low-level wrapper for `::fz_add_separation()`. */
/**
	Add a separation (null terminated name, colorspace)
*/
FZ_FUNCTION void ll_fz_add_separation(::fz_separations *sep, const char *name, ::fz_colorspace *cs, int cs_channel);

/** Low-level wrapper for `::fz_add_separation_equivalents()`. */
/**
	Add a separation with equivalents (null terminated name,
	colorspace)

	(old, deprecated)
*/
FZ_FUNCTION void ll_fz_add_separation_equivalents(::fz_separations *sep, uint32_t rgba, uint32_t cmyk, const char *name);

/** Low-level wrapper for `::fz_adjust_rect_for_stroke()`. */
/**
	Given a rectangle (assumed to be the bounding box for a path),
	expand it to allow for the expansion of the bbox that would be
	seen by stroking the path with the given stroke state and
	transform.
*/
FZ_FUNCTION ::fz_rect ll_fz_adjust_rect_for_stroke(::fz_rect rect, const ::fz_stroke_state *stroke, ::fz_matrix ctm);

/** Low-level wrapper for `::fz_advance_glyph()`. */
/**
	Return the advance for a given glyph.

	font: The font to look for the glyph in.

	glyph: The glyph to find the advance for.

	wmode: 1 for vertical mode, 0 for horizontal.

	Returns the advance for the glyph.
*/
FZ_FUNCTION float ll_fz_advance_glyph(::fz_font *font, int glyph, int wmode);

/** Low-level wrapper for `::fz_aes_crypt_cbc()`. */
/**
	AES block processing. Encrypts or Decrypts (according to mode,
	which must match what was initially set up) length bytes (which
	must be a multiple of 16), using (and modifying) the insertion
	vector iv, reading from input, and writing to output.

	Never throws an exception.
*/
FZ_FUNCTION void ll_fz_aes_crypt_cbc(::fz_aes *ctx, int mode, size_t length, unsigned char iv[16], const unsigned char *input, unsigned char *output);

/** Low-level wrapper for `::fz_aes_setkey_dec()`. */
/**
	AES decryption intialisation. Fills in the supplied context
	and prepares for decryption using the given key.

	Returns non-zero for error (key size other than 128/192/256).

	Never throws an exception.
*/
FZ_FUNCTION int ll_fz_aes_setkey_dec(::fz_aes *ctx, const unsigned char *key, int keysize);

/** Low-level wrapper for `::fz_aes_setkey_enc()`. */
/**
	AES encryption intialisation. Fills in the supplied context
	and prepares for encryption using the given key.

	Returns non-zero for error (key size other than 128/192/256).

	Never throws an exception.
*/
FZ_FUNCTION int ll_fz_aes_setkey_enc(::fz_aes *ctx, const unsigned char *key, int keysize);

/** Low-level wrapper for `::fz_alpha_from_gray()`. */
FZ_FUNCTION ::fz_pixmap *ll_fz_alpha_from_gray(::fz_pixmap *gray);

/** Low-level wrapper for `::fz_append_base64()`. */
/**
	Write a base64 encoded data block, optionally with periodic newlines.
*/
FZ_FUNCTION void ll_fz_append_base64(::fz_buffer *out, const unsigned char *data, size_t size, int newline);

/** Low-level wrapper for `::fz_append_base64_buffer()`. */
/**
	Append a base64 encoded fz_buffer, optionally with periodic newlines.
*/
FZ_FUNCTION void ll_fz_append_base64_buffer(::fz_buffer *out, ::fz_buffer *data, int newline);

/** Low-level wrapper for `::fz_append_bits()`. */
FZ_FUNCTION void ll_fz_append_bits(::fz_buffer *buf, int value, int count);

/** Low-level wrapper for `::fz_append_bits_pad()`. */
FZ_FUNCTION void ll_fz_append_bits_pad(::fz_buffer *buf);

/** Low-level wrapper for `::fz_append_buffer()`. */
/**
	Append the contents of the source buffer onto the end of the
	destination buffer, extending automatically as required.

	Ownership of buffers does not change.
*/
FZ_FUNCTION void ll_fz_append_buffer(::fz_buffer *destination, ::fz_buffer *source);

/** Low-level wrapper for `::fz_append_byte()`. */
FZ_FUNCTION void ll_fz_append_byte(::fz_buffer *buf, int c);

/** Low-level wrapper for `::fz_append_data()`. */
/**
	fz_append_*: Append data to a buffer.

	The buffer will automatically grow as required.
*/
FZ_FUNCTION void ll_fz_append_data(::fz_buffer *buf, const void *data, size_t len);

/** Low-level wrapper for `::fz_append_image_as_data_uri()`. */
FZ_FUNCTION void ll_fz_append_image_as_data_uri(::fz_buffer *out, ::fz_image *image);

/** Low-level wrapper for `::fz_append_int16_be()`. */
FZ_FUNCTION void ll_fz_append_int16_be(::fz_buffer *buf, int x);

/** Low-level wrapper for `::fz_append_int16_le()`. */
FZ_FUNCTION void ll_fz_append_int16_le(::fz_buffer *buf, int x);

/** Low-level wrapper for `::fz_append_int32_be()`. */
FZ_FUNCTION void ll_fz_append_int32_be(::fz_buffer *buf, int x);

/** Low-level wrapper for `::fz_append_int32_le()`. */
FZ_FUNCTION void ll_fz_append_int32_le(::fz_buffer *buf, int x);

/** Low-level wrapper for `::fz_append_pdf_string()`. */
/**
	fz_append_pdf_string: Append a string with PDF syntax quotes and
	escapes.

	The buffer will automatically grow as required.
*/
FZ_FUNCTION void ll_fz_append_pdf_string(::fz_buffer *buffer, const char *text);

/** Low-level wrapper for `::fz_append_pixmap_as_data_uri()`. */
FZ_FUNCTION void ll_fz_append_pixmap_as_data_uri(::fz_buffer *out, ::fz_pixmap *pixmap);

/** Low-level wrapper for `::fz_append_rune()`. */
FZ_FUNCTION void ll_fz_append_rune(::fz_buffer *buf, int c);

/** Low-level wrapper for `::fz_append_string()`. */
FZ_FUNCTION void ll_fz_append_string(::fz_buffer *buf, const char *data);

/** Low-level wrapper for `::fz_append_vprintf()`. */
/**
	fz_append_vprintf: Format and append data to buffer using
	printf-like formatting with varargs (see fz_vsnprintf).
*/
FZ_FUNCTION void ll_fz_append_vprintf(::fz_buffer *buffer, const char *fmt, va_list args);

/** Low-level wrapper for `::fz_arc4_encrypt()`. */
/**
	RC4 block encrypt operation; encrypt src into dst (both of
	length len) updating the RC4 state as we go.

	Never throws an exception.
*/
FZ_FUNCTION void ll_fz_arc4_encrypt(::fz_arc4 *state, unsigned char *dest, const unsigned char *src, size_t len);

/** Low-level wrapper for `::fz_arc4_final()`. */
/**
	RC4 finalization. Zero the context.

	Never throws an exception.
*/
FZ_FUNCTION void ll_fz_arc4_final(::fz_arc4 *state);

/** Low-level wrapper for `::fz_arc4_init()`. */
/**
	RC4 initialization. Begins an RC4 operation, writing a new
	context.

	Never throws an exception.
*/
FZ_FUNCTION void ll_fz_arc4_init(::fz_arc4 *state, const unsigned char *key, size_t len);

/** Low-level wrapper for `::fz_archive_format()`. */
/**
	Return a pointer to a string describing the format of the
	archive.

	The lifetime of the string is unspecified (in current
	implementations the string will persist until the archive
	is closed, but this is not guaranteed).
*/
FZ_FUNCTION const char *ll_fz_archive_format(::fz_archive *arch);

/** Low-level wrapper for `::fz_atof()`. */
/**
	Range checking atof
*/
FZ_FUNCTION float ll_fz_atof(const char *s);

/** Low-level wrapper for `::fz_atoi()`. */
/**
	atoi that copes with NULL
*/
FZ_FUNCTION int ll_fz_atoi(const char *s);

/** Low-level wrapper for `::fz_atoi64()`. */
/**
	64bit atoi that copes with NULL
*/
FZ_FUNCTION int64_t ll_fz_atoi64(const char *s);

/** Low-level wrapper for `::fz_authenticate_password()`. */
/**
	Test if the given password can decrypt the document.

	password: The password string to be checked. Some document
	specifications do not specify any particular text encoding, so
	neither do we.

	Returns 0 for failure to authenticate, non-zero for success.

	For PDF documents, further information can be given by examining
	the bits in the return code.

		Bit 0 => No password required
		Bit 1 => User password authenticated
		Bit 2 => Owner password authenticated
*/
FZ_FUNCTION int ll_fz_authenticate_password(::fz_document *doc, const char *password);

/** Low-level wrapper for `::fz_available()`. */
/**
	Ask how many bytes are available immediately from
	a given stream.

	stm: The stream to read from.

	max: A hint for the underlying stream; the maximum number of
	bytes that we are sure we will want to read. If you do not know
	this number, give 1.

	Returns the number of bytes immediately available between the
	read and write pointers. This number is guaranteed only to be 0
	if we have hit EOF. The number of bytes returned here need have
	no relation to max (could be larger, could be smaller).
*/
FZ_FUNCTION size_t ll_fz_available(::fz_stream *stm, size_t max);

/** Low-level wrapper for `::fz_base_colorspace()`. */
/**
	Get the 'base' colorspace for a colorspace.

	For indexed colorspaces, this is the colorspace the index
	decodes into. For all other colorspaces, it is the colorspace
	itself.

	The returned colorspace is 'borrowed' (i.e. no additional
	references are taken or dropped).
*/
FZ_FUNCTION ::fz_colorspace *ll_fz_base_colorspace(::fz_colorspace *cs);

/** Low-level wrapper for `::fz_basename()`. */
/**
	Find the filename component in a path.
*/
FZ_FUNCTION const char *ll_fz_basename(const char *path);

/** Low-level wrapper for `::fz_begin_group()`. */
FZ_FUNCTION void ll_fz_begin_group(::fz_device *dev, ::fz_rect area, ::fz_colorspace *cs, int isolated, int knockout, int blendmode, float alpha);

/** Low-level wrapper for `::fz_begin_layer()`. */
FZ_FUNCTION void ll_fz_begin_layer(::fz_device *dev, const char *layer_name);

/** Low-level wrapper for `::fz_begin_mask()`. */
FZ_FUNCTION void ll_fz_begin_mask(::fz_device *dev, ::fz_rect area, int luminosity, ::fz_colorspace *colorspace, const float *bc, ::fz_color_params color_params);

/** Low-level wrapper for `::fz_begin_metatext()`. */
FZ_FUNCTION void ll_fz_begin_metatext(::fz_device *dev, ::fz_metatext meta, const char *text);

/** Low-level wrapper for `::fz_begin_page()`. */
/**
	Called to start the process of writing a page to
	a document.

	mediabox: page size rectangle in points.

	Returns a borrowed fz_device to write page contents to. This
	should be kept if required, and only dropped if it was kept.
*/
FZ_FUNCTION ::fz_device *ll_fz_begin_page(::fz_document_writer *wri, ::fz_rect mediabox);

/** Low-level wrapper for `::fz_begin_structure()`. */
FZ_FUNCTION void ll_fz_begin_structure(::fz_device *dev, ::fz_structure standard, const char *raw, int idx);

/** Low-level wrapper for `::fz_begin_tile()`. */
FZ_FUNCTION void ll_fz_begin_tile(::fz_device *dev, ::fz_rect area, ::fz_rect view, float xstep, float ystep, ::fz_matrix ctm);

/** Low-level wrapper for `::fz_begin_tile_id()`. */
FZ_FUNCTION int ll_fz_begin_tile_id(::fz_device *dev, ::fz_rect area, ::fz_rect view, float xstep, float ystep, ::fz_matrix ctm, int id);

/** Low-level wrapper for `::fz_bidi_fragment_text()`.

This function has out-params. Python/C# wrappers look like:
	`ll_fz_bidi_fragment_text(const uint32_t *text, size_t textlen, ::fz_bidi_fragment_fn *callback, void *arg, int flags)` => ::fz_bidi_direction baseDir
*/
/**
	Partitions the given Unicode sequence into one or more
	unidirectional fragments and invokes the given callback
	function for each fragment.

	For example, if directionality of text is:
			0123456789
			rrlllrrrrr,
	we'll invoke callback with:
			&text[0], length == 2
			&text[2], length == 3
			&text[5], length == 5

	@param[in] text	start of Unicode sequence
	@param[in] textlen   number of Unicodes to analyse
	@param[in] baseDir   direction of paragraph (specify FZ_BIDI_NEUTRAL to force auto-detection)
	@param[in] callback  function to be called for each fragment
	@param[in] arg	data to be passed to the callback function
	@param[in] flags     flags to control operation (see fz_bidi_flags above)
*/
FZ_FUNCTION void ll_fz_bidi_fragment_text(const uint32_t *text, size_t textlen, ::fz_bidi_direction *baseDir, ::fz_bidi_fragment_fn *callback, void *arg, int flags);

/** Low-level wrapper for `::fz_bitmap_details()`.

This function has out-params. Python/C# wrappers look like:
	`ll_fz_bitmap_details(::fz_bitmap *bitmap)` => `(int w, int h, int n, int stride)`
*/
/**
	Retrieve details of a given bitmap.

	bitmap: The bitmap to query.

	w: Pointer to storage to retrieve width (or NULL).

	h: Pointer to storage to retrieve height (or NULL).

	n: Pointer to storage to retrieve number of color components (or
	NULL).

	stride: Pointer to storage to retrieve bitmap stride (or NULL).
*/
FZ_FUNCTION void ll_fz_bitmap_details(::fz_bitmap *bitmap, int *w, int *h, int *n, int *stride);

/** Low-level wrapper for `::fz_blendmode_name()`. */
/**
	Map from enumeration to blend mode string.

	The string is static, with arbitrary lifespan.
*/
FZ_FUNCTION const char *ll_fz_blendmode_name(int blendmode);

/** Low-level wrapper for `::fz_bound_display_list()`. */
/**
	Return the bounding box of the page recorded in a display list.
*/
FZ_FUNCTION ::fz_rect ll_fz_bound_display_list(::fz_display_list *list);

/** Low-level wrapper for `::fz_bound_glyph()`. */
/**
	Return a bbox for a given glyph in a font.

	font: The font to look for the glyph in.

	gid: The glyph to bound.

	trm: The matrix to apply to the glyph before bounding.

	Returns rectangle by value containing the bounds of the given
	glyph.
*/
FZ_FUNCTION ::fz_rect ll_fz_bound_glyph(::fz_font *font, int gid, ::fz_matrix trm);

/** Low-level wrapper for `::fz_bound_page()`. */
/**
	Determine the size of a page at 72 dpi.
*/
FZ_FUNCTION ::fz_rect ll_fz_bound_page(::fz_page *page);

/** Low-level wrapper for `::fz_bound_page_box()`. */
FZ_FUNCTION ::fz_rect ll_fz_bound_page_box(::fz_page *page, ::fz_box_type box);

/** Low-level wrapper for `::fz_bound_path()`. */
/**
	Return a bounding rectangle for a path.

	path: The path to bound.

	stroke: If NULL, the bounding rectangle given is for
	the filled path. If non-NULL the bounding rectangle
	given is for the path stroked with the given attributes.

	ctm: The matrix to apply to the path during stroking.

	r: Pointer to a fz_rect which will be used to hold
	the result.

	Returns r, updated to contain the bounding rectangle.
*/
FZ_FUNCTION ::fz_rect ll_fz_bound_path(const ::fz_path *path, const ::fz_stroke_state *stroke, ::fz_matrix ctm);

/** Low-level wrapper for `::fz_bound_shade()`. */
/**
	Bound a given shading.

	shade: The shade to bound.

	ctm: The transform to apply to the shade before bounding.

	r: Pointer to storage to put the bounds in.

	Returns r, updated to contain the bounds for the shading.
*/
FZ_FUNCTION ::fz_rect ll_fz_bound_shade(::fz_shade *shade, ::fz_matrix ctm);

/** Low-level wrapper for `::fz_bound_text()`. */
/**
	Find the bounds of a given text object.

	text: The text object to find the bounds of.

	stroke: Pointer to the stroke attributes (for stroked
	text), or NULL (for filled text).

	ctm: The matrix in use.

	r: pointer to storage for the bounds.

	Returns a pointer to r, which is updated to contain the
	bounding box for the text object.
*/
FZ_FUNCTION ::fz_rect ll_fz_bound_text(const ::fz_text *text, const ::fz_stroke_state *stroke, ::fz_matrix ctm);

/** Low-level wrapper for `::fz_box_type_from_string()`. */
FZ_FUNCTION ::fz_box_type ll_fz_box_type_from_string(const char *name);

/** Low-level wrapper for `::fz_buffer_extract()`.

This function has out-params. Python/C# wrappers look like:
	`ll_fz_buffer_extract(::fz_buffer *buf)` => `(size_t, unsigned char *data)`
*/
/**
	Take ownership of buffer contents.

	Performs the same task as fz_buffer_storage, but ownership of
	the data buffer returns with this call. The buffer is left
	empty.

	Note: Bad things may happen if this is called on a buffer with
	multiple references that is being used from multiple threads.

	data: Pointer to place to retrieve data pointer.

	Returns length of stream.
*/
FZ_FUNCTION size_t ll_fz_buffer_extract(::fz_buffer *buf, unsigned char **data);

/** Low-level wrapper for `::fz_buffer_storage()`.

This function has out-params. Python/C# wrappers look like:
	`ll_fz_buffer_storage(::fz_buffer *buf)` => `(size_t, unsigned char *datap)`
*/
/**
	Retrieve internal memory of buffer.

	datap: Output parameter that will be pointed to the data.

	Returns the current size of the data in bytes.
*/
FZ_FUNCTION size_t ll_fz_buffer_storage(::fz_buffer *buf, unsigned char **datap);

/** Low-level wrapper for `::fz_calloc()`. */
/**
	Allocate array of memory of count entries of size bytes.
	Clears the memory to zero.

	Throws exception in the event of failure to allocate.
*/
FZ_FUNCTION void *ll_fz_calloc(size_t count, size_t size);

/** Low-level wrapper for `::fz_calloc_no_throw()`. */
/**
	fz_calloc equivalent that returns NULL rather than throwing
	exceptions.
*/
FZ_FUNCTION void *ll_fz_calloc_no_throw(size_t count, size_t size);

/** Low-level wrapper for `::fz_caught()`. */
/**
	Within an fz_catch() block, retrieve the error code for
	the current exception.

	This assumes no intervening use of fz_try/fz_catch.
*/
FZ_FUNCTION int ll_fz_caught();

/** Low-level wrapper for `::fz_caught_errno()`. */
FZ_FUNCTION int ll_fz_caught_errno();

/** Low-level wrapper for `::fz_caught_message()`. */
/**
	Within an fz_catch() block, retrieve the formatted message
	string for the current exception.

	This assumes no intervening use of fz_try/fz_catch.
*/
FZ_FUNCTION const char *ll_fz_caught_message();

/** Low-level wrapper for `::fz_chartorune()`.

This function has out-params. Python/C# wrappers look like:
	`ll_fz_chartorune(const char *str)` => `(int, int rune)`
*/
/**
	UTF8 decode a single rune from a sequence of chars.

	rune: Pointer to an int to assign the decoded 'rune' to.

	str: Pointer to a UTF8 encoded string.

	Returns the number of bytes consumed.
*/
FZ_FUNCTION int ll_fz_chartorune(int *rune, const char *str);

/** Low-level wrapper for `::fz_clamp()`. */
FZ_FUNCTION float ll_fz_clamp(float x, float min, float max);

/** Low-level wrapper for `::fz_clamp64()`. */
FZ_FUNCTION int64_t ll_fz_clamp64(int64_t x, int64_t min, int64_t max);

/** Low-level wrapper for `::fz_clamp_color()`.

This function has out-params. Python/C# wrappers look like:
	`ll_fz_clamp_color(::fz_colorspace *cs, const float *in)` => float out
*/
/**
	Clamp the samples in a color to the correct ranges for a
	given colorspace.
*/
FZ_FUNCTION void ll_fz_clamp_color(::fz_colorspace *cs, const float *in, float *out);

/** Low-level wrapper for `::fz_clamp_location()`. */
/**
	Clamps a location into valid chapter/page range. (First clamps
	the chapter into range, then the page into range).
*/
FZ_FUNCTION ::fz_location ll_fz_clamp_location(::fz_document *doc, ::fz_location loc);

/** Low-level wrapper for `::fz_clampd()`. */
FZ_FUNCTION double ll_fz_clampd(double x, double min, double max);

/** Low-level wrapper for `::fz_clampi()`. */
FZ_FUNCTION int ll_fz_clampi(int x, int min, int max);

/** Low-level wrapper for `::fz_clampp()`. */
FZ_FUNCTION void *ll_fz_clampp(void *x, void *min, void *max);

/** Low-level wrapper for `::fz_cleanname()`. */
/**
	rewrite path to the shortest string that names the same path.

	Eliminates multiple and trailing slashes, interprets "." and
	"..". Overwrites the string in place.
*/
FZ_FUNCTION char *ll_fz_cleanname(char *name);

/** Low-level wrapper for `::fz_cleanname_strdup()`. */
/**
	rewrite path to the shortest string that names the same path.

	Eliminates multiple and trailing slashes, interprets "." and
	"..". Allocates a new string that the caller must free.
*/
FZ_FUNCTION char *ll_fz_cleanname_strdup(const char *name);

/** Low-level wrapper for `::fz_clear_bitmap()`. */
/**
	Set the entire bitmap to 0.

	Never throws exceptions.
*/
FZ_FUNCTION void ll_fz_clear_bitmap(::fz_bitmap *bit);

/** Low-level wrapper for `::fz_clear_buffer()`. */
/**
	Empties the buffer. Storage is not freed, but is held ready
	to be reused as the buffer is refilled.

	Never throws exceptions.
*/
FZ_FUNCTION void ll_fz_clear_buffer(::fz_buffer *buf);

/** Low-level wrapper for `::fz_clear_pixmap()`. */
/**
	Sets all components (including alpha) of
	all pixels in a pixmap to 0.

	pix: The pixmap to clear.
*/
FZ_FUNCTION void ll_fz_clear_pixmap(::fz_pixmap *pix);

/** Low-level wrapper for `::fz_clear_pixmap_rect_with_value()`. */
/**
	Clears a subrect of a pixmap with the given value.

	pix: The pixmap to clear.

	value: Values in the range 0 to 255 are valid. Each component
	sample for each pixel in the pixmap will be set to this value,
	while alpha will always be set to 255 (non-transparent).

	r: the rectangle.
*/
FZ_FUNCTION void ll_fz_clear_pixmap_rect_with_value(::fz_pixmap *pix, int value, ::fz_irect r);

/** Low-level wrapper for `::fz_clear_pixmap_with_value()`. */
/**
	Clears a pixmap with the given value.

	pix: The pixmap to clear.

	value: Values in the range 0 to 255 are valid. Each component
	sample for each pixel in the pixmap will be set to this value,
	while alpha will always be set to 255 (non-transparent).

	This function is horrible, and should be removed from the
	API and replaced with a less magic one.
*/
FZ_FUNCTION void ll_fz_clear_pixmap_with_value(::fz_pixmap *pix, int value);

/** Low-level wrapper for `::fz_clip_image_mask()`. */
FZ_FUNCTION void ll_fz_clip_image_mask(::fz_device *dev, ::fz_image *image, ::fz_matrix ctm, ::fz_rect scissor);

/** Low-level wrapper for `::fz_clip_path()`. */
FZ_FUNCTION void ll_fz_clip_path(::fz_device *dev, const ::fz_path *path, int even_odd, ::fz_matrix ctm, ::fz_rect scissor);

/** Low-level wrapper for `::fz_clip_stroke_path()`. */
FZ_FUNCTION void ll_fz_clip_stroke_path(::fz_device *dev, const ::fz_path *path, const ::fz_stroke_state *stroke, ::fz_matrix ctm, ::fz_rect scissor);

/** Low-level wrapper for `::fz_clip_stroke_text()`. */
FZ_FUNCTION void ll_fz_clip_stroke_text(::fz_device *dev, const ::fz_text *text, const ::fz_stroke_state *stroke, ::fz_matrix ctm, ::fz_rect scissor);

/** Low-level wrapper for `::fz_clip_text()`. */
FZ_FUNCTION void ll_fz_clip_text(::fz_device *dev, const ::fz_text *text, ::fz_matrix ctm, ::fz_rect scissor);

/** Low-level wrapper for `::fz_clone_buffer()`. */
/**
	Make a new buffer, containing a copy of the data used in
	the original.
*/
FZ_FUNCTION ::fz_buffer *ll_fz_clone_buffer(::fz_buffer *buf);

/** Low-level wrapper for `::fz_clone_context()`. */
/**
	Make a clone of an existing context.

	This function is meant to be used in multi-threaded
	applications where each thread requires its own context, yet
	parts of the global state, for example caching, are shared.

	ctx: Context obtained from fz_new_context to make a copy of.
	ctx must have had locks and lock/functions setup when created.
	The two contexts will share the memory allocator, resource
	store, locks and lock/unlock functions. They will each have
	their own exception stacks though.

	May return NULL.
*/
FZ_FUNCTION ::fz_context *ll_fz_clone_context();

/** Low-level wrapper for `::fz_clone_default_colorspaces()`. */
/**
	Returns a reference to a newly cloned default colorspaces
	structure.

	The new clone may safely be altered without fear of race
	conditions as the caller is the only reference holder.
*/
FZ_FUNCTION ::fz_default_colorspaces *ll_fz_clone_default_colorspaces(::fz_default_colorspaces *base);

/** Low-level wrapper for `::fz_clone_path()`. */
/**
	Clone the data for a path.

	This is used in preference to fz_keep_path when a whole
	new copy of a path is required, rather than just a shared
	pointer. This probably indicates that the path is about to
	be modified.

	path: path to clone.

	Throws exceptions on failure to allocate.
*/
FZ_FUNCTION ::fz_path *ll_fz_clone_path(::fz_path *path);

/** Low-level wrapper for `::fz_clone_pixmap()`. */
/**
	Clone a pixmap, copying the pixels and associated data to new
	storage.

	The reference count of 'old' is unchanged.
*/
FZ_FUNCTION ::fz_pixmap *ll_fz_clone_pixmap(const ::fz_pixmap *old);

/** Low-level wrapper for `::fz_clone_pixmap_area_with_different_seps()`. */
FZ_FUNCTION ::fz_pixmap *ll_fz_clone_pixmap_area_with_different_seps(::fz_pixmap *src, const ::fz_irect *bbox, ::fz_colorspace *dcs, ::fz_separations *seps, ::fz_color_params color_params, ::fz_default_colorspaces *default_cs);

/** Low-level wrapper for `::fz_clone_separations_for_overprint()`. */
/**
	Return a separations object with all the spots in the input
	separations object that are set to composite, reset to be
	enabled. If there ARE no spots in the object, this returns
	NULL. If the object already has all its spots enabled, then
	just returns another handle on the same object.
*/
FZ_FUNCTION ::fz_separations *ll_fz_clone_separations_for_overprint(::fz_separations *seps);

/** Low-level wrapper for `::fz_clone_stroke_state()`. */
/**
	Create an identical stroke_state structure and return a
	reference to it.

	stroke: The stroke state reference to clone.

	Exceptions may be thrown in the event of a failure to
	allocate.
*/
FZ_FUNCTION ::fz_stroke_state *ll_fz_clone_stroke_state(::fz_stroke_state *stroke);

/** Low-level wrapper for `::fz_close_band_writer()`. */
/**
	Finishes up the output and closes the band writer. After this
	call no more headers or bands may be written.
*/
FZ_FUNCTION void ll_fz_close_band_writer(::fz_band_writer *writer);

/** Low-level wrapper for `::fz_close_device()`. */
/**
	Signal the end of input, and flush any buffered output.
	This is NOT called implicitly on fz_drop_device. This
	may throw exceptions.
*/
FZ_FUNCTION void ll_fz_close_device(::fz_device *dev);

/** Low-level wrapper for `::fz_close_document_writer()`. */
/**
	Called to end the process of writing
	pages to a document.

	This writes any file level trailers required. After this
	completes successfully the file is up to date and complete.
*/
FZ_FUNCTION void ll_fz_close_document_writer(::fz_document_writer *wri);

/** Low-level wrapper for `::fz_close_output()`. */
/**
	Flush pending output and close an output stream.
*/
FZ_FUNCTION void ll_fz_close_output(::fz_output *arg_1);

/** Low-level wrapper for `::fz_close_zip_writer()`. */
/**
	Close the zip file for writing.

	This flushes any pending data to the file. This can throw
	exceptions.
*/
FZ_FUNCTION void ll_fz_close_zip_writer(::fz_zip_writer *zip);

/** Low-level wrapper for `::fz_closepath()`. */
/**
	Close the current subpath.

	path: The path to modify.

	Throws exceptions on failure to allocate, attempting to modify
	a packed path, and illegal path closes (i.e. closing a non open
	path).
*/
FZ_FUNCTION void ll_fz_closepath(::fz_path *path);

/** Low-level wrapper for `::fz_colorspace_colorant()`. */
/**
	Retrieve a the name for a colorant.

	Returns a pointer with the same lifespan as the colorspace.
*/
FZ_FUNCTION const char *ll_fz_colorspace_colorant(::fz_colorspace *cs, int n);

/** Low-level wrapper for `::fz_colorspace_device_n_has_cmyk()`. */
/**
	True if DeviceN color space has cyan magenta yellow or black as
	one of its colorants.
*/
FZ_FUNCTION int ll_fz_colorspace_device_n_has_cmyk(::fz_colorspace *cs);

/** Low-level wrapper for `::fz_colorspace_device_n_has_only_cmyk()`. */
/**
	True if DeviceN color space has only colorants from the CMYK set.
*/
FZ_FUNCTION int ll_fz_colorspace_device_n_has_only_cmyk(::fz_colorspace *cs);

/** Low-level wrapper for `::fz_colorspace_is_cmyk()`. */
FZ_FUNCTION int ll_fz_colorspace_is_cmyk(::fz_colorspace *cs);

/** Low-level wrapper for `::fz_colorspace_is_device()`. */
FZ_FUNCTION int ll_fz_colorspace_is_device(::fz_colorspace *cs);

/** Low-level wrapper for `::fz_colorspace_is_device_cmyk()`. */
FZ_FUNCTION int ll_fz_colorspace_is_device_cmyk(::fz_colorspace *cs);

/** Low-level wrapper for `::fz_colorspace_is_device_gray()`. */
FZ_FUNCTION int ll_fz_colorspace_is_device_gray(::fz_colorspace *cs);

/** Low-level wrapper for `::fz_colorspace_is_device_n()`. */
FZ_FUNCTION int ll_fz_colorspace_is_device_n(::fz_colorspace *cs);

/** Low-level wrapper for `::fz_colorspace_is_gray()`. */
/**
	Tests for particular types of colorspaces
*/
FZ_FUNCTION int ll_fz_colorspace_is_gray(::fz_colorspace *cs);

/** Low-level wrapper for `::fz_colorspace_is_indexed()`. */
FZ_FUNCTION int ll_fz_colorspace_is_indexed(::fz_colorspace *cs);

/** Low-level wrapper for `::fz_colorspace_is_lab()`. */
FZ_FUNCTION int ll_fz_colorspace_is_lab(::fz_colorspace *cs);

/** Low-level wrapper for `::fz_colorspace_is_lab_icc()`. */
FZ_FUNCTION int ll_fz_colorspace_is_lab_icc(::fz_colorspace *cs);

/** Low-level wrapper for `::fz_colorspace_is_rgb()`. */
FZ_FUNCTION int ll_fz_colorspace_is_rgb(::fz_colorspace *cs);

/** Low-level wrapper for `::fz_colorspace_is_subtractive()`. */
/**
	True for CMYK, Separation and DeviceN colorspaces.
*/
FZ_FUNCTION int ll_fz_colorspace_is_subtractive(::fz_colorspace *cs);

/** Low-level wrapper for `::fz_colorspace_n()`. */
/**
	Query the number of colorants in a colorspace.
*/
FZ_FUNCTION int ll_fz_colorspace_n(::fz_colorspace *cs);

/** Low-level wrapper for `::fz_colorspace_name()`. */
/**
	Query the name of a colorspace.

	The returned string has the same lifespan as the colorspace
	does. Caller should not free it.
*/
FZ_FUNCTION const char *ll_fz_colorspace_name(::fz_colorspace *cs);

/** Low-level wrapper for `::fz_colorspace_name_colorant()`. */
/**
	Assign a name for a given colorant in a colorspace.

	Used while initially setting up a colorspace. The string is
	copied into local storage, so need not be retained by the
	caller.
*/
FZ_FUNCTION void ll_fz_colorspace_name_colorant(::fz_colorspace *cs, int n, const char *name);

/** Low-level wrapper for `::fz_colorspace_type()`. */
/**
	Query the type of colorspace.
*/
FZ_FUNCTION enum fz_colorspace_type ll_fz_colorspace_type(::fz_colorspace *cs);

/** Low-level wrapper for `::fz_compare_separations()`. */
/**
	Compare 2 separations structures (or NULLs).

	Return 0 if identical, non-zero if not identical.
*/
FZ_FUNCTION int ll_fz_compare_separations(const ::fz_separations *sep1, const ::fz_separations *sep2);

/** Low-level wrapper for `::fz_compress_ccitt_fax_g3()`. */
/**
	Compress bitmap data as CCITT Group 3 1D fax image.
	Creates a stream assuming the default PDF parameters,
	except the number of columns.
*/
FZ_FUNCTION ::fz_buffer *ll_fz_compress_ccitt_fax_g3(const unsigned char *data, int columns, int rows, ptrdiff_t stride);

/** Low-level wrapper for `::fz_compress_ccitt_fax_g4()`. */
/**
	Compress bitmap data as CCITT Group 4 2D fax image.
	Creates a stream assuming the default PDF parameters, except
	K=-1 and the number of columns.
*/
FZ_FUNCTION ::fz_buffer *ll_fz_compress_ccitt_fax_g4(const unsigned char *data, int columns, int rows, ptrdiff_t stride);

/** Low-level wrapper for `::fz_compressed_buffer_size()`. */
/**
	Return the storage size used for a buffer and its data.
	Used in implementing store handling.

	Never throws exceptions.
*/
FZ_FUNCTION size_t ll_fz_compressed_buffer_size(::fz_compressed_buffer *buffer);

/** Low-level wrapper for `::fz_compressed_image_buffer()`. */
/**
	Retrieve the underlying compressed data for an image.

	Returns a pointer to the underlying data buffer for an image,
	or NULL if this image is not based upon a compressed data
	buffer.

	This is not a reference counted structure, so no reference is
	returned. Lifespan is limited to that of the image itself.
*/
FZ_FUNCTION ::fz_compressed_buffer *ll_fz_compressed_image_buffer(::fz_image *image);

/** Low-level wrapper for `::fz_compressed_image_type()`. */
/**
	Return the type of a compressed image.

	Any non-compressed image will have the type returned as UNKNOWN.
*/
FZ_FUNCTION int ll_fz_compressed_image_type(::fz_image *image);

/** Low-level wrapper for `::fz_concat()`. */
/**
	Multiply two matrices.

	The order of the two matrices are important since matrix
	multiplication is not commutative.

	Returns result.
*/
FZ_FUNCTION ::fz_matrix ll_fz_concat(::fz_matrix left, ::fz_matrix right);

/** Low-level wrapper for `::fz_concat_push_drop()`. */
/**
	Add a chained stream to the end of the concatenate filter.

	Ownership of chain is passed in.
*/
FZ_FUNCTION void ll_fz_concat_push_drop(::fz_stream *concat, ::fz_stream *chain);

/** Low-level wrapper for `::fz_contains_rect()`. */
/**
	Test rectangle inclusion.

	Return true if a entirely contains b.
*/
FZ_FUNCTION int ll_fz_contains_rect(::fz_rect a, ::fz_rect b);

/** Low-level wrapper for `::fz_convert_color()`.

This function has out-params. Python/C# wrappers look like:
	`ll_fz_convert_color(::fz_colorspace *ss, const float *sv, ::fz_colorspace *ds, ::fz_colorspace *is, ::fz_color_params params)` => float dv
*/
/**
	Convert color values sv from colorspace ss into colorvalues dv
	for colorspace ds, via an optional intervening space is,
	respecting the given color_params.
*/
FZ_FUNCTION void ll_fz_convert_color(::fz_colorspace *ss, const float *sv, ::fz_colorspace *ds, float *dv, ::fz_colorspace *is, ::fz_color_params params);

/** Low-level wrapper for `::fz_convert_error()`.

This function has out-params. Python/C# wrappers look like:
	`ll_fz_convert_error()` => `(const char *, int code)`
*/
FZ_FUNCTION const char *ll_fz_convert_error(int *code);

/** Low-level wrapper for `::fz_convert_indexed_pixmap_to_base()`. */
/**
	Convert pixmap from indexed to base colorspace.

	This creates a new bitmap containing the converted pixmap data.
 */
FZ_FUNCTION ::fz_pixmap *ll_fz_convert_indexed_pixmap_to_base(const ::fz_pixmap *src);

/** Low-level wrapper for `::fz_convert_pixmap()`. */
/**
	Convert an existing pixmap to a desired
	colorspace. Other properties of the pixmap, such as resolution
	and position are copied to the converted pixmap.

	pix: The pixmap to convert.

	default_cs: If NULL pix->colorspace is used. It is possible that
	the data may need to be interpreted as one of the color spaces
	in default_cs.

	cs_des: Desired colorspace, may be NULL to denote alpha-only.

	prf: Proofing color space through which we need to convert.

	color_params: Parameters that may be used in conversion (e.g.
	ri).

	keep_alpha: If 0 any alpha component is removed, otherwise
	alpha is kept if present in the pixmap.
*/
FZ_FUNCTION ::fz_pixmap *ll_fz_convert_pixmap(const ::fz_pixmap *pix, ::fz_colorspace *cs_des, ::fz_colorspace *prf, ::fz_default_colorspaces *default_cs, ::fz_color_params color_params, int keep_alpha);

/** Low-level wrapper for `::fz_convert_separation_colors()`.

This function has out-params. Python/C# wrappers look like:
	`ll_fz_convert_separation_colors(::fz_colorspace *src_cs, const float *src_color, ::fz_separations *dst_seps, ::fz_colorspace *dst_cs, ::fz_color_params color_params)` => float dst_color
*/
/**
	Convert a color given in terms of one colorspace,
	to a color in terms of another colorspace/separations.
*/
FZ_FUNCTION void ll_fz_convert_separation_colors(::fz_colorspace *src_cs, const float *src_color, ::fz_separations *dst_seps, ::fz_colorspace *dst_cs, float *dst_color, ::fz_color_params color_params);

/** Low-level wrapper for `::fz_convert_separation_pixmap_to_base()`. */
/**
	Convert pixmap from DeviceN/Separation to base colorspace.

	This creates a new bitmap containing the converted pixmap data.
*/
FZ_FUNCTION ::fz_pixmap *ll_fz_convert_separation_pixmap_to_base(const ::fz_pixmap *src);

/** Low-level wrapper for `::fz_copy_option()`. */
/**
	Copy an option (val) into a destination buffer (dest), of maxlen
	bytes.

	Returns the number of bytes (including terminator) that did not
	fit. If val is maxlen or greater bytes in size, it will be left
	unterminated.
*/
FZ_FUNCTION size_t ll_fz_copy_option(const char *val, char *dest, size_t maxlen);

/** Low-level wrapper for `::fz_copy_pixmap_rect()`. */
FZ_FUNCTION void ll_fz_copy_pixmap_rect(::fz_pixmap *dest, ::fz_pixmap *src, ::fz_irect r, const ::fz_default_colorspaces *default_cs);

/** Low-level wrapper for `::fz_copy_rectangle()`. */
/**
	Return a newly allocated UTF-8 string with the text for a given
	selection rectangle.

	crlf: If true, write "\r\n" style line endings (otherwise "\n"
	only).
*/
FZ_FUNCTION char *ll_fz_copy_rectangle(::fz_stext_page *page, ::fz_rect area, int crlf);

/** Low-level wrapper for `::fz_copy_selection()`. */
/**
	Return a newly allocated UTF-8 string with the text for a given
	selection.

	crlf: If true, write "\r\n" style line endings (otherwise "\n"
	only).
*/
FZ_FUNCTION char *ll_fz_copy_selection(::fz_stext_page *page, ::fz_point a, ::fz_point b, int crlf);

/** Low-level wrapper for `::fz_count_active_separations()`. */
/**
	Return the number of active separations.
*/
FZ_FUNCTION int ll_fz_count_active_separations(const ::fz_separations *seps);

/** Low-level wrapper for `::fz_count_archive_entries()`. */
/**
	Number of entries in archive.

	Will always return a value >= 0.

	May throw an exception if this type of archive cannot count the
	entries (such as a directory).
*/
FZ_FUNCTION int ll_fz_count_archive_entries(::fz_archive *arch);

/** Low-level wrapper for `::fz_count_chapter_pages()`. */
/**
	Return the number of pages in a chapter.
	May return 0.
*/
FZ_FUNCTION int ll_fz_count_chapter_pages(::fz_document *doc, int chapter);

/** Low-level wrapper for `::fz_count_chapters()`. */
/**
	Return the number of chapters in the document.
	At least 1.
*/
FZ_FUNCTION int ll_fz_count_chapters(::fz_document *doc);

/** Low-level wrapper for `::fz_count_pages()`. */
/**
	Return the number of pages in document

	May return 0 for documents with no pages.
*/
FZ_FUNCTION int ll_fz_count_pages(::fz_document *doc);

/** Low-level wrapper for `::fz_count_separations()`. */
FZ_FUNCTION int ll_fz_count_separations(const ::fz_separations *sep);

/** Low-level wrapper for `::fz_create_link()`. */
/**
	Create a new link on a page.
*/
FZ_FUNCTION ::fz_link *ll_fz_create_link(::fz_page *page, ::fz_rect bbox, const char *uri);

/** Low-level wrapper for `::fz_currentpoint()`. */
/**
	Return the current point that a path has
	reached or (0,0) if empty.

	path: path to return the current point of.
*/
FZ_FUNCTION ::fz_point ll_fz_currentpoint(::fz_path *path);

/** Low-level wrapper for `::fz_curveto()`. */
/**
	Append a 'curveto' command to an open path. (For a
	cubic bezier).

	path: The path to modify.

	x0, y0: The coordinates of the first control point for the
	curve.

	x1, y1: The coordinates of the second control point for the
	curve.

	x2, y2: The end coordinates for the curve.

	Throws exceptions on failure to allocate, or attempting to
	modify a packed path.
*/
FZ_FUNCTION void ll_fz_curveto(::fz_path *path, float x0, float y0, float x1, float y1, float x2, float y2);

/** Low-level wrapper for `::fz_curvetov()`. */
/**
	Append a 'curvetov' command to an open path. (For a
	cubic bezier with the first control coordinate equal to
	the start point).

	path: The path to modify.

	x1, y1: The coordinates of the second control point for the
	curve.

	x2, y2: The end coordinates for the curve.

	Throws exceptions on failure to allocate, or attempting to
	modify a packed path.
*/
FZ_FUNCTION void ll_fz_curvetov(::fz_path *path, float x1, float y1, float x2, float y2);

/** Low-level wrapper for `::fz_curvetoy()`. */
/**
	Append a 'curvetoy' command to an open path. (For a
	cubic bezier with the second control coordinate equal to
	the end point).

	path: The path to modify.

	x0, y0: The coordinates of the first control point for the
	curve.

	x2, y2: The end coordinates for the curve (and the second
	control coordinate).

	Throws exceptions on failure to allocate, or attempting to
	modify a packed path.
*/
FZ_FUNCTION void ll_fz_curvetoy(::fz_path *path, float x0, float y0, float x2, float y2);

/** Low-level wrapper for `::fz_debug_store()`. */
/**
	Output debugging information for the current state of the store
	to the given output channel.
*/
FZ_FUNCTION void ll_fz_debug_store(::fz_output *out);

/** Low-level wrapper for `::fz_debug_xml()`. */
/**
	Pretty-print an XML tree to stdout. (Deprecated, use
	fz_output_xml in preference).
*/
FZ_FUNCTION void ll_fz_debug_xml(::fz_xml *item, int level);

/** Low-level wrapper for `::fz_decode_tile()`. */
FZ_FUNCTION void ll_fz_decode_tile(::fz_pixmap *pix, const float *decode);

/** Low-level wrapper for `::fz_decode_uri()`. */
/**
 * Return a new string representing the unencoded version of the given URI.
 * This decodes all escape sequences except those that would result in a reserved
 * character that are part of the URI syntax (; / ? : @ & = + $ , #).
 */
FZ_FUNCTION char *ll_fz_decode_uri(const char *s);

/** Low-level wrapper for `::fz_decode_uri_component()`. */
/**
 * Return a new string representing the unencoded version of the given URI component.
 * This decodes all escape sequences!
 */
FZ_FUNCTION char *ll_fz_decode_uri_component(const char *s);

/** Low-level wrapper for `::fz_decomp_image_from_stream()`.

This function has out-params. Python/C# wrappers look like:
	`ll_fz_decomp_image_from_stream(::fz_stream *stm, ::fz_compressed_image *image, ::fz_irect *subarea, int indexed, int l2factor)` => `(fz_pixmap *, int l2extra)`
*/
/**
	Decode a subarea of a compressed image. l2factor is the amount
	of subsampling inbuilt to the stream (i.e. performed by the
	decoder). If non NULL, l2extra is the extra amount of
	subsampling that should be performed by this routine. This will
	be updated on exit to the amount of subsampling that is still
	required to be done.

	Returns a kept reference.
*/
FZ_FUNCTION ::fz_pixmap *ll_fz_decomp_image_from_stream(::fz_stream *stm, ::fz_compressed_image *image, ::fz_irect *subarea, int indexed, int l2factor, int *l2extra);

/** Low-level wrapper for `::fz_decouple_type3_font()`. */
FZ_FUNCTION void ll_fz_decouple_type3_font(::fz_font *font, void *t3doc);

/** Low-level wrapper for `::fz_default_cmyk()`. */
FZ_FUNCTION ::fz_colorspace *ll_fz_default_cmyk(const ::fz_default_colorspaces *default_cs);

/** Low-level wrapper for `::fz_default_error_callback()`. */
/**
	FIXME: Better not to expose fz_default_error_callback, and
	fz_default_warning callback and to allow 'NULL' to be used
	int fz_set_xxxx_callback to mean "defaults".

	FIXME: Do we need/want functions like
	fz_error_callback(ctx, message) to allow callers to inject
	stuff into the error/warning streams?
*/
/**
	The default error callback. Declared publicly just so that the
	error callback can be set back to this after it has been
	overridden.
*/
FZ_FUNCTION void ll_fz_default_error_callback(void *user, const char *message);

/** Low-level wrapper for `::fz_default_gray()`. */
/**
	Retrieve default colorspaces (typically page local).

	If default_cs is non NULL, the default is retrieved from there,
	otherwise the global default is retrieved.

	These return borrowed references that should not be dropped,
	unless they are kept first.
*/
FZ_FUNCTION ::fz_colorspace *ll_fz_default_gray(const ::fz_default_colorspaces *default_cs);

/** Low-level wrapper for `::fz_default_halftone()`. */
/**
	Create a 'default' halftone structure
	for the given number of components.

	num_comps: The number of components to use.

	Returns a simple default halftone. The default halftone uses
	the same halftone tile for each plane, which may not be ideal
	for all purposes.
*/
FZ_FUNCTION ::fz_halftone *ll_fz_default_halftone(int num_comps);

/** Low-level wrapper for `::fz_default_output_intent()`. */
FZ_FUNCTION ::fz_colorspace *ll_fz_default_output_intent(const ::fz_default_colorspaces *default_cs);

/** Low-level wrapper for `::fz_default_rgb()`. */
FZ_FUNCTION ::fz_colorspace *ll_fz_default_rgb(const ::fz_default_colorspaces *default_cs);

/** Low-level wrapper for `::fz_default_warning_callback()`. */
/**
	The default warning callback. Declared publicly just so that
	the warning callback can be set back to this after it has been
	overridden.
*/
FZ_FUNCTION void ll_fz_default_warning_callback(void *user, const char *message);

/** Low-level wrapper for `::fz_defer_reap_end()`. */
/**
	Decrement the defer reap count.

	If the defer reap count returns to 0, and the store
	has reapable objects in, a reap pass will begin.

	Call this at the end of a process during which you
	potentially might drop many reapable objects.

	It is vital that every fz_defer_reap_start is matched
	by a fz_defer_reap_end call.
*/
FZ_FUNCTION void ll_fz_defer_reap_end();

/** Low-level wrapper for `::fz_defer_reap_start()`. */
/**
	Increment the defer reap count.

	No reap operations will take place (except for those
	triggered by an immediate failed malloc) until the
	defer reap count returns to 0.

	Call this at the start of a process during which you
	potentially might drop many reapable objects.

	It is vital that every fz_defer_reap_start is matched
	by a fz_defer_reap_end call.
*/
FZ_FUNCTION void ll_fz_defer_reap_start();

/** Low-level wrapper for `::fz_deflate()`.

This function has out-params. Python/C# wrappers look like:
	`ll_fz_deflate(unsigned char *dest, const unsigned char *source, size_t source_length, ::fz_deflate_level level)` => size_t compressed_length
*/
/**
	Compress source_length bytes of data starting
	at source, into a buffer of length *destLen, starting at dest.
	*compressed_length will be updated on exit to contain the size
	actually used.
 */
FZ_FUNCTION void ll_fz_deflate(unsigned char *dest, size_t *compressed_length, const unsigned char *source, size_t source_length, ::fz_deflate_level level);

/** Low-level wrapper for `::fz_deflate_bound()`. */
/**
	Returns the upper bound on the
	size of flated data of length size.
 */
FZ_FUNCTION size_t ll_fz_deflate_bound(size_t size);

/** Low-level wrapper for `::fz_delete_link()`. */
/**
	Delete an existing link on a page.
*/
FZ_FUNCTION void ll_fz_delete_link(::fz_page *page, ::fz_link *link);

/** Low-level wrapper for `::fz_detach_xml()`. */
/**
	Detach a node from the tree, unlinking it from its parent,
	and setting the document root to the node.
*/
FZ_FUNCTION void ll_fz_detach_xml(::fz_xml *node);

/** Low-level wrapper for `::fz_device_bgr()`. */
FZ_FUNCTION ::fz_colorspace *ll_fz_device_bgr();

/** Low-level wrapper for `::fz_device_cmyk()`. */
FZ_FUNCTION ::fz_colorspace *ll_fz_device_cmyk();

/** Low-level wrapper for `::fz_device_current_scissor()`. */
/**
	Find current scissor region as tracked by the device.
*/
FZ_FUNCTION ::fz_rect ll_fz_device_current_scissor(::fz_device *dev);

/** Low-level wrapper for `::fz_device_gray()`. */
/**
	Retrieve global default colorspaces.

	These return borrowed references that should not be dropped,
	unless they are kept first.
*/
FZ_FUNCTION ::fz_colorspace *ll_fz_device_gray();

/** Low-level wrapper for `::fz_device_lab()`. */
FZ_FUNCTION ::fz_colorspace *ll_fz_device_lab();

/** Low-level wrapper for `::fz_device_rgb()`. */
FZ_FUNCTION ::fz_colorspace *ll_fz_device_rgb();

/** Low-level wrapper for `::fz_dirname()`. */
/**
	extract the directory component from a path.
*/
FZ_FUNCTION void ll_fz_dirname(char *dir, const char *path, size_t dirsize);

/** Low-level wrapper for `::fz_disable_device_hints()`. */
/**
	Disable (clear) hint bits within the hint bitfield for a device.
*/
FZ_FUNCTION void ll_fz_disable_device_hints(::fz_device *dev, int hints);

/** Low-level wrapper for `::fz_disable_icc()`. */
/**
	Disable icc profile based operation.
*/
FZ_FUNCTION void ll_fz_disable_icc();

/** Low-level wrapper for `::fz_display_list_is_empty()`. */
/**
	Check for a display list being empty

	list: The list to check.

	Returns true if empty, false otherwise.
*/
FZ_FUNCTION int ll_fz_display_list_is_empty(const ::fz_display_list *list);

/** Low-level wrapper for `::fz_div255()`. */
/**
	Undo alpha premultiplication.
*/
FZ_FUNCTION int ll_fz_div255(int c, int a);

/** Low-level wrapper for `::fz_do_always()`. */
FZ_FUNCTION int ll_fz_do_always();

/** Low-level wrapper for `::fz_do_catch()`. */
FZ_FUNCTION int ll_fz_do_catch();

/** Low-level wrapper for `::fz_do_try()`. */
FZ_FUNCTION int ll_fz_do_try();

/** Low-level wrapper for `::fz_document_handler_open()`. */
/** Helper for calling `fz_document_handler::open` function pointer via
Swig from Python/C#. */
FZ_FUNCTION ::fz_document *ll_fz_document_handler_open(const ::fz_document_handler *handler, ::fz_stream *stream, ::fz_stream *accel, ::fz_archive *dir, void *recognize_state);

/** Low-level wrapper for `::fz_document_handler_recognize()`. */
/** Helper for calling a `fz_document_handler::recognize` function
pointer via Swig from Python/C#. */
FZ_FUNCTION int ll_fz_document_handler_recognize(const ::fz_document_handler *handler, const char *magic);

/** Low-level wrapper for `::fz_document_output_intent()`. */
/**
	Find the output intent colorspace if the document has defined
	one.

	Returns a borrowed reference that should not be dropped, unless
	it is kept first.
*/
FZ_FUNCTION ::fz_colorspace *ll_fz_document_output_intent(::fz_document *doc);

/** Low-level wrapper for `::fz_document_supports_accelerator()`. */
/**
	Query if the document supports the saving of accelerator data.
*/
FZ_FUNCTION int ll_fz_document_supports_accelerator(::fz_document *doc);

/** Low-level wrapper for `::fz_dom_add_attribute()`. */
/**
	Add an attribute to an element.

	Ownership of att and value remain with the caller.
*/
FZ_FUNCTION void ll_fz_dom_add_attribute(::fz_xml *elt, const char *att, const char *value);

/** Low-level wrapper for `::fz_dom_append_child()`. */
/**
	Insert an element as the last child of a parent, unlinking the
	child from its current position if required.
*/
FZ_FUNCTION void ll_fz_dom_append_child(::fz_xml *parent, ::fz_xml *child);

/** Low-level wrapper for `::fz_dom_attribute()`. */
/**
	Retrieve the value of a given attribute from a given element.

	Returns a borrowed pointer to the value or NULL if not found.
*/
FZ_FUNCTION const char *ll_fz_dom_attribute(::fz_xml *elt, const char *att);

/** Low-level wrapper for `::fz_dom_body()`. */
/**
	Return a borrowed reference for the 'body' element of
	the given DOM.
*/
FZ_FUNCTION ::fz_xml *ll_fz_dom_body(::fz_xml *dom);

/** Low-level wrapper for `::fz_dom_clone()`. */
/**
	Clone an element (and its children).

	A borrowed reference to the clone is returned. The clone is not
	yet linked into the DOM.
*/
FZ_FUNCTION ::fz_xml *ll_fz_dom_clone(::fz_xml *elt);

/** Low-level wrapper for `::fz_dom_create_element()`. */
/**
	Create an element of a given tag type for the given DOM.

	The element is not linked into the DOM yet.
*/
FZ_FUNCTION ::fz_xml *ll_fz_dom_create_element(::fz_xml *dom, const char *tag);

/** Low-level wrapper for `::fz_dom_create_text_node()`. */
/**
	Create a text node for the given DOM.

	The element is not linked into the DOM yet.
*/
FZ_FUNCTION ::fz_xml *ll_fz_dom_create_text_node(::fz_xml *dom, const char *text);

/** Low-level wrapper for `::fz_dom_document_element()`. */
/**
	Return a borrowed reference for the document (the top
	level element) of the DOM.
*/
FZ_FUNCTION ::fz_xml *ll_fz_dom_document_element(::fz_xml *dom);

/** Low-level wrapper for `::fz_dom_find()`. */
/**
	Find the first element matching the requirements in a depth first traversal from elt.

	The tagname must match tag, unless tag is NULL, when all tag names are considered to match.

	If att is NULL, then all tags match.
	Otherwise:
		If match is NULL, then only nodes that have an att attribute match.
		If match is non-NULL, then only nodes that have an att attribute that matches match match.

	Returns NULL (if no match found), or a borrowed reference to the first matching element.
*/
FZ_FUNCTION ::fz_xml *ll_fz_dom_find(::fz_xml *elt, const char *tag, const char *att, const char *match);

/** Low-level wrapper for `::fz_dom_find_next()`. */
/**
	Find the next element matching the requirements.
*/
FZ_FUNCTION ::fz_xml *ll_fz_dom_find_next(::fz_xml *elt, const char *tag, const char *att, const char *match);

/** Low-level wrapper for `::fz_dom_first_child()`. */
/**
	Return a borrowed reference to the first child of a node,
	or NULL if there isn't one.
*/
FZ_FUNCTION ::fz_xml *ll_fz_dom_first_child(::fz_xml *elt);

/** Low-level wrapper for `::fz_dom_get_attribute()`.

This function has out-params. Python/C# wrappers look like:
	`ll_fz_dom_get_attribute(::fz_xml *elt, int i)` => `(const char *, const char *att)`
*/
/**
	Enumerate through the attributes of an element.

	Call with i=0,1,2,3... to enumerate attributes.

	On return *att and the return value will be NULL if there are not
	that many attributes to read. Otherwise, *att will be filled in
	with a borrowed pointer to the attribute name, and the return
	value will be a borrowed pointer to the value.
*/
FZ_FUNCTION const char *ll_fz_dom_get_attribute(::fz_xml *elt, int i, const char **att);

/** Low-level wrapper for `::fz_dom_insert_after()`. */
/**
	Insert an element (new_elt), after another element (node),
	unlinking the new_elt from its current position if required.
*/
FZ_FUNCTION void ll_fz_dom_insert_after(::fz_xml *node, ::fz_xml *new_elt);

/** Low-level wrapper for `::fz_dom_insert_before()`. */
/**
	Insert an element (new_elt), before another element (node),
	unlinking the new_elt from its current position if required.
*/
FZ_FUNCTION void ll_fz_dom_insert_before(::fz_xml *node, ::fz_xml *new_elt);

/** Low-level wrapper for `::fz_dom_next()`. */
/**
	Return a borrowed reference to the next sibling of a node,
	or NULL if there isn't one.
*/
FZ_FUNCTION ::fz_xml *ll_fz_dom_next(::fz_xml *elt);

/** Low-level wrapper for `::fz_dom_parent()`. */
/**
	Return a borrowed reference to the parent of a node,
	or NULL if there isn't one.
*/
FZ_FUNCTION ::fz_xml *ll_fz_dom_parent(::fz_xml *elt);

/** Low-level wrapper for `::fz_dom_previous()`. */
/**
	Return a borrowed reference to the previous sibling of a node,
	or NULL if there isn't one.
*/
FZ_FUNCTION ::fz_xml *ll_fz_dom_previous(::fz_xml *elt);

/** Low-level wrapper for `::fz_dom_remove()`. */
/**
	Remove an element from the DOM. The element can be added back elsewhere
	if required.

	No reference counting changes for the element.
*/
FZ_FUNCTION void ll_fz_dom_remove(::fz_xml *elt);

/** Low-level wrapper for `::fz_dom_remove_attribute()`. */
/**
	Remove an attribute from an element.
*/
FZ_FUNCTION void ll_fz_dom_remove_attribute(::fz_xml *elt, const char *att);

/** Low-level wrapper for `::fz_draw_story()`. */
FZ_FUNCTION void ll_fz_draw_story(::fz_story *story, ::fz_device *dev, ::fz_matrix ctm);

/** Low-level wrapper for `::fz_drop_archive()`. */
/**
	Drop a reference to an archive.

	When the last reference is dropped, this closes and releases
	any memory or filehandles associated with the archive.
*/
FZ_FUNCTION void ll_fz_drop_archive(::fz_archive *arch);

/** Low-level wrapper for `::fz_drop_band_writer()`. */
/**
	Drop the reference to the band writer, causing it to be
	destroyed.

	Never throws an exception.
*/
FZ_FUNCTION void ll_fz_drop_band_writer(::fz_band_writer *writer);

/** Low-level wrapper for `::fz_drop_bitmap()`. */
/**
	Drop a reference to the bitmap. When the reference count reaches
	zero, the bitmap will be destroyed.

	Never throws exceptions.
*/
FZ_FUNCTION void ll_fz_drop_bitmap(::fz_bitmap *bit);

/** Low-level wrapper for `::fz_drop_buffer()`. */
/**
	Drop a reference to the buffer. When the reference count reaches
	zero, the buffer is destroyed.

	Never throws exceptions.
*/
FZ_FUNCTION void ll_fz_drop_buffer(::fz_buffer *buf);

/** Low-level wrapper for `::fz_drop_colorspace()`. */
/**
	Drops a reference to the colorspace.

	When the reference count reaches zero, the colorspace is
	destroyed.
*/
FZ_FUNCTION void ll_fz_drop_colorspace(::fz_colorspace *colorspace);

/** Low-level wrapper for `::fz_drop_colorspace_imp()`. */
FZ_FUNCTION void ll_fz_drop_colorspace_imp(::fz_storable *cs_);

/** Low-level wrapper for `::fz_drop_compressed_buffer()`. */
/**
	Drop a reference to a compressed buffer. Destroys the buffer
	and frees any storage/other references held by it.

	Never throws exceptions.
*/
FZ_FUNCTION void ll_fz_drop_compressed_buffer(::fz_compressed_buffer *buf);

/** Low-level wrapper for `::fz_drop_context()`. */
/**
	Free a context and its global state.

	The context and all of its global state is freed, and any
	buffered warnings are flushed (see fz_flush_warnings). If NULL
	is passed in nothing will happen.

	Must not be called for a context that is being used in an active
	fz_try(), fz_always() or fz_catch() block.
*/
FZ_FUNCTION void ll_fz_drop_context();

/** Low-level wrapper for `::fz_drop_default_colorspaces()`. */
/**
	Drop a reference to the default colorspaces structure. When the
	reference count reaches 0, the references it holds internally
	to the underlying colorspaces will be dropped, and the structure
	will be destroyed.

	Never throws exceptions.
*/
FZ_FUNCTION void ll_fz_drop_default_colorspaces(::fz_default_colorspaces *default_cs);

/** Low-level wrapper for `::fz_drop_device()`. */
/**
	Reduce the reference count on a device. When the reference count
	reaches zero, the device and its resources will be freed.
	Don't forget to call fz_close_device before dropping the device,
	or you may get incomplete output!

	Never throws exceptions.
*/
FZ_FUNCTION void ll_fz_drop_device(::fz_device *dev);

/** Low-level wrapper for `::fz_drop_display_list()`. */
/**
	Decrement the reference count for a display list. When the
	reference count reaches zero, all the references in the display
	list itself are dropped, and the display list is freed.

	Never throws exceptions.
*/
FZ_FUNCTION void ll_fz_drop_display_list(::fz_display_list *list);

/** Low-level wrapper for `::fz_drop_document()`. */
/**
	Decrement the document reference count. When the reference
	count reaches 0, the document and all it's references are
	freed.

	Never throws exceptions.
*/
FZ_FUNCTION void ll_fz_drop_document(::fz_document *doc);

/** Low-level wrapper for `::fz_drop_document_writer()`. */
/**
	Called to discard a fz_document_writer.
	This may be called at any time during the process to release all
	the resources owned by the writer.

	Calling drop without having previously called close may leave
	the file in an inconsistent state.
*/
FZ_FUNCTION void ll_fz_drop_document_writer(::fz_document_writer *wri);

/** Low-level wrapper for `::fz_drop_font()`. */
/**
	Drop a reference to a fz_font, destroying the
	font when the last reference is dropped.

	font: The font to drop a reference to.
*/
FZ_FUNCTION void ll_fz_drop_font(::fz_font *font);

/** Low-level wrapper for `::fz_drop_function()`. */
FZ_FUNCTION void ll_fz_drop_function(::fz_function *func);

/** Low-level wrapper for `::fz_drop_glyph()`. */
/**
	Drop a reference and free a glyph.

	Decrement the reference count for the glyph. When no
	references remain the glyph will be freed.
*/
FZ_FUNCTION void ll_fz_drop_glyph(::fz_glyph *pix);

/** Low-level wrapper for `::fz_drop_halftone()`. */
/**
	Drop a reference to the halftone. When the reference count
	reaches zero, the halftone is destroyed.

	Never throws exceptions.
*/
FZ_FUNCTION void ll_fz_drop_halftone(::fz_halftone *ht);

/** Low-level wrapper for `::fz_drop_hash_table()`. */
/**
	Destroy the hash table.

	Values are dropped using the drop function.
*/
FZ_FUNCTION void ll_fz_drop_hash_table(::fz_hash_table *table);

/** Low-level wrapper for `::fz_drop_image()`. */
/**
	Decrement the (normal) reference count for an image. When the
	total (normal + key) reference count reaches zero, the image and
	its resources are freed.

	Never throws exceptions.
*/
FZ_FUNCTION void ll_fz_drop_image(::fz_image *image);

/** Low-level wrapper for `::fz_drop_image_base()`. */
/**
	Internal destructor for the base image class members.

	Exposed to allow derived image classes to be written.
*/
FZ_FUNCTION void ll_fz_drop_image_base(::fz_image *image);

/** Low-level wrapper for `::fz_drop_image_imp()`. */
/**
	Internal destructor exposed for fz_store integration.
*/
FZ_FUNCTION void ll_fz_drop_image_imp(::fz_storable *image);

/** Low-level wrapper for `::fz_drop_image_store_key()`. */
/**
	Decrement the store key reference count for an image. When the
	total (normal + key) reference count reaches zero, the image and
	its resources are freed.

	Never throws exceptions.
*/
FZ_FUNCTION void ll_fz_drop_image_store_key(::fz_image *image);

/** Low-level wrapper for `::fz_drop_imp()`.

This function has out-params. Python/C# wrappers look like:
	`ll_fz_drop_imp(void *p)` => `(int, int refs)`
*/
FZ_FUNCTION int ll_fz_drop_imp(void *p, int *refs);

/** Low-level wrapper for `::fz_drop_imp16()`.

This function has out-params. Python/C# wrappers look like:
	`ll_fz_drop_imp16(void *p)` => `(int, int16_t refs)`
*/
FZ_FUNCTION int ll_fz_drop_imp16(void *p, int16_t *refs);

/** Low-level wrapper for `::fz_drop_imp8()`. */
FZ_FUNCTION int ll_fz_drop_imp8(void *p, int8_t *refs);

/** Low-level wrapper for `::fz_drop_jbig2_globals()`. */
/**
	Decrement the reference count for a jbig2 globals record.
	When the reference count hits zero, the record is freed.

	Never throws an exception.
*/
FZ_FUNCTION void ll_fz_drop_jbig2_globals(::fz_jbig2_globals *globals);

/** Low-level wrapper for `::fz_drop_jbig2_globals_imp()`. */
/**
	Special jbig2 globals drop function for use in implementing
	store support.
*/
FZ_FUNCTION void ll_fz_drop_jbig2_globals_imp(::fz_storable *globals);

/** Low-level wrapper for `::fz_drop_key_storable()`. */
/**
	Decrement the (normal) reference count for a storable object.
	When the total reference count hits zero, the drop function for
	that object is called to free the object.

	Never throws exceptions.
*/
FZ_FUNCTION void ll_fz_drop_key_storable(const ::fz_key_storable *arg_1);

/** Low-level wrapper for `::fz_drop_key_storable_key()`. */
/**
	Decrement the (key) reference count for a storable object.
	When the total reference count hits zero, the drop function for
	that object is called to free the object.

	Never throws exceptions.
*/
FZ_FUNCTION void ll_fz_drop_key_storable_key(const ::fz_key_storable *arg_1);

/** Low-level wrapper for `::fz_drop_layout()`. */
/**
	Drop layout block. Free the pool, and linked blocks.

	Never throws exceptions.
*/
FZ_FUNCTION void ll_fz_drop_layout(::fz_layout_block *block);

/** Low-level wrapper for `::fz_drop_link()`. */
/**
	Decrement the reference count for a link. When the reference
	count reaches zero, the link is destroyed.

	When a link is freed, the reference for any linked link (next)
	is dropped too, thus an entire linked list of fz_link's can be
	freed by just dropping the head.
*/
FZ_FUNCTION void ll_fz_drop_link(::fz_link *link);

/** Low-level wrapper for `::fz_drop_outline()`. */
/**
	Decrements the reference count. When the reference point
	reaches zero, the outline is freed.

	When freed, it will drop linked	outline entries (next and down)
	too, thus a whole outline structure can be dropped by dropping
	the top entry.

	Never throws exceptions.
*/
FZ_FUNCTION void ll_fz_drop_outline(::fz_outline *outline);

/** Low-level wrapper for `::fz_drop_outline_iterator()`. */
/**
	Drop the current iterator.
*/
FZ_FUNCTION void ll_fz_drop_outline_iterator(::fz_outline_iterator *iter);

/** Low-level wrapper for `::fz_drop_output()`. */
/**
	Free an output stream. Don't forget to close it first!
*/
FZ_FUNCTION void ll_fz_drop_output(::fz_output *arg_1);

/** Low-level wrapper for `::fz_drop_page()`. */
/**
	Decrements the reference count for the page. When the reference
	count hits 0, the page and its references are freed.

	Never throws exceptions.
*/
FZ_FUNCTION void ll_fz_drop_page(::fz_page *page);

/** Low-level wrapper for `::fz_drop_path()`. */
/**
	Decrement the reference count. When the reference count hits
	zero, free the path.

	All paths can be dropped, regardless of their packing type.
	Packed paths do not own the blocks into which they are packed
	so dropping them does not free those blocks.

	Never throws exceptions.
*/
FZ_FUNCTION void ll_fz_drop_path(const ::fz_path *path);

/** Low-level wrapper for `::fz_drop_pixmap()`. */
/**
	Decrement the reference count for the pixmap. When the
	reference count hits 0, the pixmap is freed.

	Never throws exceptions.
*/
FZ_FUNCTION void ll_fz_drop_pixmap(::fz_pixmap *pix);

/** Low-level wrapper for `::fz_drop_pool()`. */
/**
	Drop a pool, freeing and invalidating all storage returned from
	the pool.
*/
FZ_FUNCTION void ll_fz_drop_pool(::fz_pool *pool);

/** Low-level wrapper for `::fz_drop_separations()`. */
/**
	Decrement the reference count for a separations structure.
	When the reference count hits zero, the separations structure
	is freed.

	Never throws exceptions.
*/
FZ_FUNCTION void ll_fz_drop_separations(::fz_separations *sep);

/** Low-level wrapper for `::fz_drop_shade()`. */
/**
	Decrement the reference count for the shade structure. When
	the reference count hits zero, the structure is freed.

	Never throws exceptions.
*/
FZ_FUNCTION void ll_fz_drop_shade(::fz_shade *shade);

/** Low-level wrapper for `::fz_drop_shade_color_cache()`. */
FZ_FUNCTION void ll_fz_drop_shade_color_cache(::fz_shade_color_cache *cache);

/** Low-level wrapper for `::fz_drop_shade_imp()`. */
/**
	Internal function to destroy a
	shade. Only exposed for use with the fz_store.

	shade: The reference to destroy.
*/
FZ_FUNCTION void ll_fz_drop_shade_imp(::fz_storable *shade);

/** Low-level wrapper for `::fz_drop_stext_page()`. */
FZ_FUNCTION void ll_fz_drop_stext_page(::fz_stext_page *page);

/** Low-level wrapper for `::fz_drop_storable()`. */
/**
	Decrement the reference count for a storable object. When the
	reference count hits zero, the drop function for that object
	is called to free the object.

	Never throws exceptions.
*/
FZ_FUNCTION void ll_fz_drop_storable(const ::fz_storable *arg_1);

/** Low-level wrapper for `::fz_drop_store_context()`. */
/**
	Decrement the reference count for the store context. When the
	reference count hits zero, the store context is freed.

	Never throws exceptions.
*/
FZ_FUNCTION void ll_fz_drop_store_context();

/** Low-level wrapper for `::fz_drop_story()`. */
FZ_FUNCTION void ll_fz_drop_story(::fz_story *story);

/** Low-level wrapper for `::fz_drop_stream()`. */
/**
	Decrements the reference count for a stream.

	When the reference count for the stream hits zero, frees the
	storage used for the fz_stream itself, and (usually)
	releases the underlying resources that the stream is based upon
	(depends on the method used to open the stream initially).
*/
FZ_FUNCTION void ll_fz_drop_stream(::fz_stream *stm);

/** Low-level wrapper for `::fz_drop_string()`. */
FZ_FUNCTION void ll_fz_drop_string(::fz_string *str);

/** Low-level wrapper for `::fz_drop_stroke_state()`. */
/**
	Drop a reference to a stroke state structure, destroying the
	structure if it is the last reference.
*/
FZ_FUNCTION void ll_fz_drop_stroke_state(const ::fz_stroke_state *stroke);

/** Low-level wrapper for `::fz_drop_text()`. */
/**
	Decrement the reference count for the text object. When the
	reference count hits zero, the text object is freed.

	Never throws exceptions.
*/
FZ_FUNCTION void ll_fz_drop_text(const ::fz_text *text);

/** Low-level wrapper for `::fz_drop_tree()`. */
/**
	Drop the tree.

	The storage used by the tree is freed, and each value has
	dropfunc called on it.
*/
FZ_FUNCTION void ll_fz_drop_tree(::fz_tree *node, void (*dropfunc)(::fz_context *, void *));

/** Low-level wrapper for `::fz_drop_xml()`. */
/**
	Drop a reference to the XML. When the last reference is
	dropped, the node and all its children and siblings will
	be freed.
*/
FZ_FUNCTION void ll_fz_drop_xml(::fz_xml *xml);

/** Low-level wrapper for `::fz_drop_zip_writer()`. */
/**
	Drop the reference to the zipfile.

	In common with other 'drop' methods, this will never throw an
	exception.
*/
FZ_FUNCTION void ll_fz_drop_zip_writer(::fz_zip_writer *zip);

/** Low-level wrapper for `::fz_dump_glyph_cache_stats()`. */
/**
	Dump debug statistics for the glyph cache.
*/
FZ_FUNCTION void ll_fz_dump_glyph_cache_stats(::fz_output *out);

/** Low-level wrapper for `::fz_duplicate_glyph_names_from_unicode()`. */
FZ_FUNCTION const char **ll_fz_duplicate_glyph_names_from_unicode(int unicode);

/** Low-level wrapper for `::fz_empty_store()`. */
/**
	Evict every item from the store.
*/
FZ_FUNCTION void ll_fz_empty_store();

/** Low-level wrapper for `::fz_enable_device_hints()`. */
/**
	Enable (set) hint bits within the hint bitfield for a device.
*/
FZ_FUNCTION void ll_fz_enable_device_hints(::fz_device *dev, int hints);

/** Low-level wrapper for `::fz_enable_icc()`. */
/**
	Enable icc profile based operation.
*/
FZ_FUNCTION void ll_fz_enable_icc();

/** Low-level wrapper for `::fz_encode_character()`. */
/**
	Find the glyph id for a given unicode
	character within a font.

	font: The font to look for the unicode character in.

	unicode: The unicode character to encode.

	Returns the glyph id for the given unicode value, or 0 if
	unknown.
*/
FZ_FUNCTION int ll_fz_encode_character(::fz_font *font, int unicode);

/** Low-level wrapper for `::fz_encode_character_by_glyph_name()`. */
/**
	Encode character.

	Either by direct lookup of glyphname within a font, or, failing
	that, by mapping glyphname to unicode and thence to the glyph
	index within the given font.

	Returns zero for type3 fonts.
*/
FZ_FUNCTION int ll_fz_encode_character_by_glyph_name(::fz_font *font, const char *glyphname);

/** Low-level wrapper for `::fz_encode_character_sc()`. */
/**
	Encode character, preferring small-caps variant if available.

	font: The font to look for the unicode character in.

	unicode: The unicode character to encode.

	Returns the glyph id for the given unicode value, or 0 if
	unknown.
*/
FZ_FUNCTION int ll_fz_encode_character_sc(::fz_font *font, int unicode);

/** Low-level wrapper for `::fz_encode_character_with_fallback()`.

This function has out-params. Python/C# wrappers look like:
	`ll_fz_encode_character_with_fallback(::fz_font *font, int unicode, int script, int language, ::fz_font **out_font)` => `(int)`
*/
/**
	Find the glyph id for
	a given unicode character within a font, falling back to
	an alternative if not found.

	font: The font to look for the unicode character in.

	unicode: The unicode character to encode.

	script: The script in use.

	language: The language in use.

	out_font: The font handle in which the given glyph represents
	the requested unicode character. The caller does not own the
	reference it is passed, so should call fz_keep_font if it is
	not simply to be used immediately.

	Returns the glyph id for the given unicode value in the supplied
	font (and sets *out_font to font) if it is present. Otherwise
	an alternative fallback font (based on script/language) is
	searched for. If the glyph is found therein, *out_font is set
	to this reference, and the glyph reference is returned. If it
	cannot be found anywhere, the function returns 0.
*/
FZ_FUNCTION int ll_fz_encode_character_with_fallback(::fz_font *font, int unicode, int script, int language, ::fz_font **out_font);

/** Low-level wrapper for `::fz_encode_uri()`. */
/**
 * Return a new string representing the provided string encoded as a URI.
 */
FZ_FUNCTION char *ll_fz_encode_uri(const char *s);

/** Low-level wrapper for `::fz_encode_uri_component()`. */
/**
 * Return a new string representing the provided string encoded as an URI component.
 * This also encodes the special reserved characters (; / ? : @ & = + $ , #).
 */
FZ_FUNCTION char *ll_fz_encode_uri_component(const char *s);

/** Low-level wrapper for `::fz_encode_uri_pathname()`. */
/**
 * Return a new string representing the provided string encoded as an URI path name.
 * This also encodes the special reserved characters except /.
 */
FZ_FUNCTION char *ll_fz_encode_uri_pathname(const char *s);

/** Low-level wrapper for `::fz_end_group()`. */
FZ_FUNCTION void ll_fz_end_group(::fz_device *dev);

/** Low-level wrapper for `::fz_end_layer()`. */
FZ_FUNCTION void ll_fz_end_layer(::fz_device *dev);

/** Low-level wrapper for `::fz_end_mask()`. */
FZ_FUNCTION void ll_fz_end_mask(::fz_device *dev);

/** Low-level wrapper for `::fz_end_mask_tr()`. */
FZ_FUNCTION void ll_fz_end_mask_tr(::fz_device *dev, ::fz_function *fn);

/** Low-level wrapper for `::fz_end_metatext()`. */
FZ_FUNCTION void ll_fz_end_metatext(::fz_device *dev);

/** Low-level wrapper for `::fz_end_page()`. */
/**
	Called to end the process of writing a page to a
	document.
*/
FZ_FUNCTION void ll_fz_end_page(::fz_document_writer *wri);

/** Low-level wrapper for `::fz_end_structure()`. */
FZ_FUNCTION void ll_fz_end_structure(::fz_device *dev);

/** Low-level wrapper for `::fz_end_throw_on_repair()`. */
FZ_FUNCTION void ll_fz_end_throw_on_repair();

/** Low-level wrapper for `::fz_end_tile()`. */
FZ_FUNCTION void ll_fz_end_tile(::fz_device *dev);

/** Low-level wrapper for `::fz_enumerate_font_cmap()`. */
/**
	Enumerate a cmap using a callback.
*/
FZ_FUNCTION void ll_fz_enumerate_font_cmap(::fz_font *font, ::fz_cmap_callback *cb, void *opaque);

/** Low-level wrapper for `::fz_enumerate_font_cmap2()`. */
/** SWIG-friendly wrapper for fz_enumerate_font_cmap(). */
FZ_FUNCTION std::vector<fz_font_ucs_gid> ll_fz_enumerate_font_cmap2(::fz_font *font);

/** Low-level wrapper for `::fz_error_callback()`.

This function has out-params. Python/C# wrappers look like:
	`ll_fz_error_callback()` => `(fz_error_cb *, void *user)`
*/
/**
	Retrieve the currently set error callback, or NULL if none
	has been set. Optionally, if user is non-NULL, the user pointer
	given when the warning callback was set is also passed back to
	the caller.
*/
FZ_FUNCTION ::fz_error_cb *ll_fz_error_callback(void **user);

/** Low-level wrapper for `::fz_eval_function()`.

This function has out-params. Python/C# wrappers look like:
	`ll_fz_eval_function(::fz_function *func, const float *in, int inlen, int outlen)` => float out
*/
FZ_FUNCTION void ll_fz_eval_function(::fz_function *func, const float *in, int inlen, float *out, int outlen);

/** Low-level wrapper for `::fz_expand_irect()`. */
FZ_FUNCTION ::fz_irect ll_fz_expand_irect(::fz_irect a, int expand);

/** Low-level wrapper for `::fz_expand_rect()`. */
/**
	Expand a bbox by a given amount in all directions.
*/
FZ_FUNCTION ::fz_rect ll_fz_expand_rect(::fz_rect b, float expand);

/** Low-level wrapper for `::fz_extract_ttf_from_ttc()`. */
FZ_FUNCTION ::fz_buffer *ll_fz_extract_ttf_from_ttc(::fz_font *font);

/** Low-level wrapper for `::fz_file_exists()`. */
/**
	Return true if the named file exists and is readable.
*/
FZ_FUNCTION int ll_fz_file_exists(const char *path);

/** Low-level wrapper for `::fz_fill_image()`. */
FZ_FUNCTION void ll_fz_fill_image(::fz_device *dev, ::fz_image *image, ::fz_matrix ctm, float alpha, ::fz_color_params color_params);

/** Low-level wrapper for `::fz_fill_image_mask()`. */
FZ_FUNCTION void ll_fz_fill_image_mask(::fz_device *dev, ::fz_image *image, ::fz_matrix ctm, ::fz_colorspace *colorspace, const float *color, float alpha, ::fz_color_params color_params);

/** Low-level wrapper for `::fz_fill_path()`. */
/**
	Device calls; graphics primitives and containers.
*/
FZ_FUNCTION void ll_fz_fill_path(::fz_device *dev, const ::fz_path *path, int even_odd, ::fz_matrix ctm, ::fz_colorspace *colorspace, const float *color, float alpha, ::fz_color_params color_params);

/** Low-level wrapper for `::fz_fill_pixmap_from_display_list()`. */
FZ_FUNCTION ::fz_pixmap *ll_fz_fill_pixmap_from_display_list(::fz_display_list *list, ::fz_matrix ctm, ::fz_pixmap *pix);

/** Low-level wrapper for `::fz_fill_pixmap_with_color()`.

This function has out-params. Python/C# wrappers look like:
	`ll_fz_fill_pixmap_with_color(::fz_pixmap *pix, ::fz_colorspace *colorspace, ::fz_color_params color_params)` => float color
*/
/**
	Fill pixmap with solid color.
*/
FZ_FUNCTION void ll_fz_fill_pixmap_with_color(::fz_pixmap *pix, ::fz_colorspace *colorspace, float *color, ::fz_color_params color_params);

/** Low-level wrapper for `::fz_fill_shade()`. */
FZ_FUNCTION void ll_fz_fill_shade(::fz_device *dev, ::fz_shade *shade, ::fz_matrix ctm, float alpha, ::fz_color_params color_params);

/** Low-level wrapper for `::fz_fill_text()`. */
FZ_FUNCTION void ll_fz_fill_text(::fz_device *dev, const ::fz_text *text, ::fz_matrix ctm, ::fz_colorspace *colorspace, const float *color, float alpha, ::fz_color_params color_params);

/** Low-level wrapper for `::fz_filter_store()`. */
/**
	Filter every element in the store with a matching type with the
	given function.

	If the function returns 1 for an element, drop the element.
*/
FZ_FUNCTION void ll_fz_filter_store(::fz_store_filter_fn *fn, void *arg, const ::fz_store_type *type);

/** Low-level wrapper for `::fz_find_item()`. */
/**
	Find an item within the store.

	drop: The function used to free the value (to ensure we get a
	value of the correct type).

	key: The key used to index the item.

	type: Functions used to manipulate the key.

	Returns NULL for not found, otherwise returns a pointer to the
	value indexed by key to which a reference has been taken.
*/
FZ_FUNCTION void *ll_fz_find_item(::fz_store_drop_fn *drop, void *key, const ::fz_store_type *type);

/** Low-level wrapper for `::fz_flush_output()`. */
/**
	Flush unwritten data.
*/
FZ_FUNCTION void ll_fz_flush_output(::fz_output *out);

/** Low-level wrapper for `::fz_flush_warnings()`. */
/**
	Flush any repeated warnings.

	Repeated warnings are buffered, counted and eventually printed
	along with the number of repetitions. Call fz_flush_warnings
	to force printing of the latest buffered warning and the
	number of repetitions, for example to make sure that all
	warnings are printed before exiting an application.
*/
FZ_FUNCTION void ll_fz_flush_warnings();

/** Low-level wrapper for `::fz_font_ascender()`. */
/**
	Retrieve font ascender in ems.
*/
FZ_FUNCTION float ll_fz_font_ascender(::fz_font *font);

/** Low-level wrapper for `::fz_font_bbox()`. */
/**
	Retrieve the font bbox.

	font: The font to query.

	Returns the font bbox by value; it is valid only if
	fz_font_flags(font)->invalid_bbox is zero.
*/
FZ_FUNCTION ::fz_rect ll_fz_font_bbox(::fz_font *font);

/** Low-level wrapper for `::fz_font_descender()`. */
/**
	Retrieve font descender in ems.
*/
FZ_FUNCTION float ll_fz_font_descender(::fz_font *font);

/** Low-level wrapper for `::fz_font_digest()`. */
/**
	Retrieve the MD5 digest for the font's data.
*/
FZ_FUNCTION void ll_fz_font_digest(::fz_font *font, unsigned char digest[16]);

/** Low-level wrapper for `::fz_font_flags()`. */
/**
	Retrieve a pointer to the font flags
	for a given font. These can then be updated as required.

	font: The font to query

	Returns a pointer to the flags structure (or NULL, if
	the font is NULL).
*/
FZ_FUNCTION ::fz_font_flags_t *ll_fz_font_flags(::fz_font *font);

/** Low-level wrapper for `::fz_font_ft_face()`. */
/**
	Retrieve the FT_Face handle
	for the font.

	font: The font to query

	Returns the FT_Face handle for the font, or NULL
	if not a freetype handled font. (Cast to void *
	to avoid nasty header exposure).
*/
FZ_FUNCTION void *ll_fz_font_ft_face(::fz_font *font);

/** Low-level wrapper for `::fz_font_is_bold()`. */
/**
	Query whether the font flags say that this font is bold.
*/
FZ_FUNCTION int ll_fz_font_is_bold(::fz_font *font);

/** Low-level wrapper for `::fz_font_is_italic()`. */
/**
	Query whether the font flags say that this font is italic.
*/
FZ_FUNCTION int ll_fz_font_is_italic(::fz_font *font);

/** Low-level wrapper for `::fz_font_is_monospaced()`. */
/**
	Query whether the font flags say that this font is monospaced.
*/
FZ_FUNCTION int ll_fz_font_is_monospaced(::fz_font *font);

/** Low-level wrapper for `::fz_font_is_serif()`. */
/**
	Query whether the font flags say that this font is serif.
*/
FZ_FUNCTION int ll_fz_font_is_serif(::fz_font *font);

/** Low-level wrapper for `::fz_font_name()`. */
/**
	Retrieve a pointer to the name of the font.

	font: The font to query.

	Returns a pointer to an internal copy of the font name.
	Will never be NULL, but may be the empty string.
*/
FZ_FUNCTION const char *ll_fz_font_name(::fz_font *font);

/** Low-level wrapper for `::fz_font_shaper_data()`. */
/**
	Retrieve a pointer to the shaper data
	structure for the given font.

	font: The font to query.

	Returns a pointer to the shaper data structure (or NULL if
	font is NULL).
*/
FZ_FUNCTION ::fz_shaper_data_t *ll_fz_font_shaper_data(::fz_font *font);

/** Low-level wrapper for `::fz_font_t3_procs()`. */
/**
	Retrieve the Type3 procs
	for a font.

	font: The font to query

	Returns the t3_procs pointer. Will be NULL for a
	non type-3 font.
*/
FZ_FUNCTION ::fz_buffer **ll_fz_font_t3_procs(::fz_font *font);

/** Low-level wrapper for `::fz_format_double()`. */
/** Swig-friendly and typesafe way to do fz_snprintf(fmt, value). `fmt`
must end with one of 'efg' otherwise we throw an exception. */
FZ_FUNCTION std::string ll_fz_format_double(const char *fmt, double value);

/** Low-level wrapper for `::fz_format_link_uri()`. */
/**
	Format an internal link to a page number, location, and possible viewing parameters,
	suitable for use with fz_create_link.

	Returns a newly allocated string that the caller must free.
*/
FZ_FUNCTION char *ll_fz_format_link_uri(::fz_document *doc, ::fz_link_dest dest);

/** Low-level wrapper for `::fz_format_output_path()`. */
/**
	create output file name using a template.

	If the path contains %[0-9]*d, the first such pattern will be
	replaced with the page number. If the template does not contain
	such a pattern, the page number will be inserted before the
	filename extension. If the template does not have a filename
	extension, the page number will be added to the end.
*/
FZ_FUNCTION void ll_fz_format_output_path(char *path, size_t size, const char *fmt, int page);

/** Low-level wrapper for `::fz_format_string()`. */
/**
	Our customised 'printf'-like string formatter.
	Takes %c, %d, %s, %u, %x, %X as usual.
	Modifiers are not supported except for zero-padding ints (e.g.
	%02d, %03u, %04x, etc).
	%g output in "as short as possible hopefully lossless
	non-exponent" form, see fz_ftoa for specifics.
	%f and %e output as usual.
	%C outputs a utf8 encoded int.
	%M outputs a fz_matrix*.
	%R outputs a fz_rect*.
	%P outputs a fz_point*.
	%n outputs a PDF name (with appropriate escaping).
	%q and %( output escaped strings in C/PDF syntax.
	%l{d,u,x,X} indicates that the values are int64_t.
	%z{d,u,x,X} indicates that the value is a size_t.

	user: An opaque pointer that is passed to the emit function.

	emit: A function pointer called to emit output bytes as the
	string is being formatted.
*/
FZ_FUNCTION void ll_fz_format_string(void *user, void (*emit)(::fz_context *, void *, int ), const char *fmt, va_list args);

/** Low-level wrapper for `::fz_free()`. */
/**
	Free a previously allocated block of memory.

	fz_free(ctx, NULL) does nothing.

	Never throws exceptions.
*/
FZ_FUNCTION void ll_fz_free(void *p);

/** Low-level wrapper for `::fz_ft_lock()`. */
FZ_FUNCTION void ll_fz_ft_lock();

/** Low-level wrapper for `::fz_ft_lock_held()`. */
FZ_FUNCTION int ll_fz_ft_lock_held();

/** Low-level wrapper for `::fz_ft_unlock()`. */
FZ_FUNCTION void ll_fz_ft_unlock();

/** Low-level wrapper for `::fz_function_size()`. */
FZ_FUNCTION size_t ll_fz_function_size(::fz_function *func);

/** Low-level wrapper for `::fz_gamma_pixmap()`. */
/**
	Apply gamma correction to a pixmap. All components
	of all pixels are modified (except alpha, which is unchanged).

	gamma: The gamma value to apply; 1.0 for no change.
*/
FZ_FUNCTION void ll_fz_gamma_pixmap(::fz_pixmap *pix, float gamma);

/** Low-level wrapper for `::fz_generate_transition()`. */
/**
	Generate a frame of a transition.

	tpix: Target pixmap
	opix: Old pixmap
	npix: New pixmap
	time: Position within the transition (0 to 256)
	trans: Transition details

	Returns 1 if successfully generated a frame.

	Note: Pixmaps must include alpha.
*/
FZ_FUNCTION int ll_fz_generate_transition(::fz_pixmap *tpix, ::fz_pixmap *opix, ::fz_pixmap *npix, int time, ::fz_transition *trans);

/** Low-level wrapper for `::fz_get_glyph_name()`. */
/**
	Find the name of a glyph

	font: The font to look for the glyph in.

	glyph: The glyph id to look for.

	buf: Pointer to a buffer for the name to be inserted into.

	size: The size of the buffer.

	If a font contains a name table, then the name of the glyph
	will be returned in the supplied buffer. Otherwise a name
	is synthesised. The name will be truncated to fit in
	the buffer.
*/
FZ_FUNCTION void ll_fz_get_glyph_name(::fz_font *font, int glyph, char *buf, int size);

/** Low-level wrapper for `::fz_get_glyph_name2()`. */
/**
C++ alternative to fz_get_glyph_name() that returns information in a std::string.
*/
FZ_FUNCTION std::string ll_fz_get_glyph_name2(::fz_font *font, int glyph);

/** Low-level wrapper for `::fz_get_pixmap_from_image()`.

This function has out-params. Python/C# wrappers look like:
	`ll_fz_get_pixmap_from_image(::fz_image *image, const ::fz_irect *subarea, ::fz_matrix *ctm)` => `(fz_pixmap *, int w, int h)`
*/
/**
	Called to get a handle to a pixmap from an image.

	image: The image to retrieve a pixmap from.

	subarea: The subarea of the image that we actually care about
	(or NULL to indicate the whole image).

	ctm: Optional, unless subarea is given. If given, then on
	entry this is the transform that will be applied to the complete
	image. It should be updated on exit to the transform to apply to
	the given subarea of the image. This is used to calculate the
	desired width/height for subsampling.

	w: If non-NULL, a pointer to an int to be updated on exit to the
	width (in pixels) that the scaled output will cover.

	h: If non-NULL, a pointer to an int to be updated on exit to the
	height (in pixels) that the scaled output will cover.

	Returns a non NULL kept pixmap pointer. May throw exceptions.
*/
FZ_FUNCTION ::fz_pixmap *ll_fz_get_pixmap_from_image(::fz_image *image, const ::fz_irect *subarea, ::fz_matrix *ctm, int *w, int *h);

/** Low-level wrapper for `::fz_get_unscaled_pixmap_from_image()`. */
/**
	Calls fz_get_pixmap_from_image() with ctm, subarea, w and h all set to NULL.
*/
FZ_FUNCTION ::fz_pixmap *ll_fz_get_unscaled_pixmap_from_image(::fz_image *image);

/** Low-level wrapper for `::fz_getopt()`.

This function has out-params. Python/C# wrappers look like:
	`ll_fz_getopt(int nargc, const char *ostr)` => `(int, char *nargv)`
*/
/**
	Identical to fz_getopt_long, but with a NULL longopts field, signifying no long
	options.
*/
FZ_FUNCTION int ll_fz_getopt(int nargc, char **nargv, const char *ostr);

/** Low-level wrapper for `::fz_getopt_long()`.

This function has out-params. Python/C# wrappers look like:
	`ll_fz_getopt_long(int nargc, const char *ostr, const ::fz_getopt_long_options *longopts)` => `(int, char *nargv)`
*/
/**
	Simple functions/variables for use in tools.

	ostr = option string. Comprises single letter options, followed by : if there
	is an argument to the option.

	longopts: NULL (indicating no long options), or a pointer to an array of
	longoptions, terminated by an entry with option == NULL.

	In the event of matching a single char option, this function will normally
	return the char. The exception to this is when the option requires an
	argument and none is supplied; in this case we return ':'.

	In the event of matching a long option, this function returns 0, with fz_optlong
	set to point to the matching option.

	A long option entry may be followed with : to indicate there is an argument to the
	option. If the need for an argument is specified in this way, and no argument is
	given, an error will be displayed and argument processing will stop. If an argument
	is given, and the long option record contains a non-null flag pointer, then the code
	will decode the argument and fill in that flag pointer. Specifically,
	case-insensitive matches to 'yes', 'no', 'true' and 'false' will cause a value of 0
	or 1 as appropriate to be written; failing this the arg will be interpreted as a
	decimal integer using atoi.

	A long option entry may be followed by an list of options (e.g. myoption=foo|bar|baz)
	and the option will be passed to fz_opt_from_list. The return value of that will be
	placed in fz_optitem. If the return value of that function is -1, then an error will
	be displayed and argument processing will stop.

	In the event of reaching the end of the arg list or '--', this function returns EOF.

	In the event of failing to match anything, an error is printed, and we return '?'.

	If an argument is expected for the option, then fz_optarg will be returned pointing
	at the start of the argument. Examples of supported argument formats: '-r500', '-r 500',
	'--resolution 500', '--resolution=500'.
*/
FZ_FUNCTION int ll_fz_getopt_long(int nargc, char **nargv, const char *ostr, const ::fz_getopt_long_options *longopts);

/** Low-level wrapper for `::fz_glyph_bbox()`. */
/**
	Return the bounding box of the glyph in pixels.
*/
FZ_FUNCTION ::fz_irect ll_fz_glyph_bbox(::fz_glyph *glyph);

/** Low-level wrapper for `::fz_glyph_bbox_no_ctx()`. */
FZ_FUNCTION ::fz_irect ll_fz_glyph_bbox_no_ctx(::fz_glyph *src);

/** Low-level wrapper for `::fz_glyph_cacheable()`. */
/**
	Determine if a given glyph in a font
	is cacheable. Certain glyphs in a type 3 font cannot safely
	be cached, as their appearance depends on the enclosing
	graphic state.

	font: The font to look for the glyph in.

	gif: The glyph to query.

	Returns non-zero if cacheable, 0 if not.
*/
FZ_FUNCTION int ll_fz_glyph_cacheable(::fz_font *font, int gid);

/** Low-level wrapper for `::fz_glyph_height()`. */
/**
	Return the height of the glyph in pixels.
*/
FZ_FUNCTION int ll_fz_glyph_height(::fz_glyph *glyph);

/** Low-level wrapper for `::fz_glyph_name_from_unicode_sc()`. */
FZ_FUNCTION const char *ll_fz_glyph_name_from_unicode_sc(int unicode);

/** Low-level wrapper for `::fz_glyph_width()`. */
/**
	Return the width of the glyph in pixels.
*/
FZ_FUNCTION int ll_fz_glyph_width(::fz_glyph *glyph);

/** Low-level wrapper for `::fz_graphics_aa_level()`. */
/**
	Get the number of bits of antialiasing we are
	using for graphics. Between 0 and 8.
*/
FZ_FUNCTION int ll_fz_graphics_aa_level();

/** Low-level wrapper for `::fz_graphics_min_line_width()`. */
/**
	Get the minimum line width to be
	used for stroked lines.

	min_line_width: The minimum line width to use (in pixels).
*/
FZ_FUNCTION float ll_fz_graphics_min_line_width();

/** Low-level wrapper for `::fz_gridfit_matrix()`. */
/**
	Grid fit a matrix.

	as_tiled = 0 => adjust the matrix so that the image of the unit
	square completely covers any pixel that was touched by the
	image of the unit square under the original matrix.

	as_tiled = 1 => adjust the matrix so that the corners of the
	image of the unit square align with the closest integer corner
	of the image of the unit square under the original matrix.
*/
FZ_FUNCTION ::fz_matrix ll_fz_gridfit_matrix(int as_tiled, ::fz_matrix m);

/** Low-level wrapper for `::fz_grisu()`.

This function has out-params. Python/C# wrappers look like:
	`ll_fz_grisu(float f, char *s)` => `(int, int exp)`
*/
FZ_FUNCTION int ll_fz_grisu(float f, char *s, int *exp);

/** Low-level wrapper for `::fz_grow_buffer()`. */
/**
	Make some space within a buffer (i.e. ensure that
	capacity > size).
*/
FZ_FUNCTION void ll_fz_grow_buffer(::fz_buffer *buf);

/** Low-level wrapper for `::fz_has_archive_entry()`. */
/**
	Check if entry by given name exists.

	If named entry does not exist 0 will be returned, if it does
	exist 1 is returned.

	name: Entry name to look for, this must be an exact match to
	the entry name in the archive.
*/
FZ_FUNCTION int ll_fz_has_archive_entry(::fz_archive *arch, const char *name);

/** Low-level wrapper for `::fz_has_option()`.

This function has out-params. Python/C# wrappers look like:
	`ll_fz_has_option(const char *opts, const char *key)` => `(int, const char *val)`
*/
/**
	Look for a given option (key) in the opts string. Return 1 if
	it has it, and update *val to point to the value within opts.
*/
FZ_FUNCTION int ll_fz_has_option(const char *opts, const char *key, const char **val);

/** Low-level wrapper for `::fz_has_permission()`. */
/**
	Check permission flags on document.
*/
FZ_FUNCTION int ll_fz_has_permission(::fz_document *doc, ::fz_permission p);

/** Low-level wrapper for `::fz_hash_filter()`. */
/**
	Iterate over the entries in a hash table, removing all the ones where callback returns true.
	Does NOT free the value of the entry, so the caller is expected to take care of this.
*/
FZ_FUNCTION void ll_fz_hash_filter(::fz_hash_table *table, void *state, ::fz_hash_table_filter_fn *callback);

/** Low-level wrapper for `::fz_hash_find()`. */
/**
	Search for a matching hash within the table, and return the
	associated value.
*/
FZ_FUNCTION void *ll_fz_hash_find(::fz_hash_table *table, const void *key);

/** Low-level wrapper for `::fz_hash_for_each()`. */
/**
	Iterate over the entries in a hash table.
*/
FZ_FUNCTION void ll_fz_hash_for_each(::fz_hash_table *table, void *state, ::fz_hash_table_for_each_fn *callback);

/** Low-level wrapper for `::fz_hash_insert()`. */
/**
	Insert a new key/value pair into the hash table.

	If an existing entry with the same key is found, no change is
	made to the hash table, and a pointer to the existing value is
	returned.

	If no existing entry with the same key is found, ownership of
	val passes in, key is copied, and NULL is returned.
*/
FZ_FUNCTION void *ll_fz_hash_insert(::fz_hash_table *table, const void *key, void *val);

/** Low-level wrapper for `::fz_hash_remove()`. */
/**
	Remove the entry for a given key.

	The value is NOT freed, so the caller is expected to take care
	of this.
*/
FZ_FUNCTION void ll_fz_hash_remove(::fz_hash_table *table, const void *key);

/** Low-level wrapper for `::fz_hb_lock()`. */
/**
	Lock against Harfbuzz being called
	simultaneously in several threads. This reuses
	FZ_LOCK_FREETYPE.
*/
FZ_FUNCTION void ll_fz_hb_lock();

/** Low-level wrapper for `::fz_hb_unlock()`. */
/**
	Unlock after a Harfbuzz call. This reuses
	FZ_LOCK_FREETYPE.
*/
FZ_FUNCTION void ll_fz_hb_unlock();

/** Low-level wrapper for `::fz_highlight_selection()`. */
/**
	Return a list of quads to highlight lines inside the selection
	points.
*/
FZ_FUNCTION int ll_fz_highlight_selection(::fz_stext_page *page, ::fz_point a, ::fz_point b, ::fz_quad *quads, int max_quads);

/** Low-level wrapper for `::fz_highlight_selection2()`. */
/**
C++ alternative to fz_highlight_selection() that returns quads in a
std::vector.
*/
FZ_FUNCTION std::vector<fz_quad> ll_fz_highlight_selection2(::fz_stext_page *page, ::fz_point a, ::fz_point b, int max_quads);

/** Low-level wrapper for `::fz_ignore_error()`. */
FZ_FUNCTION void ll_fz_ignore_error();

/** Low-level wrapper for `::fz_ignore_text()`. */
FZ_FUNCTION void ll_fz_ignore_text(::fz_device *dev, const ::fz_text *text, ::fz_matrix ctm);

/** Low-level wrapper for `::fz_image_orientation()`. */
/**
	Request the natural orientation of an image.

	This is for images (such as JPEG) that can contain internal
	specifications of rotation/flips. This is ignored by all the
	internal decode/rendering routines, but can be used by callers
	(such as the image document handler) to respect such
	specifications.

	The values used by MuPDF are as follows, with the equivalent
	Exif specifications given for information:

	0: Undefined
	1: 0 degree ccw rotation. (Exif = 1)
	2: 90 degree ccw rotation. (Exif = 8)
	3: 180 degree ccw rotation. (Exif = 3)
	4: 270 degree ccw rotation. (Exif = 6)
	5: flip on X. (Exif = 2)
	6: flip on X, then rotate ccw by 90 degrees. (Exif = 5)
	7: flip on X, then rotate ccw by 180 degrees. (Exif = 4)
	8: flip on X, then rotate ccw by 270 degrees. (Exif = 7)
*/
FZ_FUNCTION uint8_t ll_fz_image_orientation(::fz_image *image);

/** Low-level wrapper for `::fz_image_orientation_matrix()`. */
FZ_FUNCTION ::fz_matrix ll_fz_image_orientation_matrix(::fz_image *image);

/** Low-level wrapper for `::fz_image_resolution()`.

This function has out-params. Python/C# wrappers look like:
	`ll_fz_image_resolution(::fz_image *image)` => `(int xres, int yres)`
*/
/**
	Request the natural resolution
	of an image.

	xres, yres: Pointers to ints to be updated with the
	natural resolution of an image (or a sensible default
	if not encoded).
*/
FZ_FUNCTION void ll_fz_image_resolution(::fz_image *image, int *xres, int *yres);

/** Low-level wrapper for `::fz_image_size()`. */
/**
	Return the size of the storage used by an image.
*/
FZ_FUNCTION size_t ll_fz_image_size(::fz_image *im);

/** Low-level wrapper for `::fz_image_type_name()`. */
/**
	Map from FZ_IMAGE_* value to string.

	The returned string is static and therefore must not be freed.
*/
FZ_FUNCTION const char *ll_fz_image_type_name(int type);

/** Low-level wrapper for `::fz_include_point_in_rect()`. */
/**
	Expand a bbox to include a given point.
	To create a rectangle that encompasses a sequence of points, the
	rectangle must first be set to be the empty rectangle at one of
	the points before including the others.
*/
FZ_FUNCTION ::fz_rect ll_fz_include_point_in_rect(::fz_rect r, ::fz_point p);

/** Low-level wrapper for `::fz_init_text_decoder()`. */
FZ_FUNCTION void ll_fz_init_text_decoder(::fz_text_decoder *dec, const char *encoding);

/** Low-level wrapper for `::fz_install_load_system_font_funcs()`. */
/**
	Install functions to allow MuPDF to request fonts from the
	system.

	Only one set of hooks can be in use at a time.
*/
FZ_FUNCTION void ll_fz_install_load_system_font_funcs(::fz_load_system_font_fn *f, ::fz_load_system_cjk_font_fn *f_cjk, ::fz_load_system_fallback_font_fn *f_fallback);

/** Low-level wrapper for `::fz_install_load_system_font_funcs2()`. */
/**
Alternative to fz_install_load_system_font_funcs() that takes args in a
struct, to allow use from Python/C# via Swig Directors.
*/
FZ_FUNCTION void ll_fz_install_load_system_font_funcs2(::fz_install_load_system_font_funcs_args *args);

/** Low-level wrapper for `::fz_int2_heap_insert()`. */
FZ_FUNCTION void ll_fz_int2_heap_insert(::fz_int2_heap *heap, ::fz_int2 v);

/** Low-level wrapper for `::fz_int2_heap_sort()`. */
FZ_FUNCTION void ll_fz_int2_heap_sort(::fz_int2_heap *heap);

/** Low-level wrapper for `::fz_int2_heap_uniq()`. */
FZ_FUNCTION void ll_fz_int2_heap_uniq(::fz_int2_heap *heap);

/** Low-level wrapper for `::fz_int_heap_insert()`. */
FZ_FUNCTION void ll_fz_int_heap_insert(::fz_int_heap *heap, int v);

/** Low-level wrapper for `::fz_int_heap_sort()`. */
FZ_FUNCTION void ll_fz_int_heap_sort(::fz_int_heap *heap);

/** Low-level wrapper for `::fz_int_heap_uniq()`. */
FZ_FUNCTION void ll_fz_int_heap_uniq(::fz_int_heap *heap);

/** Low-level wrapper for `::fz_intersect_irect()`. */
/**
	Compute intersection of two bounding boxes.

	Similar to fz_intersect_rect but operates on two bounding
	boxes instead of two rectangles.
*/
FZ_FUNCTION ::fz_irect ll_fz_intersect_irect(::fz_irect a, ::fz_irect b);

/** Low-level wrapper for `::fz_intersect_rect()`. */
/**
	Compute intersection of two rectangles.

	Given two rectangles, update the first to be the smallest
	axis-aligned rectangle that covers the area covered by both
	given rectangles. If either rectangle is empty then the
	intersection is also empty. If either rectangle is infinite
	then the intersection is simply the non-infinite rectangle.
	Should both rectangles be infinite, then the intersection is
	also infinite.
*/
FZ_FUNCTION ::fz_rect ll_fz_intersect_rect(::fz_rect a, ::fz_rect b);

/** Low-level wrapper for `::fz_intptr_heap_insert()`. */
FZ_FUNCTION void ll_fz_intptr_heap_insert(::fz_intptr_heap *heap, ::fz_intptr v);

/** Low-level wrapper for `::fz_intptr_heap_sort()`. */
FZ_FUNCTION void ll_fz_intptr_heap_sort(::fz_intptr_heap *heap);

/** Low-level wrapper for `::fz_intptr_heap_uniq()`. */
FZ_FUNCTION void ll_fz_intptr_heap_uniq(::fz_intptr_heap *heap);

/** Low-level wrapper for `::fz_invert_bitmap()`. */
/**
	Invert bitmap.

	Never throws exceptions.
*/
FZ_FUNCTION void ll_fz_invert_bitmap(::fz_bitmap *bmp);

/** Low-level wrapper for `::fz_invert_matrix()`. */
/**
	Create an inverse matrix.

	inverse: Place to store inverse matrix.

	matrix: Matrix to invert. A degenerate matrix, where the
	determinant is equal to zero, can not be inverted and the
	original matrix is returned instead.

	Returns inverse.
*/
FZ_FUNCTION ::fz_matrix ll_fz_invert_matrix(::fz_matrix matrix);

/** Low-level wrapper for `::fz_invert_pixmap()`. */
/**
	Invert all the pixels in a pixmap. All components (process and
	spots) of all pixels are inverted (except alpha, which is
	unchanged).
*/
FZ_FUNCTION void ll_fz_invert_pixmap(::fz_pixmap *pix);

/** Low-level wrapper for `::fz_invert_pixmap_alpha()`. */
/**
	Invert the alpha fo all the pixels in a pixmap.
*/
FZ_FUNCTION void ll_fz_invert_pixmap_alpha(::fz_pixmap *pix);

/** Low-level wrapper for `::fz_invert_pixmap_luminance()`. */
/**
	Transform the pixels in a pixmap so that luminance of each
	pixel is inverted, and the chrominance remains unchanged (as
	much as accuracy allows).

	All components of all pixels are inverted (except alpha, which
	is unchanged). Only supports Grey and RGB bitmaps.
*/
FZ_FUNCTION void ll_fz_invert_pixmap_luminance(::fz_pixmap *pix);

/** Low-level wrapper for `::fz_invert_pixmap_raw()`. */
/**
	Invert all the pixels in a non-premultiplied pixmap in a
	very naive manner.
*/
FZ_FUNCTION void ll_fz_invert_pixmap_raw(::fz_pixmap *pix);

/** Low-level wrapper for `::fz_invert_pixmap_rect()`. */
/**
	Invert all the pixels in a given rectangle of a (premultiplied)
	pixmap. All components of all pixels in the rectangle are
	inverted (except alpha, which is unchanged).
*/
FZ_FUNCTION void ll_fz_invert_pixmap_rect(::fz_pixmap *image, ::fz_irect rect);

/** Low-level wrapper for `::fz_irect_from_rect()`. */
/**
	Convert a rect into the minimal bounding box
	that covers the rectangle.

	Coordinates in a bounding box are integers, so rounding of the
	rects coordinates takes place. The top left corner is rounded
	upwards and left while the bottom right corner is rounded
	downwards and to the right.
*/
FZ_FUNCTION ::fz_irect ll_fz_irect_from_rect(::fz_rect rect);

/** Low-level wrapper for `::fz_irect_height()`. */
/**
	Return the height of an irect. Invalid irects return 0.
*/
FZ_FUNCTION int ll_fz_irect_height(::fz_irect r);

/** Low-level wrapper for `::fz_irect_width()`. */
/**
	Return the width of an irect. Invalid irects return 0.
*/
FZ_FUNCTION unsigned int ll_fz_irect_width(::fz_irect r);

/** Low-level wrapper for `::fz_is_cfb_archive()`. */
/**
	Detect if stream object is a cfb archive.

	Assumes that the stream object is seekable.
*/
FZ_FUNCTION int ll_fz_is_cfb_archive(::fz_stream *file);

/** Low-level wrapper for `::fz_is_directory()`. */
/**
	Determine if a given path is a directory.

	In the case of the path not existing, or having no access
	we will return 0.
*/
FZ_FUNCTION int ll_fz_is_directory(const char *path);

/** Low-level wrapper for `::fz_is_document_reflowable()`. */
/**
	Is the document reflowable.

	Returns 1 to indicate reflowable documents, otherwise 0.
*/
FZ_FUNCTION int ll_fz_is_document_reflowable(::fz_document *doc);

/** Low-level wrapper for `::fz_is_empty_irect()`. */
FZ_FUNCTION int ll_fz_is_empty_irect(::fz_irect r);

/** Low-level wrapper for `::fz_is_empty_rect()`. */
/**
	Check if rectangle is empty.

	An empty rectangle is defined as one whose area is zero.
	All invalid rectangles are empty.
*/
FZ_FUNCTION int ll_fz_is_empty_rect(::fz_rect r);

/** Low-level wrapper for `::fz_is_eof()`. */
/**
	Query if the stream has reached EOF (during normal bytewise
	reading).

	See fz_is_eof_bits for the equivalent function for bitwise
	reading.
*/
FZ_FUNCTION int ll_fz_is_eof(::fz_stream *stm);

/** Low-level wrapper for `::fz_is_eof_bits()`. */
/**
	Query if the stream has reached EOF (during bitwise
	reading).

	See fz_is_eof for the equivalent function for bytewise
	reading.
*/
FZ_FUNCTION int ll_fz_is_eof_bits(::fz_stream *stm);

/** Low-level wrapper for `::fz_is_external_link()`. */
/**
	Query whether a link is external to a document (determined by
	uri containing a ':', intended to match with '://' which
	separates the scheme from the scheme specific parts in URIs).
*/
FZ_FUNCTION int ll_fz_is_external_link(const char *uri);

/** Low-level wrapper for `::fz_is_identity()`. */
FZ_FUNCTION int ll_fz_is_identity(::fz_matrix m);

/** Low-level wrapper for `::fz_is_infinite_irect()`. */
/**
	Check if an integer rectangle
	is infinite.
*/
FZ_FUNCTION int ll_fz_is_infinite_irect(::fz_irect r);

/** Low-level wrapper for `::fz_is_infinite_rect()`. */
/**
	Check if rectangle is infinite.
*/
FZ_FUNCTION int ll_fz_is_infinite_rect(::fz_rect r);

/** Low-level wrapper for `::fz_is_libarchive_archive()`. */
/**
	Detect if stream object is an archive supported by libarchive.

	Assumes that the stream object is seekable.
*/
FZ_FUNCTION int ll_fz_is_libarchive_archive(::fz_stream *file);

/** Low-level wrapper for `::fz_is_page_range()`. */
/**
	Check and parse string into page ranges:
		/,?(-?\d+|N)(-(-?\d+|N))?/
*/
FZ_FUNCTION int ll_fz_is_page_range(const char *s);

/** Low-level wrapper for `::fz_is_pixmap_monochrome()`. */
/**
	Check if the pixmap is a 1-channel image containing samples with
	only values 0 and 255
*/
FZ_FUNCTION int ll_fz_is_pixmap_monochrome(::fz_pixmap *pixmap);

/** Low-level wrapper for `::fz_is_point_inside_irect()`. */
/**
	Inclusion test for irects. (Rect is assumed to be open, i.e.
	top right corner is not included).
*/
FZ_FUNCTION int ll_fz_is_point_inside_irect(int x, int y, ::fz_irect r);

/** Low-level wrapper for `::fz_is_point_inside_quad()`. */
/**
	Inclusion test for quads.
*/
FZ_FUNCTION int ll_fz_is_point_inside_quad(::fz_point p, ::fz_quad q);

/** Low-level wrapper for `::fz_is_point_inside_rect()`. */
/**
	Inclusion test for rects. (Rect is assumed to be open, i.e.
	top right corner is not included).
*/
FZ_FUNCTION int ll_fz_is_point_inside_rect(::fz_point p, ::fz_rect r);

/** Low-level wrapper for `::fz_is_pow2()`. */
FZ_FUNCTION int ll_fz_is_pow2(int a);

/** Low-level wrapper for `::fz_is_quad_inside_quad()`. */
/**
	Inclusion test for quad in quad.

	This may break down if quads are not 'well formed'.
*/
FZ_FUNCTION int ll_fz_is_quad_inside_quad(::fz_quad needle, ::fz_quad haystack);

/** Low-level wrapper for `::fz_is_quad_intersecting_quad()`. */
/**
	Intersection test for quads.

	This may break down if quads are not 'well formed'.
*/
FZ_FUNCTION int ll_fz_is_quad_intersecting_quad(::fz_quad a, ::fz_quad b);

/** Low-level wrapper for `::fz_is_rectilinear()`. */
/**
	Check if a transformation is rectilinear.

	Rectilinear means that no shearing is present and that any
	rotations present are a multiple of 90 degrees. Usually this
	is used to make sure that axis-aligned rectangles before the
	transformation are still axis-aligned rectangles afterwards.
*/
FZ_FUNCTION int ll_fz_is_rectilinear(::fz_matrix m);

/** Low-level wrapper for `::fz_is_tar_archive()`. */
/**
	Detect if stream object is a tar archive.

	Assumes that the stream object is seekable.
*/
FZ_FUNCTION int ll_fz_is_tar_archive(::fz_stream *file);

/** Low-level wrapper for `::fz_is_valid_blend_colorspace()`. */
/**
	Check to see that a colorspace is appropriate to be used as
	a blending space (i.e. only grey, rgb or cmyk).
*/
FZ_FUNCTION int ll_fz_is_valid_blend_colorspace(::fz_colorspace *cs);

/** Low-level wrapper for `::fz_is_valid_irect()`. */
/**
	Check if an integer rectangle is valid.
*/
FZ_FUNCTION int ll_fz_is_valid_irect(::fz_irect r);

/** Low-level wrapper for `::fz_is_valid_rect()`. */
/**
	Check if rectangle is valid.
*/
FZ_FUNCTION int ll_fz_is_valid_rect(::fz_rect r);

/** Low-level wrapper for `::fz_is_zip_archive()`. */
/**
	Detect if stream object is a zip archive.

	Assumes that the stream object is seekable.
*/
FZ_FUNCTION int ll_fz_is_zip_archive(::fz_stream *file);

/** Low-level wrapper for `::fz_iso8859_1_from_unicode()`. */
FZ_FUNCTION int ll_fz_iso8859_1_from_unicode(int u);

/** Low-level wrapper for `::fz_iso8859_7_from_unicode()`. */
FZ_FUNCTION int ll_fz_iso8859_7_from_unicode(int u);

/** Low-level wrapper for `::fz_jbig2_globals_data()`. */
/**
	Return buffer containing jbig2 globals data stream.
*/
FZ_FUNCTION ::fz_buffer *ll_fz_jbig2_globals_data(::fz_jbig2_globals *globals);

/** Low-level wrapper for `::fz_keep_archive()`. */
/**
	Keep a reference to an archive.
*/
FZ_FUNCTION ::fz_archive *ll_fz_keep_archive(::fz_archive *arch);

/** Low-level wrapper for `::fz_keep_bitmap()`. */
/**
	Take an additional reference to the bitmap. The same pointer
	is returned.

	Never throws exceptions.
*/
FZ_FUNCTION ::fz_bitmap *ll_fz_keep_bitmap(::fz_bitmap *bit);

/** Low-level wrapper for `::fz_keep_buffer()`. */
/**
	Take an additional reference to the buffer. The same pointer
	is returned.

	Never throws exceptions.
*/
FZ_FUNCTION ::fz_buffer *ll_fz_keep_buffer(::fz_buffer *buf);

/** Low-level wrapper for `::fz_keep_colorspace()`. */
/**
	Increment the reference count for the colorspace.

	Returns the same pointer. Never throws an exception.
*/
FZ_FUNCTION ::fz_colorspace *ll_fz_keep_colorspace(::fz_colorspace *colorspace);

/** Low-level wrapper for `::fz_keep_compressed_buffer()`. */
/**
	Take a reference to an fz_compressed_buffer.
*/
FZ_FUNCTION ::fz_compressed_buffer *ll_fz_keep_compressed_buffer(::fz_compressed_buffer *cbuf);

/** Low-level wrapper for `::fz_keep_default_colorspaces()`. */
/**
	Keep an additional reference to the default colorspaces
	structure.

	Never throws exceptions.
*/
FZ_FUNCTION ::fz_default_colorspaces *ll_fz_keep_default_colorspaces(::fz_default_colorspaces *default_cs);

/** Low-level wrapper for `::fz_keep_device()`. */
/**
	Increment the reference count for a device. Returns the same
	pointer.

	Never throws exceptions.
*/
FZ_FUNCTION ::fz_device *ll_fz_keep_device(::fz_device *dev);

/** Low-level wrapper for `::fz_keep_display_list()`. */
/**
	Increment the reference count for a display list. Returns the
	same pointer.

	Never throws exceptions.
*/
FZ_FUNCTION ::fz_display_list *ll_fz_keep_display_list(::fz_display_list *list);

/** Low-level wrapper for `::fz_keep_document()`. */
/**
	Increment the document reference count. The same pointer is
	returned.

	Never throws exceptions.
*/
FZ_FUNCTION ::fz_document *ll_fz_keep_document(::fz_document *doc);

/** Low-level wrapper for `::fz_keep_font()`. */
/**
	Add a reference to an existing fz_font.

	font: The font to add a reference to.

	Returns the same font.
*/
FZ_FUNCTION ::fz_font *ll_fz_keep_font(::fz_font *font);

/** Low-level wrapper for `::fz_keep_function()`. */
FZ_FUNCTION ::fz_function *ll_fz_keep_function(::fz_function *func);

/** Low-level wrapper for `::fz_keep_glyph()`. */
/**
	Take a reference to a glyph.

	pix: The glyph to increment the reference for.

	Returns pix.
*/
FZ_FUNCTION ::fz_glyph *ll_fz_keep_glyph(::fz_glyph *pix);

/** Low-level wrapper for `::fz_keep_halftone()`. */
/**
	Take an additional reference to the halftone. The same pointer
	is returned.

	Never throws exceptions.
*/
FZ_FUNCTION ::fz_halftone *ll_fz_keep_halftone(::fz_halftone *half);

/** Low-level wrapper for `::fz_keep_image()`. */
/**
	Increment the (normal) reference count for an image. Returns the
	same pointer.

	Never throws exceptions.
*/
FZ_FUNCTION ::fz_image *ll_fz_keep_image(::fz_image *image);

/** Low-level wrapper for `::fz_keep_image_store_key()`. */
/**
	Increment the store key reference for an image. Returns the same
	pointer. (This is the count of references for an image held by
	keys in the image store).

	Never throws exceptions.
*/
FZ_FUNCTION ::fz_image *ll_fz_keep_image_store_key(::fz_image *image);

/** Low-level wrapper for `::fz_keep_imp()`.

This function has out-params. Python/C# wrappers look like:
	`ll_fz_keep_imp(void *p)` => `(void *, int refs)`
*/
FZ_FUNCTION void *ll_fz_keep_imp(void *p, int *refs);

/** Low-level wrapper for `::fz_keep_imp16()`.

This function has out-params. Python/C# wrappers look like:
	`ll_fz_keep_imp16(void *p)` => `(void *, int16_t refs)`
*/
FZ_FUNCTION void *ll_fz_keep_imp16(void *p, int16_t *refs);

/** Low-level wrapper for `::fz_keep_imp8()`. */
FZ_FUNCTION void *ll_fz_keep_imp8(void *p, int8_t *refs);

/** Low-level wrapper for `::fz_keep_imp8_locked()`. */
FZ_FUNCTION void *ll_fz_keep_imp8_locked(void *p, int8_t *refs);

/** Low-level wrapper for `::fz_keep_imp_locked()`.

This function has out-params. Python/C# wrappers look like:
	`ll_fz_keep_imp_locked(void *p)` => `(void *, int refs)`
*/
FZ_FUNCTION void *ll_fz_keep_imp_locked(void *p, int *refs);

/** Low-level wrapper for `::fz_keep_jbig2_globals()`. */
/**
	Increment the reference count for a jbig2 globals record.

	Never throws an exception.
*/
FZ_FUNCTION ::fz_jbig2_globals *ll_fz_keep_jbig2_globals(::fz_jbig2_globals *globals);

/** Low-level wrapper for `::fz_keep_key_storable()`. */
/**
	Increment the (normal) reference count for a key storable
	object. Returns the same pointer.

	Never throws exceptions.
*/
FZ_FUNCTION void *ll_fz_keep_key_storable(const ::fz_key_storable *arg_1);

/** Low-level wrapper for `::fz_keep_key_storable_key()`. */
/**
	Increment the (key) reference count for a key storable
	object. Returns the same pointer.

	Never throws exceptions.
*/
FZ_FUNCTION void *ll_fz_keep_key_storable_key(const ::fz_key_storable *arg_1);

/** Low-level wrapper for `::fz_keep_link()`. */
/**
	Increment the reference count for a link. The same pointer is
	returned.

	Never throws exceptions.
*/
FZ_FUNCTION ::fz_link *ll_fz_keep_link(::fz_link *link);

/** Low-level wrapper for `::fz_keep_outline()`. */
/**
	Increment the reference count. Returns the same pointer.

	Never throws exceptions.
*/
FZ_FUNCTION ::fz_outline *ll_fz_keep_outline(::fz_outline *outline);

/** Low-level wrapper for `::fz_keep_page()`. */
/**
	Increment the reference count for the page. Returns the same
	pointer.

	Never throws exceptions.
*/
FZ_FUNCTION ::fz_page *ll_fz_keep_page(::fz_page *page);

/** Low-level wrapper for `::fz_keep_page_locked()`. */
/**
	Increment the reference count for the page. Returns the same
	pointer. Must only be used when the alloc lock is already taken.

	Never throws exceptions.
*/
FZ_FUNCTION ::fz_page *ll_fz_keep_page_locked(::fz_page *page);

/** Low-level wrapper for `::fz_keep_path()`. */
/**
	Increment the reference count. Returns the same pointer.

	All paths can be kept, regardless of their packing type.

	Never throws exceptions.
*/
FZ_FUNCTION ::fz_path *ll_fz_keep_path(const ::fz_path *path);

/** Low-level wrapper for `::fz_keep_pixmap()`. */
/**
	Increment the reference count for the pixmap. The same pointer
	is returned.

	Never throws exceptions.
*/
FZ_FUNCTION ::fz_pixmap *ll_fz_keep_pixmap(::fz_pixmap *pix);

/** Low-level wrapper for `::fz_keep_separations()`. */
/**
	Increment the reference count for a separations structure.
	Returns the same pointer.

	Never throws exceptions.
*/
FZ_FUNCTION ::fz_separations *ll_fz_keep_separations(::fz_separations *sep);

/** Low-level wrapper for `::fz_keep_shade()`. */
/**
	Increment the reference count for the shade structure. The
	same pointer is returned.

	Never throws exceptions.
*/
FZ_FUNCTION ::fz_shade *ll_fz_keep_shade(::fz_shade *shade);

/** Low-level wrapper for `::fz_keep_storable()`. */
/**
	Increment the reference count for a storable object.
	Returns the same pointer.

	Never throws exceptions.
*/
FZ_FUNCTION void *ll_fz_keep_storable(const ::fz_storable *arg_1);

/** Low-level wrapper for `::fz_keep_store_context()`. */
/**
	Increment the reference count for the store context. Returns
	the same pointer.

	Never throws exceptions.
*/
FZ_FUNCTION ::fz_store *ll_fz_keep_store_context();

/** Low-level wrapper for `::fz_keep_stream()`. */
/**
	Increments the reference count for a stream. Returns the same
	pointer.

	Never throws exceptions.
*/
FZ_FUNCTION ::fz_stream *ll_fz_keep_stream(::fz_stream *stm);

/** Low-level wrapper for `::fz_keep_string()`. */
FZ_FUNCTION ::fz_string *ll_fz_keep_string(::fz_string *str);

/** Low-level wrapper for `::fz_keep_stroke_state()`. */
/**
	Take an additional reference to a stroke state structure.

	No modifications should be carried out on a stroke
	state to which more than one reference is held, as
	this can cause race conditions.
*/
FZ_FUNCTION ::fz_stroke_state *ll_fz_keep_stroke_state(const ::fz_stroke_state *stroke);

/** Low-level wrapper for `::fz_keep_text()`. */
/**
	Increment the reference count for the text object. The same
	pointer is returned.

	Never throws exceptions.
*/
FZ_FUNCTION ::fz_text *ll_fz_keep_text(const ::fz_text *text);

/** Low-level wrapper for `::fz_keep_xml()`. */
/**
	Add a reference to the XML.
*/
FZ_FUNCTION ::fz_xml *ll_fz_keep_xml(::fz_xml *xml);

/** Low-level wrapper for `::fz_koi8u_from_unicode()`. */
FZ_FUNCTION int ll_fz_koi8u_from_unicode(int u);

/** Low-level wrapper for `::fz_last_page()`. */
/**
	Function to get the location for the last page in the document.
	Using this can be far more efficient in some cases than calling
	fz_count_pages and using the page number.
*/
FZ_FUNCTION ::fz_location ll_fz_last_page(::fz_document *doc);

/** Low-level wrapper for `::fz_layout_document()`. */
/**
	Layout reflowable document types.

	w, h: Page size in points.
	em: Default font size in points.
*/
FZ_FUNCTION void ll_fz_layout_document(::fz_document *doc, float w, float h, float em);

/** Low-level wrapper for `::fz_lineto()`. */
/**
	Append a 'lineto' command to an open path.

	path: The path to modify.

	x, y: The coordinate to line to.

	Throws exceptions on failure to allocate, or attempting to
	modify a packed path.
*/
FZ_FUNCTION void ll_fz_lineto(::fz_path *path, float x, float y);

/** Low-level wrapper for `::fz_list_archive_entry()`. */
/**
	Get listed name of entry position idx.

	idx: Must be a value >= 0 < return value from
	fz_count_archive_entries. If not in range NULL will be
	returned.

	May throw an exception if this type of archive cannot list the
	entries (such as a directory).
*/
FZ_FUNCTION const char *ll_fz_list_archive_entry(::fz_archive *arch, int idx);

/** Low-level wrapper for `::fz_load_bmp_subimage()`. */
FZ_FUNCTION ::fz_pixmap *ll_fz_load_bmp_subimage(const unsigned char *buf, size_t len, int subimage);

/** Low-level wrapper for `::fz_load_bmp_subimage_count()`. */
FZ_FUNCTION int ll_fz_load_bmp_subimage_count(const unsigned char *buf, size_t len);

/** Low-level wrapper for `::fz_load_chapter_page()`. */
/**
	Load a page.

	After fz_load_page is it possible to retrieve the size of the
	page using fz_bound_page, or to render the page using
	fz_run_page_*. Free the page by calling fz_drop_page.

	chapter: chapter number, 0 is the first chapter of the document.
	number: page number, 0 is the first page of the chapter.
*/
FZ_FUNCTION ::fz_page *ll_fz_load_chapter_page(::fz_document *doc, int chapter, int page);

/** Low-level wrapper for `::fz_load_fallback_font()`. */
/**
	Try to load a fallback font for the
	given combination of font attributes. Whether a font is
	present or not will depend on the configuration in which
	MuPDF is built.

	script: The script desired (e.g. UCDN_SCRIPT_KATAKANA).

	language: The language desired (e.g. FZ_LANG_ja).

	serif: 1 if serif desired, 0 otherwise.

	bold: 1 if bold desired, 0 otherwise.

	italic: 1 if italic desired, 0 otherwise.

	Returns a new font handle, or NULL if not available.
*/
FZ_FUNCTION ::fz_font *ll_fz_load_fallback_font(int script, int language, int serif, int bold, int italic);

/** Low-level wrapper for `::fz_load_jbig2_globals()`. */
/**
	Create a jbig2 globals record from a buffer.

	Immutable once created.
*/
FZ_FUNCTION ::fz_jbig2_globals *ll_fz_load_jbig2_globals(::fz_buffer *buf);

/** Low-level wrapper for `::fz_load_jbig2_subimage()`. */
FZ_FUNCTION ::fz_pixmap *ll_fz_load_jbig2_subimage(const unsigned char *buf, size_t len, int subimage);

/** Low-level wrapper for `::fz_load_jbig2_subimage_count()`. */
FZ_FUNCTION int ll_fz_load_jbig2_subimage_count(const unsigned char *buf, size_t len);

/** Low-level wrapper for `::fz_load_jpx()`. */
/**
	Exposed for PDF.
*/
FZ_FUNCTION ::fz_pixmap *ll_fz_load_jpx(const unsigned char *data, size_t size, ::fz_colorspace *cs);

/** Low-level wrapper for `::fz_load_links()`. */
/**
	Load the list of links for a page.

	Returns a linked list of all the links on the page, each with
	its clickable region and link destination. Each link is
	reference counted so drop and free the list of links by
	calling fz_drop_link on the pointer return from fz_load_links.

	page: Page obtained from fz_load_page.
*/
FZ_FUNCTION ::fz_link *ll_fz_load_links(::fz_page *page);

/** Low-level wrapper for `::fz_load_outline()`. */
/**
	Load the hierarchical document outline.

	Should be freed by fz_drop_outline.
*/
FZ_FUNCTION ::fz_outline *ll_fz_load_outline(::fz_document *doc);

/** Low-level wrapper for `::fz_load_outline_from_iterator()`. */
/**
	Routine to implement the old Structure based API from an iterator.
*/
FZ_FUNCTION ::fz_outline *ll_fz_load_outline_from_iterator(::fz_outline_iterator *iter);

/** Low-level wrapper for `::fz_load_page()`. */
/**
	Load a given page number from a document. This may be much less
	efficient than loading by location (chapter+page) for some
	document types.
*/
FZ_FUNCTION ::fz_page *ll_fz_load_page(::fz_document *doc, int number);

/** Low-level wrapper for `::fz_load_pnm_subimage()`. */
FZ_FUNCTION ::fz_pixmap *ll_fz_load_pnm_subimage(const unsigned char *buf, size_t len, int subimage);

/** Low-level wrapper for `::fz_load_pnm_subimage_count()`. */
FZ_FUNCTION int ll_fz_load_pnm_subimage_count(const unsigned char *buf, size_t len);

/** Low-level wrapper for `::fz_load_system_cjk_font()`. */
/**
	Attempt to load a given font from
	the system.

	name: The name of the desired font.

	ordering: The ordering to load the font from (e.g. FZ_ADOBE_KOREA)

	serif: 1 if serif desired, 0 otherwise.

	Returns a new font handle, or NULL if no matching font was found
	(or on error).
*/
FZ_FUNCTION ::fz_font *ll_fz_load_system_cjk_font(const char *name, int ordering, int serif);

/** Low-level wrapper for `::fz_load_system_font()`. */
/**
	Attempt to load a given font from the system.

	name: The name of the desired font.

	bold: 1 if bold desired, 0 otherwise.

	italic: 1 if italic desired, 0 otherwise.

	needs_exact_metrics: 1 if an exact metrical match is required,
	0 otherwise.

	Returns a new font handle, or NULL if no matching font was found
	(or on error).
*/
FZ_FUNCTION ::fz_font *ll_fz_load_system_font(const char *name, int bold, int italic, int needs_exact_metrics);

/** Low-level wrapper for `::fz_load_tiff_subimage()`. */
FZ_FUNCTION ::fz_pixmap *ll_fz_load_tiff_subimage(const unsigned char *buf, size_t len, int subimage);

/** Low-level wrapper for `::fz_load_tiff_subimage_count()`. */
/**
	Exposed for CBZ.
*/
FZ_FUNCTION int ll_fz_load_tiff_subimage_count(const unsigned char *buf, size_t len);

/** Low-level wrapper for `::fz_location_from_page_number()`. */
/**
	Converts from page number to chapter+page. This may cause many
	chapters to be laid out in order to calculate the number of
	pages within those chapters.
*/
FZ_FUNCTION ::fz_location ll_fz_location_from_page_number(::fz_document *doc, int number);

/** Low-level wrapper for `::fz_lock()`. */
/**
	Lock one of the user supplied mutexes.
*/
FZ_FUNCTION void ll_fz_lock(int lock);

/** Low-level wrapper for `::fz_log_error()`. */
/**
	Log a (preformatted) string to the registered
	error stream (stderr by default).
*/
FZ_FUNCTION void ll_fz_log_error(const char *str);

/** Low-level wrapper for `::fz_lookup_base14_font()`.

This function has out-params. Python/C# wrappers look like:
	`ll_fz_lookup_base14_font(const char *name)` => `(const unsigned char *, int len)`
*/
/**
	Search the builtin base14 fonts for a match.
	Whether a given font is present or not will depend on the
	configuration in which MuPDF is built.

	name: The name of the font desired.

	len: Pointer to a place to receive the length of the discovered
	font buffer.

	Returns a pointer to the font file data, or NULL if not present.
*/
FZ_FUNCTION const unsigned char *ll_fz_lookup_base14_font(const char *name, int *len);

/** Low-level wrapper for `::fz_lookup_blendmode()`. */
/**
	Map from (case sensitive) blend mode string to enumeration.
*/
FZ_FUNCTION int ll_fz_lookup_blendmode(const char *name);

/** Low-level wrapper for `::fz_lookup_bookmark()`. */
/**
	Find a bookmark and return its page number.
*/
FZ_FUNCTION ::fz_location ll_fz_lookup_bookmark(::fz_document *doc, ::fz_bookmark mark);

/** Low-level wrapper for `::fz_lookup_builtin_font()`.

This function has out-params. Python/C# wrappers look like:
	`ll_fz_lookup_builtin_font(const char *name, int bold, int italic)` => `(const unsigned char *, int len)`
*/
/**
	Search the builtin fonts for a match.
	Whether a given font is present or not will depend on the
	configuration in which MuPDF is built.

	name: The name of the font desired.

	bold: 1 if bold desired, 0 otherwise.

	italic: 1 if italic desired, 0 otherwise.

	len: Pointer to a place to receive the length of the discovered
	font buffer.

	Returns a pointer to the font file data, or NULL if not present.
*/
FZ_FUNCTION const unsigned char *ll_fz_lookup_builtin_font(const char *name, int bold, int italic, int *len);

/** Low-level wrapper for `::fz_lookup_cjk_font()`.

This function has out-params. Python/C# wrappers look like:
	`ll_fz_lookup_cjk_font(int ordering)` => `(const unsigned char *, int len, int index)`
*/
/**
	Search the builtin cjk fonts for a match.
	Whether a font is present or not will depend on the
	configuration in which MuPDF is built.

	ordering: The desired ordering of the font (e.g. FZ_ADOBE_KOREA).

	len: Pointer to a place to receive the length of the discovered
	font buffer.

	Returns a pointer to the font file data, or NULL if not present.
*/
FZ_FUNCTION const unsigned char *ll_fz_lookup_cjk_font(int ordering, int *len, int *index);

/** Low-level wrapper for `::fz_lookup_cjk_font_by_language()`.

This function has out-params. Python/C# wrappers look like:
	`ll_fz_lookup_cjk_font_by_language(const char *lang)` => `(const unsigned char *, int len, int subfont)`
*/
/**
	Search the builtin cjk fonts for a match for a given language.
	Whether a font is present or not will depend on the
	configuration in which MuPDF is built.

	lang: Pointer to a (case sensitive) language string (e.g.
	"ja", "ko", "zh-Hant" etc).

	len: Pointer to a place to receive the length of the discovered
	font buffer.

	subfont: Pointer to a place to store the subfont index of the
	discovered font.

	Returns a pointer to the font file data, or NULL if not present.
*/
FZ_FUNCTION const unsigned char *ll_fz_lookup_cjk_font_by_language(const char *lang, int *len, int *subfont);

/** Low-level wrapper for `::fz_lookup_cjk_ordering_by_language()`. */
/**
	Return the matching FZ_ADOBE_* ordering
	for the given language tag, such as "zh-Hant", "zh-Hans", "ja", or "ko".
*/
FZ_FUNCTION int ll_fz_lookup_cjk_ordering_by_language(const char *name);

/** Low-level wrapper for `::fz_lookup_image_type()`. */
/**
	Map from (case sensitive) image type string to FZ_IMAGE_*
	type value.
*/
FZ_FUNCTION int ll_fz_lookup_image_type(const char *type);

/** Low-level wrapper for `::fz_lookup_metadata()`. */
/**
	Retrieve document meta data strings.

	doc: The document to query.

	key: Which meta data key to retrieve...

	Basic information:
		'format'	-- Document format and version.
		'encryption'	-- Description of the encryption used.

	From the document information dictionary:
		'info:Title'
		'info:Author'
		'info:Subject'
		'info:Keywords'
		'info:Creator'
		'info:Producer'
		'info:CreationDate'
		'info:ModDate'

	buf: The buffer to hold the results (a nul-terminated UTF-8
	string).

	size: Size of 'buf'.

	Returns the number of bytes need to store the string plus terminator
	(will be larger than 'size' if the output was truncated), or -1 if the
	key is not recognized or found.
*/
FZ_FUNCTION int ll_fz_lookup_metadata(::fz_document *doc, const char *key, char *buf, int size);

/** Low-level wrapper for `::fz_lookup_metadata2()`. */
/**
C++ alternative to `fz_lookup_metadata()` that returns a `std::string`
or calls `fz_throw()` if not found.
*/
FZ_FUNCTION std::string ll_fz_lookup_metadata2(::fz_document *doc, const char *key);

/** Low-level wrapper for `::fz_lookup_noto_boxes_font()`.

This function has out-params. Python/C# wrappers look like:
	`ll_fz_lookup_noto_boxes_font()` => `(const unsigned char *, int len)`
*/
FZ_FUNCTION const unsigned char *ll_fz_lookup_noto_boxes_font(int *len);

/** Low-level wrapper for `::fz_lookup_noto_emoji_font()`.

This function has out-params. Python/C# wrappers look like:
	`ll_fz_lookup_noto_emoji_font()` => `(const unsigned char *, int len)`
*/
FZ_FUNCTION const unsigned char *ll_fz_lookup_noto_emoji_font(int *len);

/** Low-level wrapper for `::fz_lookup_noto_font()`.

This function has out-params. Python/C# wrappers look like:
	`ll_fz_lookup_noto_font(int script, int lang)` => `(const unsigned char *, int len, int subfont)`
*/
/**
	Search the builtin noto fonts for a match.
	Whether a font is present or not will depend on the
	configuration in which MuPDF is built.

	script: The script desired (e.g. UCDN_SCRIPT_KATAKANA).

	lang: The language desired (e.g. FZ_LANG_ja).

	len: Pointer to a place to receive the length of the discovered
	font buffer.

	Returns a pointer to the font file data, or NULL if not present.
*/
FZ_FUNCTION const unsigned char *ll_fz_lookup_noto_font(int script, int lang, int *len, int *subfont);

/** Low-level wrapper for `::fz_lookup_noto_math_font()`.

This function has out-params. Python/C# wrappers look like:
	`ll_fz_lookup_noto_math_font()` => `(const unsigned char *, int len)`
*/
/**
	Search the builtin noto fonts specific symbol fonts.
	Whether a font is present or not will depend on the
	configuration in which MuPDF is built.
*/
FZ_FUNCTION const unsigned char *ll_fz_lookup_noto_math_font(int *len);

/** Low-level wrapper for `::fz_lookup_noto_music_font()`.

This function has out-params. Python/C# wrappers look like:
	`ll_fz_lookup_noto_music_font()` => `(const unsigned char *, int len)`
*/
FZ_FUNCTION const unsigned char *ll_fz_lookup_noto_music_font(int *len);

/** Low-level wrapper for `::fz_lookup_noto_symbol1_font()`.

This function has out-params. Python/C# wrappers look like:
	`ll_fz_lookup_noto_symbol1_font()` => `(const unsigned char *, int len)`
*/
FZ_FUNCTION const unsigned char *ll_fz_lookup_noto_symbol1_font(int *len);

/** Low-level wrapper for `::fz_lookup_noto_symbol2_font()`.

This function has out-params. Python/C# wrappers look like:
	`ll_fz_lookup_noto_symbol2_font()` => `(const unsigned char *, int len)`
*/
FZ_FUNCTION const unsigned char *ll_fz_lookup_noto_symbol2_font(int *len);

/** Low-level wrapper for `::fz_lookup_rendering_intent()`. */
/**
	Map from (case sensitive) rendering intent string to enumeration
	value.
*/
FZ_FUNCTION int ll_fz_lookup_rendering_intent(const char *name);

/** Low-level wrapper for `::fz_make_bookmark()`. */
/**
	Create a bookmark for the given page, which can be used to find
	the same location after the document has been laid out with
	different parameters.
*/
FZ_FUNCTION ::fz_bookmark ll_fz_make_bookmark(::fz_document *doc, ::fz_location loc);

/** Low-level wrapper for `::fz_make_irect()`. */
FZ_FUNCTION ::fz_irect ll_fz_make_irect(int x0, int y0, int x1, int y1);

/** Low-level wrapper for `::fz_make_link_dest_none()`. */
FZ_FUNCTION ::fz_link_dest ll_fz_make_link_dest_none();

/** Low-level wrapper for `::fz_make_link_dest_xyz()`. */
FZ_FUNCTION ::fz_link_dest ll_fz_make_link_dest_xyz(int chapter, int page, float x, float y, float z);

/** Low-level wrapper for `::fz_make_location()`. */
/**
	Simple constructor for fz_locations.
*/
FZ_FUNCTION ::fz_location ll_fz_make_location(int chapter, int page);

/** Low-level wrapper for `::fz_make_matrix()`. */
FZ_FUNCTION ::fz_matrix ll_fz_make_matrix(float a, float b, float c, float d, float e, float f);

/** Low-level wrapper for `::fz_make_point()`. */
FZ_FUNCTION ::fz_point ll_fz_make_point(float x, float y);

/** Low-level wrapper for `::fz_make_quad()`. */
/**
	Inline convenience construction function.
*/
FZ_FUNCTION ::fz_quad ll_fz_make_quad(float ul_x, float ul_y, float ur_x, float ur_y, float ll_x, float ll_y, float lr_x, float lr_y);

/** Low-level wrapper for `::fz_make_rect()`. */
FZ_FUNCTION ::fz_rect ll_fz_make_rect(float x0, float y0, float x1, float y1);

/** Low-level wrapper for `::fz_malloc()`. */
/**
	Allocate uninitialized memory of a given size.
	Does NOT clear the memory!

	May return NULL for size = 0.

	Throws exception in the event of failure to allocate.
*/
FZ_FUNCTION void *ll_fz_malloc(size_t size);

/** Low-level wrapper for `::fz_malloc_no_throw()`. */
/**
	fz_malloc equivalent that returns NULL rather than throwing
	exceptions.
*/
FZ_FUNCTION void *ll_fz_malloc_no_throw(size_t size);

/** Low-level wrapper for `::fz_matrix_expansion()`. */
/**
	Calculate average scaling factor of matrix.
*/
FZ_FUNCTION float ll_fz_matrix_expansion(::fz_matrix m);

/** Low-level wrapper for `::fz_matrix_max_expansion()`. */
/**
	Find the largest expansion performed by this matrix.
	(i.e. max(abs(m.a),abs(m.b),abs(m.c),abs(m.d))
*/
FZ_FUNCTION float ll_fz_matrix_max_expansion(::fz_matrix m);

/** Low-level wrapper for `::fz_max()`. */
FZ_FUNCTION float ll_fz_max(float a, float b);

/** Low-level wrapper for `::fz_maxi()`. */
FZ_FUNCTION int ll_fz_maxi(int a, int b);

/** Low-level wrapper for `::fz_maxi64()`. */
FZ_FUNCTION int64_t ll_fz_maxi64(int64_t a, int64_t b);

/** Low-level wrapper for `::fz_maxz()`. */
FZ_FUNCTION size_t ll_fz_maxz(size_t a, size_t b);

/** Low-level wrapper for `::fz_md5_buffer()`. */
/**
	Create an MD5 digest from buffer contents.

	Never throws exceptions.
*/
FZ_FUNCTION void ll_fz_md5_buffer(::fz_buffer *buffer, unsigned char digest[16]);

/** Low-level wrapper for `::fz_md5_final()`. */
/**
	MD5 finalization. Ends an MD5 message-digest operation, writing
	the message digest and zeroizing the context.

	Never throws an exception.
*/
FZ_FUNCTION void ll_fz_md5_final(::fz_md5 *state, unsigned char digest[16]);

/** Low-level wrapper for `::fz_md5_final2()`. */
/**
C++ alternative to fz_md5_final() that returns the digest by value.
*/
FZ_FUNCTION std::vector<unsigned char> ll_fz_md5_final2(::fz_md5 *md5);

/** Low-level wrapper for `::fz_md5_init()`. */
/**
	MD5 initialization. Begins an MD5 operation, writing a new
	context.

	Never throws an exception.
*/
FZ_FUNCTION void ll_fz_md5_init(::fz_md5 *state);

/** Low-level wrapper for `::fz_md5_pixmap()`. */
FZ_FUNCTION void ll_fz_md5_pixmap(::fz_pixmap *pixmap, unsigned char digest[16]);

/** Low-level wrapper for `::fz_md5_pixmap2()`. */
/**
C++ alternative to `fz_md5_pixmap()` that returns the digest by value.
*/
FZ_FUNCTION std::vector<unsigned char> ll_fz_md5_pixmap2(::fz_pixmap *pixmap);

/** Low-level wrapper for `::fz_md5_update()`. */
/**
	MD5 block update operation. Continues an MD5 message-digest
	operation, processing another message block, and updating the
	context.

	Never throws an exception.
*/
FZ_FUNCTION void ll_fz_md5_update(::fz_md5 *state, const unsigned char *input, size_t inlen);

/** Low-level wrapper for `::fz_md5_update_int64()`. */
/**
	MD5 block update operation. Continues an MD5 message-digest
	operation, processing an int64, and updating the context.

	Never throws an exception.
*/
FZ_FUNCTION void ll_fz_md5_update_int64(::fz_md5 *state, int64_t i);

/** Low-level wrapper for `::fz_measure_string()`. */
/**
	Measure the advance width of a UTF8 string should it be added to a text object.

	This uses the same layout algorithms as fz_show_string, and can be used
	to calculate text alignment adjustments.
*/
FZ_FUNCTION ::fz_matrix ll_fz_measure_string(::fz_font *user_font, ::fz_matrix trm, const char *s, int wmode, int bidi_level, ::fz_bidi_direction markup_dir, ::fz_text_language language);

/** Low-level wrapper for `::fz_memmem()`. */
/**
	Find the start of the first occurrence of the substring needle in haystack.
*/
FZ_FUNCTION void *ll_fz_memmem(const void *haystack, size_t haystacklen, const void *needle, size_t needlelen);

/** Low-level wrapper for `::fz_memrnd()`. */
/**
	Fill block with len bytes of pseudo-randomness.
*/
FZ_FUNCTION void ll_fz_memrnd(uint8_t *block, int len);

/** Low-level wrapper for `::fz_min()`. */
FZ_FUNCTION float ll_fz_min(float a, float b);

/** Low-level wrapper for `::fz_mini()`. */
FZ_FUNCTION int ll_fz_mini(int a, int b);

/** Low-level wrapper for `::fz_mini64()`. */
FZ_FUNCTION int64_t ll_fz_mini64(int64_t a, int64_t b);

/** Low-level wrapper for `::fz_minz()`. */
FZ_FUNCTION size_t ll_fz_minz(size_t a, size_t b);

/** Low-level wrapper for `::fz_mkdir()`. */
FZ_FUNCTION int ll_fz_mkdir(char *path);

/** Low-level wrapper for `::fz_morph_error()`. */
/**
	Called within a catch block this modifies the current
	exception's code. If it's of type 'fromcode' it is
	modified to 'tocode'. Typically used for 'downgrading'
	exception severity.
*/
FZ_FUNCTION void ll_fz_morph_error(int fromcode, int tocode);

/** Low-level wrapper for `::fz_mount_multi_archive()`. */
/**
	Add an archive to the set of archives handled by a multi
	archive.

	If path is NULL, then the archive contents will appear at the
	top level, otherwise, the archives contents will appear prefixed
	by path.
*/
FZ_FUNCTION void ll_fz_mount_multi_archive(::fz_archive *arch_, ::fz_archive *sub, const char *path);

/** Low-level wrapper for `::fz_moveto()`. */
/**
	Append a 'moveto' command to a path.
	This 'opens' a path.

	path: The path to modify.

	x, y: The coordinate to move to.

	Throws exceptions on failure to allocate, or attempting to
	modify a packed path.
*/
FZ_FUNCTION void ll_fz_moveto(::fz_path *path, float x, float y);

/** Low-level wrapper for `::fz_mul255()`. */
/**
	Multiply scaled two integers in the 0..255 range
*/
FZ_FUNCTION int ll_fz_mul255(int a, int b);

/** Low-level wrapper for `::fz_needs_password()`. */
/**
	Check if a document is encrypted with a
	non-blank password.
*/
FZ_FUNCTION int ll_fz_needs_password(::fz_document *doc);

/** Low-level wrapper for `::fz_new_arc4_output()`. */
FZ_FUNCTION ::fz_output *ll_fz_new_arc4_output(::fz_output *chain, unsigned char *key, size_t keylen);

/** Low-level wrapper for `::fz_new_archive_of_size()`. */
FZ_FUNCTION ::fz_archive *ll_fz_new_archive_of_size(::fz_stream *file, int size);

/** Low-level wrapper for `::fz_new_ascii85_output()`. */
FZ_FUNCTION ::fz_output *ll_fz_new_ascii85_output(::fz_output *chain);

/** Low-level wrapper for `::fz_new_asciihex_output()`. */
/**
	Compression and other filtering outputs.

	These outputs write encoded data to another output. Create a
	filter output with the destination, write to the filter, then
	close and drop it when you're done. These can also be chained
	together, for example to write ASCII Hex encoded, Deflate
	compressed, and RC4 encrypted data to a buffer output.

	Output streams don't use reference counting, so make sure to
	close all of the filters in the reverse order of creation so
	that data is flushed properly.

	Accordingly, ownership of 'chain' is never passed into the
	following functions, but remains with the caller, whose
	responsibility it is to ensure they exist at least until
	the returned fz_output is dropped.
*/
FZ_FUNCTION ::fz_output *ll_fz_new_asciihex_output(::fz_output *chain);

/** Low-level wrapper for `::fz_new_band_writer_of_size()`. */
FZ_FUNCTION ::fz_band_writer *ll_fz_new_band_writer_of_size(size_t size, ::fz_output *out);

/** Low-level wrapper for `::fz_new_base14_font()`. */
/**
	Create a new font from one of the built-in fonts.
*/
FZ_FUNCTION ::fz_font *ll_fz_new_base14_font(const char *name);

/** Low-level wrapper for `::fz_new_bbox_device()`. */
/**
	Create a device to compute the bounding
	box of all marks on a page.

	The returned bounding box will be the union of all bounding
	boxes of all objects on a page.
*/
FZ_FUNCTION ::fz_device *ll_fz_new_bbox_device(::fz_rect *rectp);

/** Low-level wrapper for `::fz_new_bitmap()`. */
/**
	Create a new bitmap.

	w, h: Width and Height for the bitmap

	n: Number of color components (assumed to be a divisor of 8)

	xres, yres: X and Y resolutions (in pixels per inch).

	Returns pointer to created bitmap structure. The bitmap
	data is uninitialised.
*/
FZ_FUNCTION ::fz_bitmap *ll_fz_new_bitmap(int w, int h, int n, int xres, int yres);

/** Low-level wrapper for `::fz_new_bitmap_from_pixmap()`. */
/**
	Make a bitmap from a pixmap and a halftone.

	pix: The pixmap to generate from. Currently must be a single
	color component with no alpha.

	ht: The halftone to use. NULL implies the default halftone.

	Returns the resultant bitmap. Throws exceptions in the case of
	failure to allocate.
*/
FZ_FUNCTION ::fz_bitmap *ll_fz_new_bitmap_from_pixmap(::fz_pixmap *pix, ::fz_halftone *ht);

/** Low-level wrapper for `::fz_new_bitmap_from_pixmap_band()`. */
/**
	Make a bitmap from a pixmap and a
	halftone, allowing for the position of the pixmap within an
	overall banded rendering.

	pix: The pixmap to generate from. Currently must be a single
	color component with no alpha.

	ht: The halftone to use. NULL implies the default halftone.

	band_start: Vertical offset within the overall banded rendering
	(in pixels)

	Returns the resultant bitmap. Throws exceptions in the case of
	failure to allocate.
*/
FZ_FUNCTION ::fz_bitmap *ll_fz_new_bitmap_from_pixmap_band(::fz_pixmap *pix, ::fz_halftone *ht, int band_start);

/** Low-level wrapper for `::fz_new_buffer()`. */
FZ_FUNCTION ::fz_buffer *ll_fz_new_buffer(size_t capacity);

/** Low-level wrapper for `::fz_new_buffer_from_base64()`. */
/**
	Create a new buffer with data decoded from a base64 input string.
*/
FZ_FUNCTION ::fz_buffer *ll_fz_new_buffer_from_base64(const char *data, size_t size);

/** Low-level wrapper for `::fz_new_buffer_from_copied_data()`. */
/**
	Create a new buffer containing a copy of the passed data.
*/
FZ_FUNCTION ::fz_buffer *ll_fz_new_buffer_from_copied_data(const unsigned char *data, size_t size);

/** Low-level wrapper for `::fz_new_buffer_from_data()`. */
/**
	Create a new buffer with existing data.

	data: Pointer to existing data.
	size: Size of existing data.

	Takes ownership of data. Does not make a copy. Calls fz_free on
	the data when the buffer is deallocated. Do not use 'data' after
	passing to this function.

	Returns pointer to new buffer. Throws exception on allocation
	failure.
*/
FZ_FUNCTION ::fz_buffer *ll_fz_new_buffer_from_data(unsigned char *data, size_t size);

/** Low-level wrapper for `::fz_new_buffer_from_display_list()`. */
FZ_FUNCTION ::fz_buffer *ll_fz_new_buffer_from_display_list(::fz_display_list *list, const ::fz_stext_options *options);

/** Low-level wrapper for `::fz_new_buffer_from_image_as_jpeg()`. */
FZ_FUNCTION ::fz_buffer *ll_fz_new_buffer_from_image_as_jpeg(::fz_image *image, ::fz_color_params color_params, int quality, int invert_cmyk);

/** Low-level wrapper for `::fz_new_buffer_from_image_as_jpx()`. */
FZ_FUNCTION ::fz_buffer *ll_fz_new_buffer_from_image_as_jpx(::fz_image *image, ::fz_color_params color_params, int quality);

/** Low-level wrapper for `::fz_new_buffer_from_image_as_pam()`. */
FZ_FUNCTION ::fz_buffer *ll_fz_new_buffer_from_image_as_pam(::fz_image *image, ::fz_color_params color_params);

/** Low-level wrapper for `::fz_new_buffer_from_image_as_png()`. */
/**
	Reencode a given image as a PNG into a buffer.

	Ownership of the buffer is returned.
*/
FZ_FUNCTION ::fz_buffer *ll_fz_new_buffer_from_image_as_png(::fz_image *image, ::fz_color_params color_params);

/** Low-level wrapper for `::fz_new_buffer_from_image_as_pnm()`. */
FZ_FUNCTION ::fz_buffer *ll_fz_new_buffer_from_image_as_pnm(::fz_image *image, ::fz_color_params color_params);

/** Low-level wrapper for `::fz_new_buffer_from_image_as_psd()`. */
FZ_FUNCTION ::fz_buffer *ll_fz_new_buffer_from_image_as_psd(::fz_image *image, ::fz_color_params color_params);

/** Low-level wrapper for `::fz_new_buffer_from_page()`. */
FZ_FUNCTION ::fz_buffer *ll_fz_new_buffer_from_page(::fz_page *page, const ::fz_stext_options *options);

/** Low-level wrapper for `::fz_new_buffer_from_page_number()`. */
FZ_FUNCTION ::fz_buffer *ll_fz_new_buffer_from_page_number(::fz_document *doc, int number, const ::fz_stext_options *options);

/** Low-level wrapper for `::fz_new_buffer_from_page_with_format()`. */
/**
	Returns an fz_buffer containing a page after conversion to specified format.

	page: The page to convert.
	format, options: Passed to fz_new_document_writer_with_output() internally.
	transform, cookie: Passed to fz_run_page() internally.
*/
FZ_FUNCTION ::fz_buffer *ll_fz_new_buffer_from_page_with_format(::fz_page *page, const char *format, const char *options, ::fz_matrix transform, ::fz_cookie *cookie);

/** Low-level wrapper for `::fz_new_buffer_from_pixmap_as_jpeg()`. */
FZ_FUNCTION ::fz_buffer *ll_fz_new_buffer_from_pixmap_as_jpeg(::fz_pixmap *pixmap, ::fz_color_params color_params, int quality, int invert_cmyk);

/** Low-level wrapper for `::fz_new_buffer_from_pixmap_as_jpx()`. */
FZ_FUNCTION ::fz_buffer *ll_fz_new_buffer_from_pixmap_as_jpx(::fz_pixmap *pix, ::fz_color_params color_params, int quality);

/** Low-level wrapper for `::fz_new_buffer_from_pixmap_as_pam()`. */
FZ_FUNCTION ::fz_buffer *ll_fz_new_buffer_from_pixmap_as_pam(::fz_pixmap *pixmap, ::fz_color_params color_params);

/** Low-level wrapper for `::fz_new_buffer_from_pixmap_as_png()`. */
/**
	Reencode a given pixmap as a PNG into a buffer.

	Ownership of the buffer is returned.
*/
FZ_FUNCTION ::fz_buffer *ll_fz_new_buffer_from_pixmap_as_png(::fz_pixmap *pixmap, ::fz_color_params color_params);

/** Low-level wrapper for `::fz_new_buffer_from_pixmap_as_pnm()`. */
FZ_FUNCTION ::fz_buffer *ll_fz_new_buffer_from_pixmap_as_pnm(::fz_pixmap *pixmap, ::fz_color_params color_params);

/** Low-level wrapper for `::fz_new_buffer_from_pixmap_as_psd()`. */
FZ_FUNCTION ::fz_buffer *ll_fz_new_buffer_from_pixmap_as_psd(::fz_pixmap *pix, ::fz_color_params color_params);

/** Low-level wrapper for `::fz_new_buffer_from_shared_data()`. */
/**
	Like fz_new_buffer, but does not take ownership.
*/
FZ_FUNCTION ::fz_buffer *ll_fz_new_buffer_from_shared_data(const unsigned char *data, size_t size);

/** Low-level wrapper for `::fz_new_buffer_from_stext_page()`. */
/**
	Convert structured text into plain text.
*/
FZ_FUNCTION ::fz_buffer *ll_fz_new_buffer_from_stext_page(::fz_stext_page *text);

/** Low-level wrapper for `::fz_new_builtin_font()`. */
FZ_FUNCTION ::fz_font *ll_fz_new_builtin_font(const char *name, int is_bold, int is_italic);

/** Low-level wrapper for `::fz_new_cal_gray_colorspace()`. */
/**
	Create a calibrated gray colorspace.

	The returned reference should be dropped when it is finished
	with.

	Colorspaces are immutable once created.
*/
FZ_FUNCTION ::fz_colorspace *ll_fz_new_cal_gray_colorspace(float wp[3], float bp[3], float gamma);

/** Low-level wrapper for `::fz_new_cal_rgb_colorspace()`. */
/**
	Create a calibrated rgb colorspace.

	The returned reference should be dropped when it is finished
	with.

	Colorspaces are immutable once created.
*/
FZ_FUNCTION ::fz_colorspace *ll_fz_new_cal_rgb_colorspace(float wp[3], float bp[3], float gamma[3], float matrix[9]);

/** Low-level wrapper for `::fz_new_cbz_writer()`. */
FZ_FUNCTION ::fz_document_writer *ll_fz_new_cbz_writer(const char *path, const char *options);

/** Low-level wrapper for `::fz_new_cbz_writer_with_output()`. */
FZ_FUNCTION ::fz_document_writer *ll_fz_new_cbz_writer_with_output(::fz_output *out, const char *options);

/** Low-level wrapper for `::fz_new_cjk_font()`. */
FZ_FUNCTION ::fz_font *ll_fz_new_cjk_font(int ordering);

/** Low-level wrapper for `::fz_new_color_pcl_band_writer()`. */
/**
	Create a new band writer, outputing color pcl.
*/
FZ_FUNCTION ::fz_band_writer *ll_fz_new_color_pcl_band_writer(::fz_output *out, const ::fz_pcl_options *options);

/** Low-level wrapper for `::fz_new_colorspace()`. */
/**
	Creates a new colorspace instance and returns a reference.

	No internal checking is done that the colorspace type (e.g.
	CMYK) matches with the flags (e.g. FZ_COLORSPACE_HAS_CMYK) or
	colorant count (n) or name.

	The reference should be dropped when it is finished with.

	Colorspaces are immutable once created (with the exception of
	setting up colorant names for separation spaces).
*/
FZ_FUNCTION ::fz_colorspace *ll_fz_new_colorspace(enum fz_colorspace_type type, int flags, int n, const char *name);

/** Low-level wrapper for `::fz_new_compressed_buffer()`. */
/**
	Create a new, UNKNOWN format, compressed_buffer.
*/
FZ_FUNCTION ::fz_compressed_buffer *ll_fz_new_compressed_buffer();

/** Low-level wrapper for `::fz_new_context_imp()`. */
FZ_FUNCTION ::fz_context *ll_fz_new_context_imp(const ::fz_alloc_context *alloc, const ::fz_locks_context *locks, size_t max_store, const char *version);

/** Low-level wrapper for `::fz_new_default_colorspaces()`. */
/**
	Create a new default colorspace structure with values inherited
	from the context, and return a reference to it.

	These can be overridden using fz_set_default_xxxx.

	These should not be overridden while more than one caller has
	the reference for fear of race conditions.

	The caller should drop this reference once finished with it.
*/
FZ_FUNCTION ::fz_default_colorspaces *ll_fz_new_default_colorspaces();

/** Low-level wrapper for `::fz_new_deflate_output()`. */
FZ_FUNCTION ::fz_output *ll_fz_new_deflate_output(::fz_output *chain, int effort, int raw);

/** Low-level wrapper for `::fz_new_deflated_data()`.

This function has out-params. Python/C# wrappers look like:
	`ll_fz_new_deflated_data(const unsigned char *source, size_t source_length, ::fz_deflate_level level)` => `(unsigned char *, size_t compressed_length)`
*/
/**
	Compress source_length bytes of data starting
	at source, into a new memory block malloced for that purpose.
	*compressed_length is updated on exit to contain the size used.
	Ownership of the block is returned from this function, and the
	caller is therefore responsible for freeing it. The block may be
	considerably larger than is actually required. The caller is
	free to fz_realloc it down if it wants to.
*/
FZ_FUNCTION unsigned char *ll_fz_new_deflated_data(size_t *compressed_length, const unsigned char *source, size_t source_length, ::fz_deflate_level level);

/** Low-level wrapper for `::fz_new_deflated_data_from_buffer()`.

This function has out-params. Python/C# wrappers look like:
	`ll_fz_new_deflated_data_from_buffer(::fz_buffer *buffer, ::fz_deflate_level level)` => `(unsigned char *, size_t compressed_length)`
*/
/**
	Compress the contents of a fz_buffer into a
	new block malloced for that purpose. *compressed_length is
	updated on exit to contain the size used. Ownership of the block
	is returned from this function, and the caller is therefore
	responsible for freeing it. The block may be considerably larger
	than is actually required. The caller is free to fz_realloc it
	down if it wants to.
*/
FZ_FUNCTION unsigned char *ll_fz_new_deflated_data_from_buffer(size_t *compressed_length, ::fz_buffer *buffer, ::fz_deflate_level level);

/** Low-level wrapper for `::fz_new_device_of_size()`. */
/**
	Devices are created by calls to device implementations, for
	instance: foo_new_device(). These will be implemented by calling
	fz_new_derived_device(ctx, foo_device) where foo_device is a
	structure "derived from" fz_device, for instance
	typedef struct { fz_device base;  ...extras...} foo_device;
*/
FZ_FUNCTION ::fz_device *ll_fz_new_device_of_size(int size);

/** Low-level wrapper for `::fz_new_display_list()`. */
/**
	Create an empty display list.

	A display list contains drawing commands (text, images, etc.).
	Use fz_new_list_device for populating the list.

	mediabox: Bounds of the page (in points) represented by the
	display list.
*/
FZ_FUNCTION ::fz_display_list *ll_fz_new_display_list(::fz_rect mediabox);

/** Low-level wrapper for `::fz_new_display_list_from_page()`. */
/**
	Create a display list.

	Ownership of the display list is returned to the caller.
*/
FZ_FUNCTION ::fz_display_list *ll_fz_new_display_list_from_page(::fz_page *page);

/** Low-level wrapper for `::fz_new_display_list_from_page_contents()`. */
/**
	Create a display list from page contents (no annotations).

	Ownership of the display list is returned to the caller.
*/
FZ_FUNCTION ::fz_display_list *ll_fz_new_display_list_from_page_contents(::fz_page *page);

/** Low-level wrapper for `::fz_new_display_list_from_page_number()`. */
FZ_FUNCTION ::fz_display_list *ll_fz_new_display_list_from_page_number(::fz_document *doc, int number);

/** Low-level wrapper for `::fz_new_display_list_from_svg()`.

This function has out-params. Python/C# wrappers look like:
	`ll_fz_new_display_list_from_svg(::fz_buffer *buf, const char *base_uri, ::fz_archive *dir)` => `(fz_display_list *, float w, float h)`
*/
/**
	Parse an SVG document into a display-list.
*/
FZ_FUNCTION ::fz_display_list *ll_fz_new_display_list_from_svg(::fz_buffer *buf, const char *base_uri, ::fz_archive *dir, float *w, float *h);

/** Low-level wrapper for `::fz_new_display_list_from_svg_xml()`.

This function has out-params. Python/C# wrappers look like:
	`ll_fz_new_display_list_from_svg_xml(::fz_xml_doc *xmldoc, ::fz_xml *xml, const char *base_uri, ::fz_archive *dir)` => `(fz_display_list *, float w, float h)`
*/
/**
	Parse an SVG document into a display-list.
*/
FZ_FUNCTION ::fz_display_list *ll_fz_new_display_list_from_svg_xml(::fz_xml_doc *xmldoc, ::fz_xml *xml, const char *base_uri, ::fz_archive *dir, float *w, float *h);

/** Low-level wrapper for `::fz_new_document_of_size()`. */
/**
	New documents are typically created by calls like
	foo_new_document(fz_context *ctx, ...). These work by
	deriving a new document type from fz_document, for instance:
	typedef struct { fz_document base; ...extras... } foo_document;
	These are allocated by calling
	fz_new_derived_document(ctx, foo_document)
*/
FZ_FUNCTION void *ll_fz_new_document_of_size(int size);

/** Low-level wrapper for `::fz_new_document_writer()`. */
/**
	Create a new fz_document_writer, for a
	file of the given type.

	path: The document name to write (or NULL for default)

	format: Which format to write (currently cbz, html, pdf, pam,
	pbm, pgm, pkm, png, ppm, pnm, svg, text, xhtml, docx, odt)

	options: NULL, or pointer to comma separated string to control
	file generation.
*/
FZ_FUNCTION ::fz_document_writer *ll_fz_new_document_writer(const char *path, const char *format, const char *options);

/** Low-level wrapper for `::fz_new_document_writer_of_size()`. */
/**
	Internal function to allocate a
	block for a derived document_writer structure, with the base
	structure's function pointers populated correctly, and the extra
	space zero initialised.
*/
FZ_FUNCTION ::fz_document_writer *ll_fz_new_document_writer_of_size(size_t size, ::fz_document_writer_begin_page_fn *begin_page, ::fz_document_writer_end_page_fn *end_page, ::fz_document_writer_close_writer_fn *close, ::fz_document_writer_drop_writer_fn *drop);

/** Low-level wrapper for `::fz_new_document_writer_with_buffer()`. */
FZ_FUNCTION ::fz_document_writer *ll_fz_new_document_writer_with_buffer(::fz_buffer *buf, const char *format, const char *options);

/** Low-level wrapper for `::fz_new_document_writer_with_output()`. */
/**
	Like fz_new_document_writer but takes a fz_output for writing
	the result. Only works for multi-page formats.
*/
FZ_FUNCTION ::fz_document_writer *ll_fz_new_document_writer_with_output(::fz_output *out, const char *format, const char *options);

/** Low-level wrapper for `::fz_new_docx_writer()`. */
FZ_FUNCTION ::fz_document_writer *ll_fz_new_docx_writer(const char *path, const char *options);

/** Low-level wrapper for `::fz_new_docx_writer_with_output()`. */
FZ_FUNCTION ::fz_document_writer *ll_fz_new_docx_writer_with_output(::fz_output *out, const char *options);

/** Low-level wrapper for `::fz_new_draw_device()`. */
/**
	Create a device to draw on a pixmap.

	dest: Target pixmap for the draw device. See fz_new_pixmap*
	for how to obtain a pixmap. The pixmap is not cleared by the
	draw device, see fz_clear_pixmap* for how to clear it prior to
	calling fz_new_draw_device. Free the device by calling
	fz_drop_device.

	transform: Transform from user space in points to device space
	in pixels.
*/
FZ_FUNCTION ::fz_device *ll_fz_new_draw_device(::fz_matrix transform, ::fz_pixmap *dest);

/** Low-level wrapper for `::fz_new_draw_device_type3()`. */
FZ_FUNCTION ::fz_device *ll_fz_new_draw_device_type3(::fz_matrix transform, ::fz_pixmap *dest);

/** Low-level wrapper for `::fz_new_draw_device_with_bbox()`. */
/**
	Create a device to draw on a pixmap.

	dest: Target pixmap for the draw device. See fz_new_pixmap*
	for how to obtain a pixmap. The pixmap is not cleared by the
	draw device, see fz_clear_pixmap* for how to clear it prior to
	calling fz_new_draw_device. Free the device by calling
	fz_drop_device.

	transform: Transform from user space in points to device space
	in pixels.

	clip: Bounding box to restrict any marking operations of the
	draw device.
*/
FZ_FUNCTION ::fz_device *ll_fz_new_draw_device_with_bbox(::fz_matrix transform, ::fz_pixmap *dest, const ::fz_irect *clip);

/** Low-level wrapper for `::fz_new_draw_device_with_bbox_proof()`. */
/**
	Create a device to draw on a pixmap.

	dest: Target pixmap for the draw device. See fz_new_pixmap*
	for how to obtain a pixmap. The pixmap is not cleared by the
	draw device, see fz_clear_pixmap* for how to clear it prior to
	calling fz_new_draw_device. Free the device by calling
	fz_drop_device.

	transform: Transform from user space in points to device space
	in pixels.

	clip: Bounding box to restrict any marking operations of the
	draw device.

	proof_cs: Color space to render to prior to mapping to color
	space defined by pixmap.
*/
FZ_FUNCTION ::fz_device *ll_fz_new_draw_device_with_bbox_proof(::fz_matrix transform, ::fz_pixmap *dest, const ::fz_irect *clip, ::fz_colorspace *cs);

/** Low-level wrapper for `::fz_new_draw_device_with_options()`.

This function has out-params. Python/C# wrappers look like:
	`ll_fz_new_draw_device_with_options(const ::fz_draw_options *options, ::fz_rect mediabox, ::fz_pixmap **pixmap)` => `(fz_device *)`
*/
/**
	Create a new pixmap and draw device, using the specified options.

	options: Options to configure the draw device, and choose the
	resolution and colorspace.

	mediabox: The bounds of the page in points.

	pixmap: An out parameter containing the newly created pixmap.
*/
FZ_FUNCTION ::fz_device *ll_fz_new_draw_device_with_options(const ::fz_draw_options *options, ::fz_rect mediabox, ::fz_pixmap **pixmap);

/** Low-level wrapper for `::fz_new_draw_device_with_proof()`. */
/**
	Create a device to draw on a pixmap.

	dest: Target pixmap for the draw device. See fz_new_pixmap*
	for how to obtain a pixmap. The pixmap is not cleared by the
	draw device, see fz_clear_pixmap* for how to clear it prior to
	calling fz_new_draw_device. Free the device by calling
	fz_drop_device.

	transform: Transform from user space in points to device space
	in pixels.

	proof_cs: Intermediate color space to map though when mapping to
	color space defined by pixmap.
*/
FZ_FUNCTION ::fz_device *ll_fz_new_draw_device_with_proof(::fz_matrix transform, ::fz_pixmap *dest, ::fz_colorspace *proof_cs);

/** Low-level wrapper for `::fz_new_font_from_buffer()`. */
/**
	Create a new font from a font file in a fz_buffer.

	Fonts created in this way, will be eligible for embedding by default.

	name: Name of font (leave NULL to use name from font).

	buffer: Buffer to load from.

	index: Which font from the file to load (0 for default).

	use_glyph_box: 1 if we should use the glyph bbox, 0 otherwise.

	Returns new font handle, or throws exception on error.
*/
FZ_FUNCTION ::fz_font *ll_fz_new_font_from_buffer(const char *name, ::fz_buffer *buffer, int index, int use_glyph_bbox);

/** Low-level wrapper for `::fz_new_font_from_file()`. */
/**
	Create a new font from a font file.

	Fonts created in this way, will be eligible for embedding by default.

	name: Name of font (leave NULL to use name from font).

	path: File path to load from.

	index: Which font from the file to load (0 for default).

	use_glyph_box: 1 if we should use the glyph bbox, 0 otherwise.

	Returns new font handle, or throws exception on error.
*/
FZ_FUNCTION ::fz_font *ll_fz_new_font_from_file(const char *name, const char *path, int index, int use_glyph_bbox);

/** Low-level wrapper for `::fz_new_font_from_memory()`. */
/**
	Create a new font from a font file in memory.

	Fonts created in this way, will be eligible for embedding by default.

	name: Name of font (leave NULL to use name from font).

	data: Pointer to the font file data.

	len: Length of the font file data.

	index: Which font from the file to load (0 for default).

	use_glyph_box: 1 if we should use the glyph bbox, 0 otherwise.

	Returns new font handle, or throws exception on error.
*/
FZ_FUNCTION ::fz_font *ll_fz_new_font_from_memory(const char *name, const unsigned char *data, int len, int index, int use_glyph_bbox);

/** Low-level wrapper for `::fz_new_function_of_size()`. */
FZ_FUNCTION ::fz_function *ll_fz_new_function_of_size(int size, size_t size2, int m, int n, ::fz_function_eval_fn *eval, ::fz_store_drop_fn *drop);

/** Low-level wrapper for `::fz_new_hash_table()`. */
/**
	Create a new hash table.

	initialsize: The initial size of the hashtable. The hashtable
	may grow (double in size) if it starts to get crowded (80%
	full).

	keylen: byte length for each key.

	lock: -1 for no lock, otherwise the FZ_LOCK to use to protect
	this table.

	drop_val: Function to use to destroy values on table drop.
*/
FZ_FUNCTION ::fz_hash_table *ll_fz_new_hash_table(int initialsize, int keylen, int lock, ::fz_hash_table_drop_fn *drop_val);

/** Low-level wrapper for `::fz_new_icc_colorspace()`. */
/**
	Create a colorspace from an ICC profile supplied in buf.

	Limited checking is done to ensure that the colorspace type is
	appropriate for the supplied ICC profile.

	An additional reference is taken to buf, which will be dropped
	on destruction. Ownership is NOT passed in.

	The returned reference should be dropped when it is finished
	with.

	Colorspaces are immutable once created.
*/
FZ_FUNCTION ::fz_colorspace *ll_fz_new_icc_colorspace(enum fz_colorspace_type type, int flags, const char *name, ::fz_buffer *buf);

/** Low-level wrapper for `::fz_new_image_from_buffer()`. */
/**
	Create a new image from a
	buffer of data, inferring its type from the format
	of the data.
*/
FZ_FUNCTION ::fz_image *ll_fz_new_image_from_buffer(::fz_buffer *buffer);

/** Low-level wrapper for `::fz_new_image_from_compressed_buffer()`. */
/**
	Create an image based on
	the data in the supplied compressed buffer.

	w,h: Width and height of the created image.

	bpc: Bits per component.

	colorspace: The colorspace (determines the number of components,
	and any color conversions required while decoding).

	xres, yres: The X and Y resolutions respectively.

	interpolate: 1 if interpolation should be used when decoding
	this image, 0 otherwise.

	imagemask: 1 if this is an imagemask (i.e. transparency bitmap
	mask), 0 otherwise.

	decode: NULL, or a pointer to to a decode array. The default
	decode array is [0 1] (repeated n times, for n color components).

	colorkey: NULL, or a pointer to a colorkey array. The default
	colorkey array is [0 255] (repeated n times, for n color
	components).

	buffer: Buffer of compressed data and compression parameters.
	Ownership of this reference is passed in.

	mask: NULL, or another image to use as a mask for this one.
	A new reference is taken to this image. Supplying a masked
	image as a mask to another image is illegal!
*/
FZ_FUNCTION ::fz_image *ll_fz_new_image_from_compressed_buffer(int w, int h, int bpc, ::fz_colorspace *colorspace, int xres, int yres, int interpolate, int imagemask, const float *decode, const int *colorkey, ::fz_compressed_buffer *buffer, ::fz_image *mask);

/** Low-level wrapper for `::fz_new_image_from_compressed_buffer2()`. */
/** Swig-friendly wrapper for fz_new_image_from_compressed_buffer(),
uses specified `decode` and `colorkey` if they are not null (in which
case we assert that they have size `2*fz_colorspace_n(colorspace)`). */
FZ_FUNCTION ::fz_image *ll_fz_new_image_from_compressed_buffer2(int w, int h, int bpc, ::fz_colorspace *colorspace, int xres, int yres, int interpolate, int imagemask, const std::vector<float> &decode, const std::vector<int> &colorkey, ::fz_compressed_buffer *buffer, ::fz_image *mask);

/** Low-level wrapper for `::fz_new_image_from_display_list()`. */
/**
	Create a new image from a display list.

	w, h: The conceptual width/height of the image.

	transform: The matrix that needs to be applied to the given
	list to make it render to the unit square.

	list: The display list.
*/
FZ_FUNCTION ::fz_image *ll_fz_new_image_from_display_list(float w, float h, ::fz_display_list *list);

/** Low-level wrapper for `::fz_new_image_from_file()`. */
/**
	Create a new image from the contents
	of a file, inferring its type from the format of the
	data.
*/
FZ_FUNCTION ::fz_image *ll_fz_new_image_from_file(const char *path);

/** Low-level wrapper for `::fz_new_image_from_pixmap()`. */
/**
	Create an image from the given
	pixmap.

	pixmap: The pixmap to base the image upon. A new reference
	to this is taken.

	mask: NULL, or another image to use as a mask for this one.
	A new reference is taken to this image. Supplying a masked
	image as a mask to another image is illegal!
*/
FZ_FUNCTION ::fz_image *ll_fz_new_image_from_pixmap(::fz_pixmap *pixmap, ::fz_image *mask);

/** Low-level wrapper for `::fz_new_image_from_svg()`. */
/**
	Create a scalable image from an SVG document.
*/
FZ_FUNCTION ::fz_image *ll_fz_new_image_from_svg(::fz_buffer *buf, const char *base_uri, ::fz_archive *dir);

/** Low-level wrapper for `::fz_new_image_from_svg_xml()`. */
/**
	Create a scalable image from an SVG document.
*/
FZ_FUNCTION ::fz_image *ll_fz_new_image_from_svg_xml(::fz_xml_doc *xmldoc, ::fz_xml *xml, const char *base_uri, ::fz_archive *dir);

/** Low-level wrapper for `::fz_new_image_of_size()`. */
/**
	Internal function to make a new fz_image structure
	for a derived class.

	w,h: Width and height of the created image.

	bpc: Bits per component.

	colorspace: The colorspace (determines the number of components,
	and any color conversions required while decoding).

	xres, yres: The X and Y resolutions respectively.

	interpolate: 1 if interpolation should be used when decoding
	this image, 0 otherwise.

	imagemask: 1 if this is an imagemask (i.e. transparent), 0
	otherwise.

	decode: NULL, or a pointer to to a decode array. The default
	decode array is [0 1] (repeated n times, for n color components).

	colorkey: NULL, or a pointer to a colorkey array. The default
	colorkey array is [0 255] (repeated n times, for n color
	components).

	mask: NULL, or another image to use as a mask for this one.
	A new reference is taken to this image. Supplying a masked
	image as a mask to another image is illegal!

	size: The size of the required allocated structure (the size of
	the derived structure).

	get: The function to be called to obtain a decoded pixmap.

	get_size: The function to be called to return the storage size
	used by this image.

	drop: The function to be called to dispose of this image once
	the last reference is dropped.

	Returns a pointer to an allocated structure of the required size,
	with the first sizeof(fz_image) bytes initialised as appropriate
	given the supplied parameters, and the other bytes set to zero.
*/
FZ_FUNCTION ::fz_image *ll_fz_new_image_of_size(int w, int h, int bpc, ::fz_colorspace *colorspace, int xres, int yres, int interpolate, int imagemask, const float *decode, const int *colorkey, ::fz_image *mask, size_t size, ::fz_image_get_pixmap_fn *get_pixmap, ::fz_image_get_size_fn *get_size, ::fz_drop_image_fn *drop);

/** Low-level wrapper for `::fz_new_indexed_colorspace()`. */
/**
	Create an indexed colorspace.

	The supplied lookup table is high palette entries long. Each
	entry is n bytes long, where n is given by the number of
	colorants in the base colorspace, one byte per colorant.

	Ownership of lookup is passed it; it will be freed on
	destruction, so must be heap allocated.

	The colorspace will keep an additional reference to the base
	colorspace that will be dropped on destruction.

	The returned reference should be dropped when it is finished
	with.

	Colorspaces are immutable once created.
*/
FZ_FUNCTION ::fz_colorspace *ll_fz_new_indexed_colorspace(::fz_colorspace *base, int high, unsigned char *lookup);

/** Low-level wrapper for `::fz_new_jpeg_pixmap_writer()`. */
FZ_FUNCTION ::fz_document_writer *ll_fz_new_jpeg_pixmap_writer(const char *path, const char *options);

/** Low-level wrapper for `::fz_new_layout()`. */
/**
	Create a new layout block, with new allocation pool, zero
	matrices, and initialise linked pointers.
*/
FZ_FUNCTION ::fz_layout_block *ll_fz_new_layout();

/** Low-level wrapper for `::fz_new_link_of_size()`. */
/**
	Create a new link record.

	next is set to NULL with the expectation that the caller will
	handle the linked list setup. Internal function.

	Different document types will be implemented by deriving from
	fz_link. This macro allocates such derived structures, and
	initialises the base sections.
*/
FZ_FUNCTION ::fz_link *ll_fz_new_link_of_size(int size, ::fz_rect rect, const char *uri);

/** Low-level wrapper for `::fz_new_list_device()`. */
/**
	Create a rendering device for a display list.

	When the device is rendering a page it will populate the
	display list with drawing commands (text, images, etc.). The
	display list can later be reused to render a page many times
	without having to re-interpret the page from the document file
	for each rendering. Once the device is no longer needed, free
	it with fz_drop_device.

	list: A display list that the list device takes a reference to.
*/
FZ_FUNCTION ::fz_device *ll_fz_new_list_device(::fz_display_list *list);

/** Low-level wrapper for `::fz_new_log_for_module()`. */
/**
	Internal function to actually do the opening of the logfile.

	Caller should close/drop the output when finished with it.
*/
FZ_FUNCTION ::fz_output *ll_fz_new_log_for_module(const char *module);

/** Low-level wrapper for `::fz_new_mono_pcl_band_writer()`. */
/**
	Create a new band writer, outputing monochrome pcl.
*/
FZ_FUNCTION ::fz_band_writer *ll_fz_new_mono_pcl_band_writer(::fz_output *out, const ::fz_pcl_options *options);

/** Low-level wrapper for `::fz_new_mono_pwg_band_writer()`. */
/**
	Create a new monochrome pwg band writer.
*/
FZ_FUNCTION ::fz_band_writer *ll_fz_new_mono_pwg_band_writer(::fz_output *out, const ::fz_pwg_options *pwg);

/** Low-level wrapper for `::fz_new_multi_archive()`. */
/**
	Create a new multi archive (initially empty).
*/
FZ_FUNCTION ::fz_archive *ll_fz_new_multi_archive();

/** Low-level wrapper for `::fz_new_ocr_device()`. */
/**
	Create a device to OCR the text on the page.

	Renders the page internally to a bitmap that is then OCRd. Text
	is then forwarded onto the target device.

	target: The target device to receive the OCRd text.

	ctm: The transform to apply to the mediabox to get the size for
	the rendered page image. Also used to calculate the resolution
	for the page image. In general, this will be the same as the CTM
	that you pass to fz_run_page (or fz_run_display_list) to feed
	this device.

	mediabox: The mediabox (in points). Combined with the CTM to get
	the bounds of the pixmap used internally for the rendered page
	image.

	with_list: If with_list is false, then all non-text operations
	are forwarded instantly to the target device. This results in
	the target device seeing all NON-text operations, followed by
	all the text operations (derived from OCR).

	If with_list is true, then all the marking operations are
	collated into a display list which is then replayed to the
	target device at the end.

	language: NULL (for "eng"), or a pointer to a string to describe
	the languages/scripts that should be used for OCR (e.g.
	"eng,ara").

	datadir: NULL (for ""), or a pointer to a path string otherwise
	provided to Tesseract in the TESSDATA_PREFIX environment variable.

	progress: NULL, or function to be called periodically to indicate
	progress. Return 0 to continue, or 1 to cancel. progress_arg is
	returned as the void *. The int is a value between 0 and 100 to
	indicate progress.

	progress_arg: A void * value to be parrotted back to the progress
	function.
*/
FZ_FUNCTION ::fz_device *ll_fz_new_ocr_device(::fz_device *target, ::fz_matrix ctm, ::fz_rect mediabox, int with_list, const char *language, const char *datadir, int (*progress)(::fz_context *, void *, int ), void *progress_arg);

/** Low-level wrapper for `::fz_new_odt_writer()`. */
FZ_FUNCTION ::fz_document_writer *ll_fz_new_odt_writer(const char *path, const char *options);

/** Low-level wrapper for `::fz_new_odt_writer_with_output()`. */
FZ_FUNCTION ::fz_document_writer *ll_fz_new_odt_writer_with_output(::fz_output *out, const char *options);

/** Low-level wrapper for `::fz_new_outline()`. */
/**
	Create a new outline entry with zeroed fields for the caller
	to fill in.
*/
FZ_FUNCTION ::fz_outline *ll_fz_new_outline();

/** Low-level wrapper for `::fz_new_outline_iterator()`. */
/**
	Get an iterator for the document outline.

	Should be freed by fz_drop_outline_iterator.
*/
FZ_FUNCTION ::fz_outline_iterator *ll_fz_new_outline_iterator(::fz_document *doc);

/** Low-level wrapper for `::fz_new_outline_iterator_of_size()`. */
FZ_FUNCTION ::fz_outline_iterator *ll_fz_new_outline_iterator_of_size(size_t size, ::fz_document *doc);

/** Low-level wrapper for `::fz_new_output()`. */
/**
	Create a new output object with the given
	internal state and function pointers.

	state: Internal state (opaque to everything but implementation).

	write: Function to output a given buffer.

	close: Cleanup function to destroy state when output closed.
	May permissibly be null.
*/
FZ_FUNCTION ::fz_output *ll_fz_new_output(int bufsiz, void *state, ::fz_output_write_fn *write, ::fz_output_close_fn *close, ::fz_output_drop_fn *drop);

/** Low-level wrapper for `::fz_new_output_with_buffer()`. */
/**
	Open an output stream that appends
	to a buffer.

	buf: The buffer to append to.
*/
FZ_FUNCTION ::fz_output *ll_fz_new_output_with_buffer(::fz_buffer *buf);

/** Low-level wrapper for `::fz_new_output_with_file_ptr()`. */
/**
	Open an output stream that writes to a
	given FILE *.

	file: The file pointers to write to. NULL is interpreted as effectively
	meaning /dev/null or similar.
*/
FZ_FUNCTION ::fz_output *ll_fz_new_output_with_file_ptr(FILE *file);

/** Low-level wrapper for `::fz_new_output_with_path()`. */
/**
	Open an output stream that writes to a
	given path.

	filename: The filename to write to (specified in UTF-8).

	append: non-zero if we should append to the file, rather than
	overwriting it.
*/
FZ_FUNCTION ::fz_output *ll_fz_new_output_with_path(const char *filename, int append);

/** Low-level wrapper for `::fz_new_page_of_size()`. */
/**
	Different document types will be implemented by deriving from
	fz_page. This macro allocates such derived structures, and
	initialises the base sections.
*/
FZ_FUNCTION ::fz_page *ll_fz_new_page_of_size(int size, ::fz_document *doc);

/** Low-level wrapper for `::fz_new_pam_band_writer()`. */
/**
	Create a band writer targetting pnm (greyscale, rgb or cmyk,
	with or without alpha).
*/
FZ_FUNCTION ::fz_band_writer *ll_fz_new_pam_band_writer(::fz_output *out);

/** Low-level wrapper for `::fz_new_pam_pixmap_writer()`. */
FZ_FUNCTION ::fz_document_writer *ll_fz_new_pam_pixmap_writer(const char *path, const char *options);

/** Low-level wrapper for `::fz_new_path()`. */
/**
	Create a new (empty) path structure.
*/
FZ_FUNCTION ::fz_path *ll_fz_new_path();

/** Low-level wrapper for `::fz_new_pbm_band_writer()`. */
/**
	Create a new band writer, targetting pbm.
*/
FZ_FUNCTION ::fz_band_writer *ll_fz_new_pbm_band_writer(::fz_output *out);

/** Low-level wrapper for `::fz_new_pbm_pixmap_writer()`. */
FZ_FUNCTION ::fz_document_writer *ll_fz_new_pbm_pixmap_writer(const char *path, const char *options);

/** Low-level wrapper for `::fz_new_pcl_writer()`. */
FZ_FUNCTION ::fz_document_writer *ll_fz_new_pcl_writer(const char *path, const char *options);

/** Low-level wrapper for `::fz_new_pcl_writer_with_output()`. */
FZ_FUNCTION ::fz_document_writer *ll_fz_new_pcl_writer_with_output(::fz_output *out, const char *options);

/** Low-level wrapper for `::fz_new_pclm_band_writer()`. */
/**
	Create a new band writer, outputing pclm
*/
FZ_FUNCTION ::fz_band_writer *ll_fz_new_pclm_band_writer(::fz_output *out, const ::fz_pclm_options *options);

/** Low-level wrapper for `::fz_new_pclm_writer()`. */
FZ_FUNCTION ::fz_document_writer *ll_fz_new_pclm_writer(const char *path, const char *options);

/** Low-level wrapper for `::fz_new_pclm_writer_with_output()`. */
FZ_FUNCTION ::fz_document_writer *ll_fz_new_pclm_writer_with_output(::fz_output *out, const char *options);

/** Low-level wrapper for `::fz_new_pdf_document_from_fz_document()`. */
FZ_FUNCTION ::pdf_document *ll_fz_new_pdf_document_from_fz_document(::fz_document *ptr);

/** Low-level wrapper for `::fz_new_pdf_writer()`. */
/**
	Document writers for various possible output formats.

	All of the "_with_output" variants pass the ownership of out in
	immediately upon calling. The writers are responsible for
	dropping the fz_output when they are finished with it (even
	if they throw an exception during creation).
*/
FZ_FUNCTION ::fz_document_writer *ll_fz_new_pdf_writer(const char *path, const char *options);

/** Low-level wrapper for `::fz_new_pdf_writer_with_output()`. */
FZ_FUNCTION ::fz_document_writer *ll_fz_new_pdf_writer_with_output(::fz_output *out, const char *options);

/** Low-level wrapper for `::fz_new_pdfocr_band_writer()`. */
/**
	Create a new band writer, outputing pdfocr.

	Ownership of output stays with the caller, the band writer
	borrows the reference. The caller must keep the output around
	for the duration of the band writer, and then close/drop as
	appropriate.
*/
FZ_FUNCTION ::fz_band_writer *ll_fz_new_pdfocr_band_writer(::fz_output *out, const ::fz_pdfocr_options *options);

/** Low-level wrapper for `::fz_new_pdfocr_writer()`. */
FZ_FUNCTION ::fz_document_writer *ll_fz_new_pdfocr_writer(const char *path, const char *options);

/** Low-level wrapper for `::fz_new_pdfocr_writer_with_output()`. */
FZ_FUNCTION ::fz_document_writer *ll_fz_new_pdfocr_writer_with_output(::fz_output *out, const char *options);

/** Low-level wrapper for `::fz_new_pgm_pixmap_writer()`. */
FZ_FUNCTION ::fz_document_writer *ll_fz_new_pgm_pixmap_writer(const char *path, const char *options);

/** Low-level wrapper for `::fz_new_pixmap()`. */
/**
	Create a new pixmap, with its origin at (0,0)

	cs: The colorspace to use for the pixmap, or NULL for an alpha
	plane/mask.

	w: The width of the pixmap (in pixels)

	h: The height of the pixmap (in pixels)

	seps: Details of separations.

	alpha: 0 for no alpha, 1 for alpha.

	Returns a pointer to the new pixmap. Throws exception on failure
	to allocate.
*/
FZ_FUNCTION ::fz_pixmap *ll_fz_new_pixmap(::fz_colorspace *cs, int w, int h, ::fz_separations *seps, int alpha);

/** Low-level wrapper for `::fz_new_pixmap_from_alpha_channel()`. */
FZ_FUNCTION ::fz_pixmap *ll_fz_new_pixmap_from_alpha_channel(::fz_pixmap *src);

/** Low-level wrapper for `::fz_new_pixmap_from_color_and_mask()`. */
FZ_FUNCTION ::fz_pixmap *ll_fz_new_pixmap_from_color_and_mask(::fz_pixmap *color, ::fz_pixmap *mask);

/** Low-level wrapper for `::fz_new_pixmap_from_display_list()`. */
/**
	Render the page to a pixmap using the transform and colorspace.

	Ownership of the pixmap is returned to the caller.
*/
FZ_FUNCTION ::fz_pixmap *ll_fz_new_pixmap_from_display_list(::fz_display_list *list, ::fz_matrix ctm, ::fz_colorspace *cs, int alpha);

/** Low-level wrapper for `::fz_new_pixmap_from_display_list_with_separations()`. */
/**
	Render the page contents with control over spot colors.

	Ownership of the pixmap is returned to the caller.
*/
FZ_FUNCTION ::fz_pixmap *ll_fz_new_pixmap_from_display_list_with_separations(::fz_display_list *list, ::fz_matrix ctm, ::fz_colorspace *cs, ::fz_separations *seps, int alpha);

/** Low-level wrapper for `::fz_new_pixmap_from_page()`. */
FZ_FUNCTION ::fz_pixmap *ll_fz_new_pixmap_from_page(::fz_page *page, ::fz_matrix ctm, ::fz_colorspace *cs, int alpha);

/** Low-level wrapper for `::fz_new_pixmap_from_page_contents()`. */
/**
	Render the page contents without annotations.

	Ownership of the pixmap is returned to the caller.
*/
FZ_FUNCTION ::fz_pixmap *ll_fz_new_pixmap_from_page_contents(::fz_page *page, ::fz_matrix ctm, ::fz_colorspace *cs, int alpha);

/** Low-level wrapper for `::fz_new_pixmap_from_page_contents_with_separations()`. */
FZ_FUNCTION ::fz_pixmap *ll_fz_new_pixmap_from_page_contents_with_separations(::fz_page *page, ::fz_matrix ctm, ::fz_colorspace *cs, ::fz_separations *seps, int alpha);

/** Low-level wrapper for `::fz_new_pixmap_from_page_number()`. */
FZ_FUNCTION ::fz_pixmap *ll_fz_new_pixmap_from_page_number(::fz_document *doc, int number, ::fz_matrix ctm, ::fz_colorspace *cs, int alpha);

/** Low-level wrapper for `::fz_new_pixmap_from_page_number_with_separations()`. */
FZ_FUNCTION ::fz_pixmap *ll_fz_new_pixmap_from_page_number_with_separations(::fz_document *doc, int number, ::fz_matrix ctm, ::fz_colorspace *cs, ::fz_separations *seps, int alpha);

/** Low-level wrapper for `::fz_new_pixmap_from_page_with_separations()`. */
FZ_FUNCTION ::fz_pixmap *ll_fz_new_pixmap_from_page_with_separations(::fz_page *page, ::fz_matrix ctm, ::fz_colorspace *cs, ::fz_separations *seps, int alpha);

/** Low-level wrapper for `::fz_new_pixmap_from_pixmap()`. */
/**
	Create a new pixmap that represents a subarea of the specified
	pixmap. A reference is taken to this pixmap that will be dropped
	on destruction.

	The supplied rectangle must be wholly contained within the
	original pixmap.

	Returns a pointer to the new pixmap. Throws exception on failure
	to allocate.
*/
FZ_FUNCTION ::fz_pixmap *ll_fz_new_pixmap_from_pixmap(::fz_pixmap *pixmap, const ::fz_irect *rect);

/** Low-level wrapper for `::fz_new_pixmap_with_bbox()`. */
/**
	Create a pixmap of a given size, location and pixel format.

	The bounding box specifies the size of the created pixmap and
	where it will be located. The colorspace determines the number
	of components per pixel. Alpha is always present. Pixmaps are
	reference counted, so drop references using fz_drop_pixmap.

	colorspace: Colorspace format used for the created pixmap. The
	pixmap will keep a reference to the colorspace.

	bbox: Bounding box specifying location/size of created pixmap.

	seps: Details of separations.

	alpha: 0 for no alpha, 1 for alpha.

	Returns a pointer to the new pixmap. Throws exception on failure
	to allocate.
*/
FZ_FUNCTION ::fz_pixmap *ll_fz_new_pixmap_with_bbox(::fz_colorspace *colorspace, ::fz_irect bbox, ::fz_separations *seps, int alpha);

/** Low-level wrapper for `::fz_new_pixmap_with_bbox_and_data()`. */
/**
	Create a pixmap of a given size, location and pixel format,
	using the supplied data block.

	The bounding box specifies the size of the created pixmap and
	where it will be located. The colorspace determines the number
	of components per pixel. Alpha is always present. Pixmaps are
	reference counted, so drop references using fz_drop_pixmap.

	colorspace: Colorspace format used for the created pixmap. The
	pixmap will keep a reference to the colorspace.

	rect: Bounding box specifying location/size of created pixmap.

	seps: Details of separations.

	alpha: Number of alpha planes (0 or 1).

	samples: The data block to keep the samples in.

	Returns a pointer to the new pixmap. Throws exception on failure
	to allocate.
*/
FZ_FUNCTION ::fz_pixmap *ll_fz_new_pixmap_with_bbox_and_data(::fz_colorspace *colorspace, ::fz_irect rect, ::fz_separations *seps, int alpha, unsigned char *samples);

/** Low-level wrapper for `::fz_new_pixmap_with_data()`. */
/**
	Create a new pixmap, with its origin at
	(0,0) using the supplied data block.

	cs: The colorspace to use for the pixmap, or NULL for an alpha
	plane/mask.

	w: The width of the pixmap (in pixels)

	h: The height of the pixmap (in pixels)

	seps: Details of separations.

	alpha: 0 for no alpha, 1 for alpha.

	stride: The byte offset from the pixel data in a row to the
	pixel data in the next row.

	samples: The data block to keep the samples in.

	Returns a pointer to the new pixmap. Throws exception on failure to
	allocate.
*/
FZ_FUNCTION ::fz_pixmap *ll_fz_new_pixmap_with_data(::fz_colorspace *colorspace, int w, int h, ::fz_separations *seps, int alpha, int stride, unsigned char *samples);

/** Low-level wrapper for `::fz_new_pixmap_writer()`. */
FZ_FUNCTION ::fz_document_writer *ll_fz_new_pixmap_writer(const char *path, const char *options, const char *default_path, int n, void (*save)(::fz_context *, ::fz_pixmap *, const char *));

/** Low-level wrapper for `::fz_new_pkm_band_writer()`. */
/**
	Create a new pkm band writer for CMYK pixmaps.
*/
FZ_FUNCTION ::fz_band_writer *ll_fz_new_pkm_band_writer(::fz_output *out);

/** Low-level wrapper for `::fz_new_pkm_pixmap_writer()`. */
FZ_FUNCTION ::fz_document_writer *ll_fz_new_pkm_pixmap_writer(const char *path, const char *options);

/** Low-level wrapper for `::fz_new_png_band_writer()`. */
/**
	Create a new png band writer (greyscale or RGB, with or without
	alpha).
*/
FZ_FUNCTION ::fz_band_writer *ll_fz_new_png_band_writer(::fz_output *out);

/** Low-level wrapper for `::fz_new_png_pixmap_writer()`. */
FZ_FUNCTION ::fz_document_writer *ll_fz_new_png_pixmap_writer(const char *path, const char *options);

/** Low-level wrapper for `::fz_new_pnm_band_writer()`. */
/**
	Create a band writer targetting pnm (greyscale or rgb, no
	alpha).
*/
FZ_FUNCTION ::fz_band_writer *ll_fz_new_pnm_band_writer(::fz_output *out);

/** Low-level wrapper for `::fz_new_pnm_pixmap_writer()`. */
FZ_FUNCTION ::fz_document_writer *ll_fz_new_pnm_pixmap_writer(const char *path, const char *options);

/** Low-level wrapper for `::fz_new_pool()`. */
/**
	Create a new pool to allocate from.
*/
FZ_FUNCTION ::fz_pool *ll_fz_new_pool();

/** Low-level wrapper for `::fz_new_ppm_pixmap_writer()`. */
FZ_FUNCTION ::fz_document_writer *ll_fz_new_ppm_pixmap_writer(const char *path, const char *options);

/** Low-level wrapper for `::fz_new_ps_band_writer()`. */
/**
	Create a postscript band writer for gray, rgb, or cmyk, no
	alpha.
*/
FZ_FUNCTION ::fz_band_writer *ll_fz_new_ps_band_writer(::fz_output *out);

/** Low-level wrapper for `::fz_new_ps_writer()`. */
FZ_FUNCTION ::fz_document_writer *ll_fz_new_ps_writer(const char *path, const char *options);

/** Low-level wrapper for `::fz_new_ps_writer_with_output()`. */
FZ_FUNCTION ::fz_document_writer *ll_fz_new_ps_writer_with_output(::fz_output *out, const char *options);

/** Low-level wrapper for `::fz_new_psd_band_writer()`. */
/**
	Open a PSD band writer.
*/
FZ_FUNCTION ::fz_band_writer *ll_fz_new_psd_band_writer(::fz_output *out);

/** Low-level wrapper for `::fz_new_pwg_band_writer()`. */
/**
	Create a new color pwg band writer.
*/
FZ_FUNCTION ::fz_band_writer *ll_fz_new_pwg_band_writer(::fz_output *out, const ::fz_pwg_options *pwg);

/** Low-level wrapper for `::fz_new_pwg_writer()`. */
FZ_FUNCTION ::fz_document_writer *ll_fz_new_pwg_writer(const char *path, const char *options);

/** Low-level wrapper for `::fz_new_pwg_writer_with_output()`. */
FZ_FUNCTION ::fz_document_writer *ll_fz_new_pwg_writer_with_output(::fz_output *out, const char *options);

/** Low-level wrapper for `::fz_new_rle_output()`. */
FZ_FUNCTION ::fz_output *ll_fz_new_rle_output(::fz_output *chain);

/** Low-level wrapper for `::fz_new_separations()`. */
/**
	Create a new separations structure (initially empty)
*/
FZ_FUNCTION ::fz_separations *ll_fz_new_separations(int controllable);

/** Low-level wrapper for `::fz_new_stext_device()`. */
/**
	Create a device to extract the text on a page.

	Gather the text on a page into blocks and lines.

	The reading order is taken from the order the text is drawn in
	the source file, so may not be accurate.

	page: The text page to which content should be added. This will
	usually be a newly created (empty) text page, but it can be one
	containing data already (for example when merging multiple
	pages, or watermarking).

	options: Options to configure the stext device.
*/
FZ_FUNCTION ::fz_device *ll_fz_new_stext_device(::fz_stext_page *page, const ::fz_stext_options *options);

/** Low-level wrapper for `::fz_new_stext_page()`. */
/**
	Create an empty text page.

	The text page is filled out by the text device to contain the
	blocks and lines of text on the page.

	mediabox: optional mediabox information.
*/
FZ_FUNCTION ::fz_stext_page *ll_fz_new_stext_page(::fz_rect mediabox);

/** Low-level wrapper for `::fz_new_stext_page_from_chapter_page_number()`. */
FZ_FUNCTION ::fz_stext_page *ll_fz_new_stext_page_from_chapter_page_number(::fz_document *doc, int chapter, int number, const ::fz_stext_options *options);

/** Low-level wrapper for `::fz_new_stext_page_from_display_list()`. */
FZ_FUNCTION ::fz_stext_page *ll_fz_new_stext_page_from_display_list(::fz_display_list *list, const ::fz_stext_options *options);

/** Low-level wrapper for `::fz_new_stext_page_from_page()`. */
/**
	Extract text from page.

	Ownership of the fz_stext_page is returned to the caller.
*/
FZ_FUNCTION ::fz_stext_page *ll_fz_new_stext_page_from_page(::fz_page *page, const ::fz_stext_options *options);

/** Low-level wrapper for `::fz_new_stext_page_from_page_number()`. */
FZ_FUNCTION ::fz_stext_page *ll_fz_new_stext_page_from_page_number(::fz_document *doc, int number, const ::fz_stext_options *options);

/** Low-level wrapper for `::fz_new_store_context()`. */
/**
	Create a new store inside the context

	max: The maximum size (in bytes) that the store is allowed to
	grow to. FZ_STORE_UNLIMITED means no limit.
*/
FZ_FUNCTION void ll_fz_new_store_context(size_t max);

/** Low-level wrapper for `::fz_new_story()`. */
FZ_FUNCTION ::fz_story *ll_fz_new_story(::fz_buffer *buf, const char *user_css, float em, ::fz_archive *dir);

/** Low-level wrapper for `::fz_new_stream()`. */
/**
	Create a new stream object with the given
	internal state and function pointers.

	state: Internal state (opaque to everything but implementation).

	next: Should provide the next set of bytes (up to max) of stream
	data. Return the number of bytes read, or EOF when there is no
	more data.

	drop: Should clean up and free the internal state. May not
	throw exceptions.
*/
FZ_FUNCTION ::fz_stream *ll_fz_new_stream(void *state, ::fz_stream_next_fn *next, ::fz_stream_drop_fn *drop);

/** Low-level wrapper for `::fz_new_string()`. */
FZ_FUNCTION ::fz_string *ll_fz_new_string(const char *str);

/** Low-level wrapper for `::fz_new_stroke_state()`. */
/**
	Create a new (empty) stroke state structure (with no dash
	data) and return a reference to it.

	Throws exception on failure to allocate.
*/
FZ_FUNCTION ::fz_stroke_state *ll_fz_new_stroke_state();

/** Low-level wrapper for `::fz_new_stroke_state_with_dash_len()`. */
/**
	Create a new (empty) stroke state structure, with room for
	dash data of the given length, and return a reference to it.

	len: The number of dash elements to allow room for.

	Throws exception on failure to allocate.
*/
FZ_FUNCTION ::fz_stroke_state *ll_fz_new_stroke_state_with_dash_len(int len);

/** Low-level wrapper for `::fz_new_svg_device()`. */
/**
	Create a device that outputs (single page) SVG files to
	the given output stream.

	Equivalent to fz_new_svg_device_with_id passing id = NULL.
*/
FZ_FUNCTION ::fz_device *ll_fz_new_svg_device(::fz_output *out, float page_width, float page_height, int text_format, int reuse_images);

/** Low-level wrapper for `::fz_new_svg_device_with_id()`.

This function has out-params. Python/C# wrappers look like:
	`ll_fz_new_svg_device_with_id(::fz_output *out, float page_width, float page_height, int text_format, int reuse_images)` => `(fz_device *, int id)`
*/
/**
	Create a device that outputs (single page) SVG files to
	the given output stream.

	output: The output stream to send the constructed SVG page to.

	page_width, page_height: The page dimensions to use (in points).

	text_format: How to emit text. One of the following values:
		FZ_SVG_TEXT_AS_TEXT: As <text> elements with possible
		layout errors and mismatching fonts.
		FZ_SVG_TEXT_AS_PATH: As <path> elements with exact
		visual appearance.

	reuse_images: Share image resources using <symbol> definitions.

	id: ID parameter to keep generated IDs unique across SVG files.
*/
FZ_FUNCTION ::fz_device *ll_fz_new_svg_device_with_id(::fz_output *out, float page_width, float page_height, int text_format, int reuse_images, int *id);

/** Low-level wrapper for `::fz_new_svg_writer()`. */
FZ_FUNCTION ::fz_document_writer *ll_fz_new_svg_writer(const char *path, const char *options);

/** Low-level wrapper for `::fz_new_svg_writer_with_output()`. */
FZ_FUNCTION ::fz_document_writer *ll_fz_new_svg_writer_with_output(::fz_output *out, const char *options);

/** Low-level wrapper for `::fz_new_test_device()`.

This function has out-params. Python/C# wrappers look like:
	`ll_fz_new_test_device(float threshold, int options, ::fz_device *passthrough)` => `(fz_device *, int is_color)`
*/
/**
	Create a device to test for features.

	Currently only tests for the presence of non-grayscale colors.

	is_color: Possible values returned:
		0: Definitely greyscale
		1: Probably color (all colors were grey, but there
		were images or shadings in a non grey colorspace).
		2: Definitely color

	threshold: The difference from grayscale that will be tolerated.
	Typical values to use are either 0 (be exact) and 0.02 (allow an
	imperceptible amount of slop).

	options: A set of bitfield options, from the FZ_TEST_OPT set.

	passthrough: A device to pass all calls through to, or NULL.
	If set, then the test device can both test and pass through to
	an underlying device (like, say, the display list device). This
	means that a display list can be created and at the end we'll
	know if it's colored or not.

	In the absence of a passthrough device, the device will throw
	an exception to stop page interpretation when color is found.
*/
FZ_FUNCTION ::fz_device *ll_fz_new_test_device(int *is_color, float threshold, int options, ::fz_device *passthrough);

/** Low-level wrapper for `::fz_new_text()`. */
/**
	Create a new empty fz_text object.

	Throws exception on failure to allocate.
*/
FZ_FUNCTION ::fz_text *ll_fz_new_text();

/** Low-level wrapper for `::fz_new_text_writer()`. */
FZ_FUNCTION ::fz_document_writer *ll_fz_new_text_writer(const char *format, const char *path, const char *options);

/** Low-level wrapper for `::fz_new_text_writer_with_output()`. */
FZ_FUNCTION ::fz_document_writer *ll_fz_new_text_writer_with_output(const char *format, ::fz_output *out, const char *options);

/** Low-level wrapper for `::fz_new_trace_device()`. */
/**
	Create a device to print a debug trace of all device calls.
*/
FZ_FUNCTION ::fz_device *ll_fz_new_trace_device(::fz_output *out);

/** Low-level wrapper for `::fz_new_tree_archive()`. */
/**
	Create an archive that holds named buffers.

	tree can either be a preformed tree with fz_buffers as values,
	or it can be NULL for an empty tree.
*/
FZ_FUNCTION ::fz_archive *ll_fz_new_tree_archive(::fz_tree *tree);

/** Low-level wrapper for `::fz_new_type3_font()`. */
/**
	Create a new (empty) type3 font.

	name: Name of font (or NULL).

	matrix: Font matrix.

	Returns a new font handle, or throws exception on
	allocation failure.
*/
FZ_FUNCTION ::fz_font *ll_fz_new_type3_font(const char *name, ::fz_matrix matrix);

/** Low-level wrapper for `::fz_new_xhtml_document_from_document()`. */
/**
	Use text extraction to convert the input document into XHTML,
	then open the result as a new document that can be reflowed.
*/
FZ_FUNCTION ::fz_document *ll_fz_new_xhtml_document_from_document(::fz_document *old_doc, const ::fz_stext_options *opts);

/** Low-level wrapper for `::fz_new_xmltext_device()`. */
/**
	Create a device to output raw information.
*/
FZ_FUNCTION ::fz_device *ll_fz_new_xmltext_device(::fz_output *out);

/** Low-level wrapper for `::fz_new_zip_writer()`. */
/**
	Create a new zip writer that writes to a given file.

	Open an archive using a seekable stream object rather than
	opening a file or directory on disk.
*/
FZ_FUNCTION ::fz_zip_writer *ll_fz_new_zip_writer(const char *filename);

/** Low-level wrapper for `::fz_new_zip_writer_with_output()`. */
/**
	Create a new zip writer that writes to a given output stream.

	Ownership of out passes in immediately upon calling this function.
	The caller should never drop the fz_output, even if this function throws
	an exception.
*/
FZ_FUNCTION ::fz_zip_writer *ll_fz_new_zip_writer_with_output(::fz_output *out);

/** Low-level wrapper for `::fz_next_page()`. */
/**
	Function to get the location of the next page (allowing for the
	end of chapters etc). If at the end of the document, returns the
	current location.
*/
FZ_FUNCTION ::fz_location ll_fz_next_page(::fz_document *doc, ::fz_location loc);

/** Low-level wrapper for `::fz_normalize_vector()`. */
/**
	Normalize a vector to length one.
*/
FZ_FUNCTION ::fz_point ll_fz_normalize_vector(::fz_point p);

/** Low-level wrapper for `::fz_open_a85d()`. */
/**
	a85d filter performs ASCII 85 Decoding of data read
	from the chained filter.
*/
FZ_FUNCTION ::fz_stream *ll_fz_open_a85d(::fz_stream *chain);

/** Low-level wrapper for `::fz_open_accelerated_document()`. */
/**
	Open a document file and read its basic structure so pages and
	objects can be located. MuPDF will try to repair broken
	documents (without actually changing the file contents).

	The returned fz_document is used when calling most other
	document related functions.

	filename: a path to a file as it would be given to open(2).
*/
FZ_FUNCTION ::fz_document *ll_fz_open_accelerated_document(const char *filename, const char *accel);

/** Low-level wrapper for `::fz_open_accelerated_document_with_stream()`. */
/**
	Open a document using the specified stream object rather than
	opening a file on disk.

	magic: a string used to detect document type; either a file name
	or mime-type.

	stream: a stream of the document contents.

	accel: NULL, or a stream of the 'accelerator' contents for this document.

	NOTE: The caller retains ownership of 'stream' and 'accel' - the document will
	take its own references if required.
*/
FZ_FUNCTION ::fz_document *ll_fz_open_accelerated_document_with_stream(const char *magic, ::fz_stream *stream, ::fz_stream *accel);

/** Low-level wrapper for `::fz_open_accelerated_document_with_stream_and_dir()`. */
/**
	Open a document using the specified stream object rather than
	opening a file on disk.

	magic: a string used to detect document type; either a file name
	or mime-type.

	stream: a stream of the document contents.

	accel: NULL, or a stream of the 'accelerator' contents for this document.

	dir: NULL, or the 'directory context' for the stream contents.

	NOTE: The caller retains ownership of 'stream', 'accel' and 'dir' - the document will
	take its own references if required.
*/
FZ_FUNCTION ::fz_document *ll_fz_open_accelerated_document_with_stream_and_dir(const char *magic, ::fz_stream *stream, ::fz_stream *accel, ::fz_archive *dir);

/** Low-level wrapper for `::fz_open_aesd()`. */
/**
	aesd filter performs AES decoding of data read from the chained
	filter using the supplied key.
*/
FZ_FUNCTION ::fz_stream *ll_fz_open_aesd(::fz_stream *chain, unsigned char *key, unsigned int keylen);

/** Low-level wrapper for `::fz_open_ahxd()`. */
/**
	ahxd filter performs ASCII Hex decoding of data read
	from the chained filter.
*/
FZ_FUNCTION ::fz_stream *ll_fz_open_ahxd(::fz_stream *chain);

/** Low-level wrapper for `::fz_open_arc4()`. */
/**
	arc4 filter performs RC4 decoding of data read from the chained
	filter using the supplied key.
*/
FZ_FUNCTION ::fz_stream *ll_fz_open_arc4(::fz_stream *chain, unsigned char *key, unsigned int keylen);

/** Low-level wrapper for `::fz_open_archive()`. */
/**
	Open a zip or tar archive

	Open a file and identify its archive type based on the archive
	signature contained inside.

	filename: a path to a file as it would be given to open(2).
*/
FZ_FUNCTION ::fz_archive *ll_fz_open_archive(const char *filename);

/** Low-level wrapper for `::fz_open_archive_entry()`. */
/**
	Opens an archive entry as a stream.

	name: Entry name to look for, this must be an exact match to
	the entry name in the archive.

	Throws an exception if a matching entry cannot be found.
*/
FZ_FUNCTION ::fz_stream *ll_fz_open_archive_entry(::fz_archive *arch, const char *name);

/** Low-level wrapper for `::fz_open_archive_with_stream()`. */
/**
	Open zip or tar archive stream.

	Open an archive using a seekable stream object rather than
	opening a file or directory on disk.
*/
FZ_FUNCTION ::fz_archive *ll_fz_open_archive_with_stream(::fz_stream *file);

/** Low-level wrapper for `::fz_open_buffer()`. */
/**
	Open a buffer as a stream.

	buf: The buffer to open. Ownership of the buffer is NOT passed
	in (this function takes its own reference).

	Returns pointer to newly created stream. May throw exceptions on
	failure to allocate.
*/
FZ_FUNCTION ::fz_stream *ll_fz_open_buffer(::fz_buffer *buf);

/** Low-level wrapper for `::fz_open_cfb_archive()`. */
/**
	Open a cfb file as an archive.

	An exception is thrown if the file is not recognised as a cfb.

	filename: a path to an archive file as it would be given to
	open(2).
*/
FZ_FUNCTION ::fz_archive *ll_fz_open_cfb_archive(const char *filename);

/** Low-level wrapper for `::fz_open_cfb_archive_with_stream()`. */
/**
	Open a cfb file as an archive.

	Open an archive using a seekable stream object rather than
	opening a file or directory on disk.

	An exception is thrown if the file is not recognised as a chm.
*/
FZ_FUNCTION ::fz_archive *ll_fz_open_cfb_archive_with_stream(::fz_stream *file);

/** Low-level wrapper for `::fz_open_compressed_buffer()`. */
/**
	Open a stream to read the decompressed version of a buffer.
*/
FZ_FUNCTION ::fz_stream *ll_fz_open_compressed_buffer(::fz_compressed_buffer *arg_1);

/** Low-level wrapper for `::fz_open_concat()`. */
/**
	Concat filter concatenates several streams into one.
*/
FZ_FUNCTION ::fz_stream *ll_fz_open_concat(int max, int pad);

/** Low-level wrapper for `::fz_open_dctd()`. */
/**
	dctd filter performs DCT (JPEG) decoding of data read
	from the chained filter.

	color_transform implements the PDF color_transform option
		use -1 for default behavior
		use 0 to disable YUV-RGB / YCCK-CMYK transforms
		use 1 to enable YUV-RGB / YCCK-CMYK transforms

	invert_cmyk implements the necessary inversion for Photoshop CMYK images
		use 0 if embedded in PDF
		use 1 if not embedded in PDF

	For subsampling on decode, set l2factor to the log2 of the
	reduction required (therefore 0 = full size decode).

	jpegtables is an optional stream from which the JPEG tables
	can be read. Use NULL if not required.
*/
FZ_FUNCTION ::fz_stream *ll_fz_open_dctd(::fz_stream *chain, int color_transform, int invert_cmyk, int l2factor, ::fz_stream *jpegtables);

/** Low-level wrapper for `::fz_open_directory()`. */
/**
	Open a directory as if it was an archive.

	A special case where a directory is opened as if it was an
	archive.

	Note that for directories it is not possible to retrieve the
	number of entries or list the entries. It is however possible
	to check if the archive has a particular entry.

	path: a path to a directory as it would be given to opendir(3).
*/
FZ_FUNCTION ::fz_archive *ll_fz_open_directory(const char *path);

/** Low-level wrapper for `::fz_open_document()`. */
/**
	Open a document file and read its basic structure so pages and
	objects can be located. MuPDF will try to repair broken
	documents (without actually changing the file contents).

	The returned fz_document is used when calling most other
	document related functions.

	filename: a path to a file as it would be given to open(2).
*/
FZ_FUNCTION ::fz_document *ll_fz_open_document(const char *filename);

/** Low-level wrapper for `::fz_open_document_with_buffer()`. */
/**
	Open a document using a buffer rather than opening a file on disk.
*/
FZ_FUNCTION ::fz_document *ll_fz_open_document_with_buffer(const char *magic, ::fz_buffer *buffer);

/** Low-level wrapper for `::fz_open_document_with_stream()`. */
/**
	Open a document using the specified stream object rather than
	opening a file on disk.

	magic: a string used to detect document type; either a file name
	or mime-type.

	stream: a stream representing the contents of the document file.

	NOTE: The caller retains ownership of 'stream' - the document will take its
	own reference if required.
*/
FZ_FUNCTION ::fz_document *ll_fz_open_document_with_stream(const char *magic, ::fz_stream *stream);

/** Low-level wrapper for `::fz_open_document_with_stream_and_dir()`. */
/**
	Open a document using the specified stream object rather than
	opening a file on disk.

	magic: a string used to detect document type; either a file name
	or mime-type.

	stream: a stream representing the contents of the document file.

	dir: a 'directory context' for those filetypes that need it.

	NOTE: The caller retains ownership of 'stream' and 'dir' - the document will
	take its own references if required.
*/
FZ_FUNCTION ::fz_document *ll_fz_open_document_with_stream_and_dir(const char *magic, ::fz_stream *stream, ::fz_archive *dir);

/** Low-level wrapper for `::fz_open_endstream_filter()`. */
/**
	The endstream filter reads a PDF substream, and starts to look
	for an 'endstream' token after the specified length.
*/
FZ_FUNCTION ::fz_stream *ll_fz_open_endstream_filter(::fz_stream *chain, uint64_t len, int64_t offset);

/** Low-level wrapper for `::fz_open_faxd()`. */
/**
	faxd filter performs FAX decoding of data read from
	the chained filter.

	k: see fax specification (fax default is 0).

	end_of_line: whether we expect end of line markers (fax default
	is 0).

	encoded_byte_align: whether we align to bytes after each line
	(fax default is 0).

	columns: how many columns in the image (fax default is 1728).

	rows: 0 for unspecified or the number of rows of data to expect.

	end_of_block: whether we expect end of block markers (fax
	default is 1).

	black_is_1: determines the polarity of the image (fax default is
	0).
*/
FZ_FUNCTION ::fz_stream *ll_fz_open_faxd(::fz_stream *chain, int k, int end_of_line, int encoded_byte_align, int columns, int rows, int end_of_block, int black_is_1);

/** Low-level wrapper for `::fz_open_file()`. */
/**
	Open the named file and wrap it in a stream.

	filename: Path to a file. On non-Windows machines the filename
	should be exactly as it would be passed to fopen(2). On Windows
	machines, the path should be UTF-8 encoded so that non-ASCII
	characters can be represented. Other platforms do the encoding
	as standard anyway (and in most cases, particularly for MacOS
	and Linux, the encoding they use is UTF-8 anyway).
*/
FZ_FUNCTION ::fz_stream *ll_fz_open_file(const char *filename);

/** Low-level wrapper for `::fz_open_file_autodelete()`. */
/**
	Do the same as fz_open_file, but delete the file upon close.
*/
FZ_FUNCTION ::fz_stream *ll_fz_open_file_autodelete(const char *filename);

/** Low-level wrapper for `::fz_open_file_ptr_no_close()`. */
/**
	Create a stream from a FILE * that will not be closed
	when the stream is dropped.
*/
FZ_FUNCTION ::fz_stream *ll_fz_open_file_ptr_no_close(FILE *file);

/** Low-level wrapper for `::fz_open_flated()`. */
/**
	flated filter performs LZ77 decoding (inflating) of data read
	from the chained filter.

	window_bits: How large a decompression window to use. Typically
	15. A negative number, -n, means to use n bits, but to expect
	raw data with no header.
*/
FZ_FUNCTION ::fz_stream *ll_fz_open_flated(::fz_stream *chain, int window_bits);

/** Low-level wrapper for `::fz_open_image_decomp_stream()`.

This function has out-params. Python/C# wrappers look like:
	`ll_fz_open_image_decomp_stream(::fz_stream *arg_0, ::fz_compression_params *arg_1)` => `(fz_stream *, int l2factor)`
*/
/**
	Open a stream to read the decompressed version of another stream
	with optional log2 subsampling.
*/
FZ_FUNCTION ::fz_stream *ll_fz_open_image_decomp_stream(::fz_stream *arg_1, ::fz_compression_params *arg_2, int *l2factor);

/** Low-level wrapper for `::fz_open_image_decomp_stream_from_buffer()`.

This function has out-params. Python/C# wrappers look like:
	`ll_fz_open_image_decomp_stream_from_buffer(::fz_compressed_buffer *arg_0)` => `(fz_stream *, int l2factor)`
*/
/**
	Open a stream to read the decompressed version of a buffer,
	with optional log2 subsampling.

	l2factor = NULL for no subsampling, or a pointer to an integer
	containing the maximum log2 subsample factor acceptable (0 =
	none, 1 = halve dimensions, 2 = quarter dimensions etc). If
	non-NULL, then *l2factor will be updated on exit with the actual
	log2 subsample factor achieved.
*/
FZ_FUNCTION ::fz_stream *ll_fz_open_image_decomp_stream_from_buffer(::fz_compressed_buffer *arg_1, int *l2factor);

/** Low-level wrapper for `::fz_open_jbig2d()`. */
/**
	Open a filter that performs jbig2 decompression on the chained
	stream, using the optional globals record.
*/
FZ_FUNCTION ::fz_stream *ll_fz_open_jbig2d(::fz_stream *chain, ::fz_jbig2_globals *globals, int embedded);

/** Low-level wrapper for `::fz_open_leecher()`. */
/**
	Attach a filter to a stream that will store any
	characters read from the stream into the supplied buffer.

	chain: The underlying stream to leech from.

	buf: The buffer into which the read data should be appended.
	The buffer will be resized as required.

	Returns pointer to newly created stream. May throw exceptions on
	failure to allocate.
*/
FZ_FUNCTION ::fz_stream *ll_fz_open_leecher(::fz_stream *chain, ::fz_buffer *buf);

/** Low-level wrapper for `::fz_open_libarchive_archive()`. */
/**
	Open an archive using libarchive.

	An exception is thrown if the file is not supported by libarchive.

	filename: a path to an archive file as it would be given to
	open(2).
*/
FZ_FUNCTION ::fz_archive *ll_fz_open_libarchive_archive(const char *filename);

/** Low-level wrapper for `::fz_open_libarchive_archive_with_stream()`. */
/**
	Open an archive using libarchive.

	Open an archive using a seekable stream object rather than
	opening a file or directory on disk.

	An exception is thrown if the stream is not supported by libarchive.
*/
FZ_FUNCTION ::fz_archive *ll_fz_open_libarchive_archive_with_stream(::fz_stream *file);

/** Low-level wrapper for `::fz_open_libarchived()`. */
/**
	libarchived filter performs generic compressed decoding of data
	in any format understood by libarchive from the chained filter.

	This will throw an exception if libarchive is not built in, or
	if the compression format is not recognised.
*/
FZ_FUNCTION ::fz_stream *ll_fz_open_libarchived(::fz_stream *chain);

/** Low-level wrapper for `::fz_open_lzwd()`. */
/**
	lzwd filter performs LZW decoding of data read from the chained
	filter.

	early_change: (Default 1) specifies whether to change codes 1
	bit early.

	min_bits: (Default 9) specifies the minimum number of bits to
	use.

	reverse_bits: (Default 0) allows for compatibility with gif and
	old style tiffs (1).

	old_tiff: (Default 0) allows for different handling of the clear
	code, as found in old style tiffs.
*/
FZ_FUNCTION ::fz_stream *ll_fz_open_lzwd(::fz_stream *chain, int early_change, int min_bits, int reverse_bits, int old_tiff);

/** Low-level wrapper for `::fz_open_memory()`. */
/**
	Open a block of memory as a stream.

	data: Pointer to start of data block. Ownership of the data
	block is NOT passed in.

	len: Number of bytes in data block.

	Returns pointer to newly created stream. May throw exceptions on
	failure to allocate.
*/
FZ_FUNCTION ::fz_stream *ll_fz_open_memory(const unsigned char *data, size_t len);

/** Low-level wrapper for `::fz_open_null_filter()`. */
/**
	The null filter reads a specified amount of data from the
	substream.
*/
FZ_FUNCTION ::fz_stream *ll_fz_open_null_filter(::fz_stream *chain, uint64_t len, int64_t offset);

/** Low-level wrapper for `::fz_open_predict()`. */
/**
	predict filter performs pixel prediction on data read from
	the chained filter.

	predictor: 1 = copy, 2 = tiff, other = inline PNG predictor

	columns: width of image in pixels

	colors: number of components.

	bpc: bits per component (typically 8)
*/
FZ_FUNCTION ::fz_stream *ll_fz_open_predict(::fz_stream *chain, int predictor, int columns, int colors, int bpc);

/** Low-level wrapper for `::fz_open_range_filter()`. */
/**
	The range filter copies data from specified ranges of the
	chained stream.
*/
FZ_FUNCTION ::fz_stream *ll_fz_open_range_filter(::fz_stream *chain, ::fz_range *ranges, int nranges);

/** Low-level wrapper for `::fz_open_reflowed_document()`. */
FZ_FUNCTION ::fz_document *ll_fz_open_reflowed_document(::fz_document *underdoc, const ::fz_stext_options *opts);

/** Low-level wrapper for `::fz_open_rld()`. */
/**
	rld filter performs Run Length Decoding of data read
	from the chained filter.
*/
FZ_FUNCTION ::fz_stream *ll_fz_open_rld(::fz_stream *chain);

/** Low-level wrapper for `::fz_open_sgilog16()`. */
/**
	SGI Log 16bit (greyscale) decode from the chained filter.
	Decodes lines of w pixels to 8bpp greyscale.
*/
FZ_FUNCTION ::fz_stream *ll_fz_open_sgilog16(::fz_stream *chain, int w);

/** Low-level wrapper for `::fz_open_sgilog24()`. */
/**
	SGI Log 24bit (LUV) decode from the chained filter.
	Decodes lines of w pixels to 8bpc rgb.
*/
FZ_FUNCTION ::fz_stream *ll_fz_open_sgilog24(::fz_stream *chain, int w);

/** Low-level wrapper for `::fz_open_sgilog32()`. */
/**
	SGI Log 32bit (LUV) decode from the chained filter.
	Decodes lines of w pixels to 8bpc rgb.
*/
FZ_FUNCTION ::fz_stream *ll_fz_open_sgilog32(::fz_stream *chain, int w);

/** Low-level wrapper for `::fz_open_tar_archive()`. */
/**
	Open a tar archive file.

	An exception is thrown if the file is not a tar archive as
	indicated by the presence of a tar signature.

	filename: a path to a tar archive file as it would be given to
	open(2).
*/
FZ_FUNCTION ::fz_archive *ll_fz_open_tar_archive(const char *filename);

/** Low-level wrapper for `::fz_open_tar_archive_with_stream()`. */
/**
	Open a tar archive stream.

	Open an archive using a seekable stream object rather than
	opening a file or directory on disk.

	An exception is thrown if the stream is not a tar archive as
	indicated by the presence of a tar signature.

*/
FZ_FUNCTION ::fz_archive *ll_fz_open_tar_archive_with_stream(::fz_stream *file);

/** Low-level wrapper for `::fz_open_thunder()`. */
/**
	4bit greyscale Thunderscan decoding from the chained filter.
	Decodes lines of w pixels to 8bpp greyscale.
*/
FZ_FUNCTION ::fz_stream *ll_fz_open_thunder(::fz_stream *chain, int w);

/** Low-level wrapper for `::fz_open_zip_archive()`. */
/**
	Open a zip archive file.

	An exception is thrown if the file is not a zip archive as
	indicated by the presence of a zip signature.

	filename: a path to a zip archive file as it would be given to
	open(2).
*/
FZ_FUNCTION ::fz_archive *ll_fz_open_zip_archive(const char *path);

/** Low-level wrapper for `::fz_open_zip_archive_with_stream()`. */
/**
	Open a zip archive stream.

	Open an archive using a seekable stream object rather than
	opening a file or directory on disk.

	An exception is thrown if the stream is not a zip archive as
	indicated by the presence of a zip signature.

*/
FZ_FUNCTION ::fz_archive *ll_fz_open_zip_archive_with_stream(::fz_stream *file);

/** Low-level wrapper for `::fz_opt_from_list()`. */
/**
	Return the index of a (case-insensitive) option within an optlist.

	For instance for optlist = "Foo|Bar|Baz", and  opt = "bar",
	this would return 1.

	If the optlist ends with "|*" then that is a catch all case and
	matches all options allowing the caller to process it itself.
	fz_optarg will be set to point to the option, and the return
	value will be the index of the '*' option within that list.

	If an optlist entry ends with ':' (e.g. "Foo:") then that option
	may have suboptions appended to it (for example "JPG:80") and
	fz_optarg will be set to point at "80". Otherwise fz_optarg will
	be set to NULL.

	In the event of no-match found, prints an error and returns -1.
*/
FZ_FUNCTION int ll_fz_opt_from_list(char *opt, const char *optlist);

/** Low-level wrapper for `::fz_option_eq()`. */
/**
	Check to see if an option, a, from a string matches a reference
	option, b.

	(i.e. a could be 'foo' or 'foo,bar...' etc, but b can only be
	'foo'.)
*/
FZ_FUNCTION int ll_fz_option_eq(const char *a, const char *b);

/** Low-level wrapper for `::fz_outline_glyph()`. */
/**
	Look a glyph up from a font, and return the outline of the
	glyph using the given transform.

	The caller owns the returned path, and so is responsible for
	ensuring that it eventually gets dropped.
*/
FZ_FUNCTION ::fz_path *ll_fz_outline_glyph(::fz_font *font, int gid, ::fz_matrix ctm);

/** Low-level wrapper for `::fz_outline_iterator_delete()`. */
/**
	Delete the current item.

	This implicitly moves us to the 'next' item, and the return code is as for fz_outline_iterator_next.
*/
FZ_FUNCTION int ll_fz_outline_iterator_delete(::fz_outline_iterator *iter);

/** Low-level wrapper for `::fz_outline_iterator_down()`. */
FZ_FUNCTION int ll_fz_outline_iterator_down(::fz_outline_iterator *iter);

/** Low-level wrapper for `::fz_outline_iterator_from_outline()`. */
FZ_FUNCTION ::fz_outline_iterator *ll_fz_outline_iterator_from_outline(::fz_outline *outline);

/** Low-level wrapper for `::fz_outline_iterator_insert()`. */
/**
	Call to insert a new item BEFORE the current point.

	Ownership of pointers are retained by the caller. The item data will be copied.

	After an insert, we do not change where we are pointing.
	The return code is the same as for next, it indicates the current iterator position.
*/
FZ_FUNCTION int ll_fz_outline_iterator_insert(::fz_outline_iterator *iter, ::fz_outline_item *item);

/** Low-level wrapper for `::fz_outline_iterator_item()`. */
/**
	Call to get the current outline item.

	Can return NULL. The item is only valid until the next call.
*/
FZ_FUNCTION ::fz_outline_item *ll_fz_outline_iterator_item(::fz_outline_iterator *iter);

/** Low-level wrapper for `::fz_outline_iterator_next()`. */
/**
	Calls to move the iterator position.

	A negative return value means we could not move as requested. Otherwise:
	0 = the final position has a valid item.
	1 = not a valid item, but we can insert an item here.
*/
FZ_FUNCTION int ll_fz_outline_iterator_next(::fz_outline_iterator *iter);

/** Low-level wrapper for `::fz_outline_iterator_prev()`. */
FZ_FUNCTION int ll_fz_outline_iterator_prev(::fz_outline_iterator *iter);

/** Low-level wrapper for `::fz_outline_iterator_up()`. */
FZ_FUNCTION int ll_fz_outline_iterator_up(::fz_outline_iterator *iter);

/** Low-level wrapper for `::fz_outline_iterator_update()`. */
/**
	Update the current item properties according to the given item.
*/
FZ_FUNCTION void ll_fz_outline_iterator_update(::fz_outline_iterator *iter, ::fz_outline_item *item);

/** Low-level wrapper for `::fz_output_accelerator()`. */
/**
	Output accelerator data for the document to a given output
	stream.
*/
FZ_FUNCTION void ll_fz_output_accelerator(::fz_document *doc, ::fz_output *accel);

/** Low-level wrapper for `::fz_output_supports_stream()`. */
/**
	Query whether a given fz_output supports fz_stream_from_output.
*/
FZ_FUNCTION int ll_fz_output_supports_stream(::fz_output *out);

/** Low-level wrapper for `::fz_output_xml()`. */
/**
	Pretty-print an XML tree to given output.
*/
FZ_FUNCTION void ll_fz_output_xml(::fz_output *out, ::fz_xml *item, int level);

/** Low-level wrapper for `::fz_pack_path()`. */
/**
	Pack a path into the given block.
	To minimise the size of paths, this function allows them to be
	packed into a buffer with other information. Paths can be used
	interchangeably regardless of how they are packed.

	pack: Pointer to a block of data to pack the path into. Should
	be aligned by the caller to the same alignment as required for
	a fz_path pointer.

	path: The path to pack.

	Returns the number of bytes within the block used. Callers can
	access the packed path data by casting the value of pack on
	entry to be a fz_path *.

	Throws exceptions on failure to allocate.

	Implementation details: Paths can be 'unpacked', 'flat', or
	'open'. Standard paths, as created are 'unpacked'. Paths
	will be packed as 'flat', unless they are too large
	(where large indicates that they exceed some private
	implementation defined limits, currently including having
	more than 256 coordinates or commands).

	Large paths are 'open' packed as a header into the given block,
	plus pointers to other data blocks.

	Users should not have to care about whether paths are 'open'
	or 'flat' packed. Simply pack a path (if required), and then
	forget about the details.
*/
FZ_FUNCTION size_t ll_fz_pack_path(uint8_t *pack, const ::fz_path *path);

/** Low-level wrapper for `::fz_packed_path_size()`. */
/**
	Return the number of bytes required to pack a path.
*/
FZ_FUNCTION int ll_fz_packed_path_size(const ::fz_path *path);

/** Low-level wrapper for `::fz_page_label()`. */
/**
	Get page label for a given page.
*/
FZ_FUNCTION const char *ll_fz_page_label(::fz_page *page, char *buf, int size);

/** Low-level wrapper for `::fz_page_number_from_location()`. */
/**
	Converts from chapter+page to page number. This may cause many
	chapters to be laid out in order to calculate the number of
	pages within those chapters.
*/
FZ_FUNCTION int ll_fz_page_number_from_location(::fz_document *doc, ::fz_location loc);

/** Low-level wrapper for `::fz_page_presentation()`.

This function has out-params. Python/C# wrappers look like:
	`ll_fz_page_presentation(::fz_page *page, ::fz_transition *transition)` => `(fz_transition *, float duration)`
*/
/**
	Get the presentation details for a given page.

	transition: A pointer to a transition struct to fill out.

	duration: A pointer to a place to set the page duration in
	seconds. Will be set to 0 if no transition is specified for the
	page.

	Returns: a pointer to the transition structure, or NULL if there
	is no transition specified for the page.
*/
FZ_FUNCTION ::fz_transition *ll_fz_page_presentation(::fz_page *page, ::fz_transition *transition, float *duration);

/** Low-level wrapper for `::fz_page_separations()`. */
/**
	Get the separations details for a page.
	This will be NULL, unless the format specifically supports
	separations (such as PDF files). May be NULL even
	so, if there are no separations on a page.

	Returns a reference that must be dropped.
*/
FZ_FUNCTION ::fz_separations *ll_fz_page_separations(::fz_page *page);

/** Low-level wrapper for `::fz_page_uses_overprint()`. */
/**
	Query if a given page requires overprint.
*/
FZ_FUNCTION int ll_fz_page_uses_overprint(::fz_page *page);

/** Low-level wrapper for `::fz_paint_shade()`.

This function has out-params. Python/C# wrappers look like:
	`ll_fz_paint_shade(::fz_shade *shade, ::fz_colorspace *override_cs, ::fz_matrix ctm, ::fz_pixmap *dest, ::fz_color_params color_params, ::fz_irect bbox, const ::fz_overprint *eop, ::fz_shade_color_cache **cache)` => 
*/
/**
	Render a shade to a given pixmap.

	shade: The shade to paint.

	override_cs: NULL, or colorspace to override the shades
	inbuilt colorspace.

	ctm: The transform to apply.

	dest: The pixmap to render into.

	color_params: The color rendering settings

	bbox: Pointer to a bounding box to limit the rendering
	of the shade.

	eop: NULL, or pointer to overprint bitmap.

	cache: *cache is used to cache color information. If *cache is NULL it
	is set to point to a new fz_shade_color_cache. If cache is NULL it is
	ignored.
*/
FZ_FUNCTION void ll_fz_paint_shade(::fz_shade *shade, ::fz_colorspace *override_cs, ::fz_matrix ctm, ::fz_pixmap *dest, ::fz_color_params color_params, ::fz_irect bbox, const ::fz_overprint *eop, ::fz_shade_color_cache **cache);

/** Low-level wrapper for `::fz_parse_draw_options()`. */
/**
	Parse draw device options from a comma separated key-value string.
*/
FZ_FUNCTION ::fz_draw_options *ll_fz_parse_draw_options(::fz_draw_options *options, const char *string);

/** Low-level wrapper for `::fz_parse_page_range()`.

This function has out-params. Python/C# wrappers look like:
	`ll_fz_parse_page_range(const char *s, int n)` => `(const char *, int a, int b)`
*/
FZ_FUNCTION const char *ll_fz_parse_page_range(const char *s, int *a, int *b, int n);

/** Low-level wrapper for `::fz_parse_pcl_options()`. */
/**
	Parse PCL options.

	Currently defined options and values are as follows:

		preset=X	Either "generic" or one of the presets as for fz_pcl_preset.
		spacing=0	No vertical spacing capability
		spacing=1	PCL 3 spacing (<ESC>*p+<n>Y)
		spacing=2	PCL 4 spacing (<ESC>*b<n>Y)
		spacing=3	PCL 5 spacing (<ESC>*b<n>Y and clear seed row)
		mode2		Disable/Enable mode 2 graphics compression
		mode3		Disable/Enable mode 3 graphics compression
		eog_reset	End of graphics (<ESC>*rB) resets all parameters
		has_duplex	Duplex supported (<ESC>&l<duplex>S)
		has_papersize	Papersize setting supported (<ESC>&l<sizecode>A)
		has_copies	Number of copies supported (<ESC>&l<copies>X)
		is_ljet4pjl	Disable/Enable HP 4PJL model-specific output
		is_oce9050	Disable/Enable Oce 9050 model-specific output
*/
FZ_FUNCTION ::fz_pcl_options *ll_fz_parse_pcl_options(::fz_pcl_options *opts, const char *args);

/** Low-level wrapper for `::fz_parse_pclm_options()`. */
/**
	Parse PCLm options.

	Currently defined options and values are as follows:

		compression=none: No compression
		compression=flate: Flate compression
		strip-height=n: Strip height (default 16)
*/
FZ_FUNCTION ::fz_pclm_options *ll_fz_parse_pclm_options(::fz_pclm_options *opts, const char *args);

/** Low-level wrapper for `::fz_parse_pdfocr_options()`. */
/**
	Parse PDFOCR options.

	Currently defined options and values are as follows:

		compression=none: No compression
		compression=flate: Flate compression
		strip-height=n: Strip height (default 16)
		ocr-language=<lang>: OCR Language (default eng)
		ocr-datadir=<datadir>: OCR data path (default rely on TESSDATA_PREFIX)
*/
FZ_FUNCTION ::fz_pdfocr_options *ll_fz_parse_pdfocr_options(::fz_pdfocr_options *opts, const char *args);

/** Low-level wrapper for `::fz_parse_stext_options()`. */
/**
	Parse stext device options from a comma separated key-value
	string.
*/
FZ_FUNCTION ::fz_stext_options *ll_fz_parse_stext_options(::fz_stext_options *opts, const char *string);

/** Low-level wrapper for `::fz_parse_xml()`. */
/**
	Parse the contents of buffer into a tree of xml nodes.

	preserve_white: whether to keep or delete all-whitespace nodes.
*/
FZ_FUNCTION ::fz_xml *ll_fz_parse_xml(::fz_buffer *buf, int preserve_white);

/** Low-level wrapper for `::fz_parse_xml_archive_entry()`. */
/**
	Parse the contents of an archive entry into a tree of xml nodes.

	preserve_white: whether to keep or delete all-whitespace nodes.
*/
FZ_FUNCTION ::fz_xml *ll_fz_parse_xml_archive_entry(::fz_archive *dir, const char *filename, int preserve_white);

/** Low-level wrapper for `::fz_parse_xml_from_html5()`. */
/**
	Parse the contents of a buffer into a tree of XML nodes,
	using the HTML5 parsing algorithm.
*/
FZ_FUNCTION ::fz_xml *ll_fz_parse_xml_from_html5(::fz_buffer *buf);

/** Low-level wrapper for `::fz_parse_xml_stream()`. */
/**
	Parse the contents of buffer into a tree of xml nodes.

	preserve_white: whether to keep or delete all-whitespace nodes.
*/
FZ_FUNCTION ::fz_xml *ll_fz_parse_xml_stream(::fz_stream *stream, int preserve_white);

/** Low-level wrapper for `::fz_pcl_preset()`. */
/**
	Initialize PCL option struct for a given preset.

	Currently defined presets include:

		generic	Generic PCL printer
		ljet4	HP DeskJet
		dj500	HP DeskJet 500
		fs600	Kyocera FS-600
		lj	HP LaserJet, HP LaserJet Plus
		lj2	HP LaserJet IIp, HP LaserJet IId
		lj3	HP LaserJet III
		lj3d	HP LaserJet IIId
		lj4	HP LaserJet 4
		lj4pl	HP LaserJet 4 PL
		lj4d	HP LaserJet 4d
		lp2563b	HP 2563B line printer
		oce9050	Oce 9050 Line printer
*/
FZ_FUNCTION void ll_fz_pcl_preset(::fz_pcl_options *opts, const char *preset);

/** Low-level wrapper for `::fz_pdfocr_band_writer_set_progress()`. */
/**
	Set the progress callback for a pdfocr bandwriter.
*/
FZ_FUNCTION void ll_fz_pdfocr_band_writer_set_progress(::fz_band_writer *writer, ::fz_pdfocr_progress_fn *progress_fn, void *progress_arg);

/** Low-level wrapper for `::fz_pdfocr_writer_set_progress()`. */
FZ_FUNCTION void ll_fz_pdfocr_writer_set_progress(::fz_document_writer *writer, ::fz_pdfocr_progress_fn *progress, void *arg_3);

/** Low-level wrapper for `::fz_peek_byte()`. */
/**
	Peek at the next byte in a stream.

	stm: The stream to peek at.

	Returns -1 for EOF, or the next byte that will be read.
*/
FZ_FUNCTION int ll_fz_peek_byte(::fz_stream *stm);

/** Low-level wrapper for `::fz_pixmap_alpha()`. */
/**
	Return the number of alpha planes in a pixmap.

	Returns the number of alphas. Does not throw exceptions.
*/
FZ_FUNCTION int ll_fz_pixmap_alpha(const ::fz_pixmap *pix);

/** Low-level wrapper for `::fz_pixmap_bbox()`. */
/**
	Return the bounding box for a pixmap.
*/
FZ_FUNCTION ::fz_irect ll_fz_pixmap_bbox(const ::fz_pixmap *pix);

/** Low-level wrapper for `::fz_pixmap_colorants()`. */
/**
	Return the number of colorants in a pixmap.

	Returns the number of colorants (components, less any spots and
	alpha).
*/
FZ_FUNCTION int ll_fz_pixmap_colorants(const ::fz_pixmap *pix);

/** Low-level wrapper for `::fz_pixmap_colorspace()`. */
/**
	Return the colorspace of a pixmap

	Returns colorspace.
*/
FZ_FUNCTION ::fz_colorspace *ll_fz_pixmap_colorspace(const ::fz_pixmap *pix);

/** Low-level wrapper for `::fz_pixmap_components()`. */
/**
	Return the number of components in a pixmap.

	Returns the number of components (including spots and alpha).
*/
FZ_FUNCTION int ll_fz_pixmap_components(const ::fz_pixmap *pix);

/** Low-level wrapper for `::fz_pixmap_height()`. */
/**
	Return the height of the pixmap in pixels.
*/
FZ_FUNCTION int ll_fz_pixmap_height(const ::fz_pixmap *pix);

/** Low-level wrapper for `::fz_pixmap_image_tile()`. */
/**
	Retrieve the underlying fz_pixmap for an image.

	Returns a pointer to the underlying fz_pixmap for an image,
	or NULL if this image is not based upon an fz_pixmap.

	No reference is returned. Lifespan is limited to that of
	the image itself. If required, use fz_keep_pixmap to take
	a reference to keep it longer.
*/
FZ_FUNCTION ::fz_pixmap *ll_fz_pixmap_image_tile(::fz_pixmap_image *cimg);

/** Low-level wrapper for `::fz_pixmap_samples()`. */
/**
	Returns a pointer to the pixel data of a pixmap.

	Returns the pointer.
*/
FZ_FUNCTION unsigned char *ll_fz_pixmap_samples(const ::fz_pixmap *pix);

/** Low-level wrapper for `::fz_pixmap_samples_int()`. */
/** */
FZ_FUNCTION long long ll_fz_pixmap_samples_int(::fz_pixmap *pixmap);

/** Low-level wrapper for `::fz_pixmap_size()`. */
/**
	Return sizeof fz_pixmap plus size of data, in bytes.
*/
FZ_FUNCTION size_t ll_fz_pixmap_size(::fz_pixmap *pix);

/** Low-level wrapper for `::fz_pixmap_spots()`. */
/**
	Return the number of spots in a pixmap.

	Returns the number of spots (components, less colorants and
	alpha). Does not throw exceptions.
*/
FZ_FUNCTION int ll_fz_pixmap_spots(const ::fz_pixmap *pix);

/** Low-level wrapper for `::fz_pixmap_stride()`. */
/**
	Return the number of bytes in a row in the pixmap.
*/
FZ_FUNCTION int ll_fz_pixmap_stride(const ::fz_pixmap *pix);

/** Low-level wrapper for `::fz_pixmap_width()`. */
/**
	Return the width of the pixmap in pixels.
*/
FZ_FUNCTION int ll_fz_pixmap_width(const ::fz_pixmap *pix);

/** Low-level wrapper for `::fz_pixmap_x()`. */
/**
	Return the x value of the pixmap in pixels.
*/
FZ_FUNCTION int ll_fz_pixmap_x(const ::fz_pixmap *pix);

/** Low-level wrapper for `::fz_pixmap_y()`. */
/**
	Return the y value of the pixmap in pixels.
*/
FZ_FUNCTION int ll_fz_pixmap_y(const ::fz_pixmap *pix);

/** Low-level wrapper for `::fz_place_story()`. */
FZ_FUNCTION int ll_fz_place_story(::fz_story *story, ::fz_rect where, ::fz_rect *filled);

/** Low-level wrapper for `::fz_place_story_flags()`. */
FZ_FUNCTION int ll_fz_place_story_flags(::fz_story *story, ::fz_rect where, ::fz_rect *filled, int flags);

/** Low-level wrapper for `::fz_pool_alloc()`. */
/**
	Allocate a block of size bytes from the pool.
*/
FZ_FUNCTION void *ll_fz_pool_alloc(::fz_pool *pool, size_t size);

/** Low-level wrapper for `::fz_pool_size()`. */
/**
	The current size of the pool.

	The number of bytes of storage currently allocated to the pool.
	This is the total of the storage used for the blocks making
	up the pool, rather then total of the allocated blocks so far,
	so it will increase in 'lumps'.
	from the pool, then the pool size may still be X
*/
FZ_FUNCTION size_t ll_fz_pool_size(::fz_pool *pool);

/** Low-level wrapper for `::fz_pool_strdup()`. */
/**
	strdup equivalent allocating from the pool.
*/
FZ_FUNCTION char *ll_fz_pool_strdup(::fz_pool *pool, const char *s);

/** Low-level wrapper for `::fz_pop_clip()`. */
FZ_FUNCTION void ll_fz_pop_clip(::fz_device *dev);

/** Low-level wrapper for `::fz_post_scale()`. */
/**
	Scale a matrix by postmultiplication.

	m: Pointer to the matrix to scale

	sx, sy: Scaling factors along the X- and Y-axes. A scaling
	factor of 1.0 will not cause any scaling along the relevant
	axis.

	Returns m (updated).
*/
FZ_FUNCTION ::fz_matrix ll_fz_post_scale(::fz_matrix m, float sx, float sy);

/** Low-level wrapper for `::fz_pre_rotate()`. */
/**
	Rotate a transformation by premultiplying.

	The premultiplied matrix is of the form
	[ cos(deg) sin(deg) -sin(deg) cos(deg) 0 0 ].

	m: Pointer to matrix to premultiply.

	degrees: Degrees of counter clockwise rotation. Values less
	than zero and greater than 360 are handled as expected.

	Returns m (updated).
*/
FZ_FUNCTION ::fz_matrix ll_fz_pre_rotate(::fz_matrix m, float degrees);

/** Low-level wrapper for `::fz_pre_scale()`. */
/**
	Scale a matrix by premultiplication.

	m: Pointer to the matrix to scale

	sx, sy: Scaling factors along the X- and Y-axes. A scaling
	factor of 1.0 will not cause any scaling along the relevant
	axis.

	Returns m (updated).
*/
FZ_FUNCTION ::fz_matrix ll_fz_pre_scale(::fz_matrix m, float sx, float sy);

/** Low-level wrapper for `::fz_pre_shear()`. */
/**
	Premultiply a matrix with a shearing matrix.

	The shearing matrix is of the form [ 1 sy sx 1 0 0 ].

	m: pointer to matrix to premultiply

	sx, sy: Shearing factors. A shearing factor of 0.0 will not
	cause any shearing along the relevant axis.

	Returns m (updated).
*/
FZ_FUNCTION ::fz_matrix ll_fz_pre_shear(::fz_matrix m, float sx, float sy);

/** Low-level wrapper for `::fz_pre_translate()`. */
/**
	Translate a matrix by premultiplication.

	m: The matrix to translate

	tx, ty: Translation distances along the X- and Y-axes. A
	translation of 0 will not cause any translation along the
	relevant axis.

	Returns m.
*/
FZ_FUNCTION ::fz_matrix ll_fz_pre_translate(::fz_matrix m, float tx, float ty);

/** Low-level wrapper for `::fz_prepare_t3_glyph()`. */
/**
	Force a type3 font to cache the displaylist for a given glyph
	id.

	This caching can involve reading the underlying file, so must
	happen ahead of time, so we aren't suddenly forced to read the
	file while playing a displaylist back.
*/
FZ_FUNCTION void ll_fz_prepare_t3_glyph(::fz_font *font, int gid);

/** Low-level wrapper for `::fz_previous_page()`. */
/**
	Function to get the location of the previous page (allowing for
	the end of chapters etc). If already at the start of the
	document, returns the current page.
*/
FZ_FUNCTION ::fz_location ll_fz_previous_page(::fz_document *doc, ::fz_location loc);

/** Low-level wrapper for `::fz_print_stext_header_as_html()`. */
FZ_FUNCTION void ll_fz_print_stext_header_as_html(::fz_output *out);

/** Low-level wrapper for `::fz_print_stext_header_as_xhtml()`. */
FZ_FUNCTION void ll_fz_print_stext_header_as_xhtml(::fz_output *out);

/** Low-level wrapper for `::fz_print_stext_page_as_html()`. */
/**
	Output structured text to a file in HTML (visual) format.
*/
FZ_FUNCTION void ll_fz_print_stext_page_as_html(::fz_output *out, ::fz_stext_page *page, int id);

/** Low-level wrapper for `::fz_print_stext_page_as_json()`. */
/**
	Output structured text to a file in JSON format.
*/
FZ_FUNCTION void ll_fz_print_stext_page_as_json(::fz_output *out, ::fz_stext_page *page, float scale);

/** Low-level wrapper for `::fz_print_stext_page_as_text()`. */
/**
	Output structured text to a file in plain-text UTF-8 format.
*/
FZ_FUNCTION void ll_fz_print_stext_page_as_text(::fz_output *out, ::fz_stext_page *page);

/** Low-level wrapper for `::fz_print_stext_page_as_xhtml()`. */
/**
	Output structured text to a file in XHTML (semantic) format.
*/
FZ_FUNCTION void ll_fz_print_stext_page_as_xhtml(::fz_output *out, ::fz_stext_page *page, int id);

/** Low-level wrapper for `::fz_print_stext_page_as_xml()`. */
/**
	Output structured text to a file in XML format.
*/
FZ_FUNCTION void ll_fz_print_stext_page_as_xml(::fz_output *out, ::fz_stext_page *page, int id);

/** Low-level wrapper for `::fz_print_stext_trailer_as_html()`. */
FZ_FUNCTION void ll_fz_print_stext_trailer_as_html(::fz_output *out);

/** Low-level wrapper for `::fz_print_stext_trailer_as_xhtml()`. */
FZ_FUNCTION void ll_fz_print_stext_trailer_as_xhtml(::fz_output *out);

/** Low-level wrapper for `::fz_process_opened_pages()`. */
/**
	Iterates over all opened pages of the document, calling the
	provided callback for each page for processing. If the callback
	returns non-NULL then the iteration stops and that value is returned
	to the called of fz_process_opened_pages().

	The state pointer provided to fz_process_opened_pages() is
	passed on to the callback but is owned by the caller.

	Returns the first non-NULL value returned by the callback,
	or NULL if the callback returned NULL for all opened pages.
*/
FZ_FUNCTION void *ll_fz_process_opened_pages(::fz_document *doc, ::fz_process_opened_page_fn *process_openend_page, void *state);

/** Low-level wrapper for `::fz_process_shade()`. */
/**
	Process a shade, using supplied callback functions. This
	decomposes the shading to a mesh (even ones that are not
	natively meshes, such as linear or radial shadings), and
	processes triangles from those meshes.

	shade: The shade to process.

	ctm: The transform to use

	prepare: Callback function to 'prepare' each vertex.
	This function is passed an array of floats, and populates
	a fz_vertex structure.

	process: This function is passed 3 pointers to vertex
	structures, and actually performs the processing (typically
	filling the area between the vertexes).

	process_arg: An opaque argument passed through from caller
	to callback functions.
*/
FZ_FUNCTION void ll_fz_process_shade(::fz_shade *shade, ::fz_matrix ctm, ::fz_rect scissor, ::fz_shade_prepare_fn *prepare, ::fz_shade_process_fn *process, void *process_arg);

/** Low-level wrapper for `::fz_ptr_heap_insert()`. */
FZ_FUNCTION void ll_fz_ptr_heap_insert(::fz_ptr_heap *heap, void *v, int (*HEAP_CMP)(void **, void **));

/** Low-level wrapper for `::fz_ptr_heap_sort()`. */
FZ_FUNCTION void ll_fz_ptr_heap_sort(::fz_ptr_heap *heap, int (*HEAP_CMP)(void **, void **));

/** Low-level wrapper for `::fz_ptr_heap_uniq()`. */
FZ_FUNCTION void ll_fz_ptr_heap_uniq(::fz_ptr_heap *heap, int (*HEAP_CMP)(void **, void **));

/** Low-level wrapper for `::fz_purge_glyph_cache()`. */
/**
	Purge all the glyphs from the cache.
*/
FZ_FUNCTION void ll_fz_purge_glyph_cache();

/** Low-level wrapper for `::fz_quad_from_rect()`. */
/**
	Convert a rect to a quad (losslessly).
*/
FZ_FUNCTION ::fz_quad ll_fz_quad_from_rect(::fz_rect r);

/** Low-level wrapper for `::fz_quadto()`. */
/**
	Append a 'quadto' command to an open path. (For a
	quadratic bezier).

	path: The path to modify.

	x0, y0: The control coordinates for the quadratic curve.

	x1, y1: The end coordinates for the quadratic curve.

	Throws exceptions on failure to allocate, or attempting to
	modify a packed path.
*/
FZ_FUNCTION void ll_fz_quadto(::fz_path *path, float x0, float y0, float x1, float y1);

/** Low-level wrapper for `::fz_read()`. */
/**
	Read from a stream into a given data block.

	stm: The stream to read from.

	data: The data block to read into.

	len: The length of the data block (in bytes).

	Returns the number of bytes read. May throw exceptions.
*/
FZ_FUNCTION size_t ll_fz_read(::fz_stream *stm, unsigned char *data, size_t len);

/** Low-level wrapper for `::fz_read_all()`. */
/**
	Read all of a stream into a buffer.

	stm: The stream to read from

	initial: Suggested initial size for the buffer.

	Returns a buffer created from reading from the stream. May throw
	exceptions on failure to allocate.
*/
FZ_FUNCTION ::fz_buffer *ll_fz_read_all(::fz_stream *stm, size_t initial);

/** Low-level wrapper for `::fz_read_archive_entry()`. */
/**
	Reads all bytes in an archive entry
	into a buffer.

	name: Entry name to look for, this must be an exact match to
	the entry name in the archive.

	Throws an exception if a matching entry cannot be found.
*/
FZ_FUNCTION ::fz_buffer *ll_fz_read_archive_entry(::fz_archive *arch, const char *name);

/** Low-level wrapper for `::fz_read_best()`.

This function has out-params. Python/C# wrappers look like:
	`ll_fz_read_best(::fz_stream *stm, size_t initial, size_t worst_case)` => `(fz_buffer *, int truncated)`
*/
/**
	Attempt to read a stream into a buffer. If truncated
	is NULL behaves as fz_read_all, sets a truncated flag in case of
	error.

	stm: The stream to read from.

	initial: Suggested initial size for the buffer.

	truncated: Flag to store success/failure indication in.

	worst_case: 0 for unknown, otherwise an upper bound for the
	size of the stream.

	Returns a buffer created from reading from the stream.
*/
FZ_FUNCTION ::fz_buffer *ll_fz_read_best(::fz_stream *stm, size_t initial, int *truncated, size_t worst_case);

/** Low-level wrapper for `::fz_read_bits()`. */
/**
	Read the next n bits from a stream (assumed to
	be packed most significant bit first).

	stm: The stream to read from.

	n: The number of bits to read, between 1 and 8*sizeof(int)
	inclusive.

	Returns -1 for EOF, or the required number of bits.
*/
FZ_FUNCTION unsigned int ll_fz_read_bits(::fz_stream *stm, int n);

/** Low-level wrapper for `::fz_read_byte()`. */
/**
	Read the next byte from a stream.

	stm: The stream t read from.

	Returns -1 for end of stream, or the next byte. May
	throw exceptions.
*/
FZ_FUNCTION int ll_fz_read_byte(::fz_stream *stm);

/** Low-level wrapper for `::fz_read_file()`. */
/**
	Read all the contents of a file into a buffer.
*/
FZ_FUNCTION ::fz_buffer *ll_fz_read_file(const char *filename);

/** Low-level wrapper for `::fz_read_float()`. */
FZ_FUNCTION float ll_fz_read_float(::fz_stream *stm);

/** Low-level wrapper for `::fz_read_float_le()`. */
FZ_FUNCTION float ll_fz_read_float_le(::fz_stream *stm);

/** Low-level wrapper for `::fz_read_int16()`. */
FZ_FUNCTION int16_t ll_fz_read_int16(::fz_stream *stm);

/** Low-level wrapper for `::fz_read_int16_le()`. */
FZ_FUNCTION int16_t ll_fz_read_int16_le(::fz_stream *stm);

/** Low-level wrapper for `::fz_read_int32()`. */
FZ_FUNCTION int32_t ll_fz_read_int32(::fz_stream *stm);

/** Low-level wrapper for `::fz_read_int32_le()`. */
FZ_FUNCTION int32_t ll_fz_read_int32_le(::fz_stream *stm);

/** Low-level wrapper for `::fz_read_int64()`. */
FZ_FUNCTION int64_t ll_fz_read_int64(::fz_stream *stm);

/** Low-level wrapper for `::fz_read_int64_le()`. */
FZ_FUNCTION int64_t ll_fz_read_int64_le(::fz_stream *stm);

/** Low-level wrapper for `::fz_read_line()`. */
/**
	Read a line from stream into the buffer until either a
	terminating newline or EOF, which it replaces with a null byte
	('\0').

	Returns buf on success, and NULL when end of file occurs while
	no characters have been read.
*/
FZ_FUNCTION char *ll_fz_read_line(::fz_stream *stm, char *buf, size_t max);

/** Low-level wrapper for `::fz_read_rbits()`. */
/**
	Read the next n bits from a stream (assumed to
	be packed least significant bit first).

	stm: The stream to read from.

	n: The number of bits to read, between 1 and 8*sizeof(int)
	inclusive.

	Returns (unsigned int)-1 for EOF, or the required number of bits.
*/
FZ_FUNCTION unsigned int ll_fz_read_rbits(::fz_stream *stm, int n);

/** Low-level wrapper for `::fz_read_rune()`. */
/**
	Read a utf-8 rune from a stream.

	In the event of encountering badly formatted utf-8 codes
	(such as a leading code with an unexpected number of following
	codes) no error/exception is given, but undefined values may be
	returned.
*/
FZ_FUNCTION int ll_fz_read_rune(::fz_stream *in);

/** Low-level wrapper for `::fz_read_string()`. */
/**
	Read a null terminated string from the stream into
	a buffer of a given length. The buffer will be null terminated.
	Throws on failure (including the failure to fit the entire
	string including the terminator into the buffer).
*/
FZ_FUNCTION void ll_fz_read_string(::fz_stream *stm, char *buffer, int len);

/** Low-level wrapper for `::fz_read_uint16()`. */
/**
	fz_read_[u]int(16|24|32|64)(_le)?

	Read a 16/32/64 bit signed/unsigned integer from stream,
	in big or little-endian byte orders.

	Throws an exception if EOF is encountered.
*/
FZ_FUNCTION uint16_t ll_fz_read_uint16(::fz_stream *stm);

/** Low-level wrapper for `::fz_read_uint16_le()`. */
FZ_FUNCTION uint16_t ll_fz_read_uint16_le(::fz_stream *stm);

/** Low-level wrapper for `::fz_read_uint24()`. */
FZ_FUNCTION uint32_t ll_fz_read_uint24(::fz_stream *stm);

/** Low-level wrapper for `::fz_read_uint24_le()`. */
FZ_FUNCTION uint32_t ll_fz_read_uint24_le(::fz_stream *stm);

/** Low-level wrapper for `::fz_read_uint32()`. */
FZ_FUNCTION uint32_t ll_fz_read_uint32(::fz_stream *stm);

/** Low-level wrapper for `::fz_read_uint32_le()`. */
FZ_FUNCTION uint32_t ll_fz_read_uint32_le(::fz_stream *stm);

/** Low-level wrapper for `::fz_read_uint64()`. */
FZ_FUNCTION uint64_t ll_fz_read_uint64(::fz_stream *stm);

/** Low-level wrapper for `::fz_read_uint64_le()`. */
FZ_FUNCTION uint64_t ll_fz_read_uint64_le(::fz_stream *stm);

/** Low-level wrapper for `::fz_read_utf16_be()`. */
FZ_FUNCTION int ll_fz_read_utf16_be(::fz_stream *stm);

/** Low-level wrapper for `::fz_read_utf16_le()`. */
/**
	Read a utf-16 rune from a stream. (little endian and
	big endian respectively).

	In the event of encountering badly formatted utf-16 codes
	(mismatched surrogates) no error/exception is given, but
	undefined values may be returned.
*/
FZ_FUNCTION int ll_fz_read_utf16_le(::fz_stream *stm);

/** Low-level wrapper for `::fz_realloc()`. */
/**
	Reallocates a block of memory to given size. Existing contents
	up to min(old_size,new_size) are maintained. The rest of the
	block is uninitialised.

	fz_realloc(ctx, NULL, size) behaves like fz_malloc(ctx, size).

	fz_realloc(ctx, p, 0); behaves like fz_free(ctx, p).

	Throws exception in the event of failure to allocate.
*/
FZ_FUNCTION void *ll_fz_realloc(void *p, size_t size);

/** Low-level wrapper for `::fz_realloc_no_throw()`. */
/**
	fz_realloc equivalent that returns NULL rather than throwing
	exceptions.
*/
FZ_FUNCTION void *ll_fz_realloc_no_throw(void *p, size_t size);

/** Low-level wrapper for `::fz_realpath()`. */
/**
	Resolve a path to an absolute file name.
	The resolved path buffer must be of at least PATH_MAX size.
*/
FZ_FUNCTION char *ll_fz_realpath(const char *path, char *resolved_path);

/** Low-level wrapper for `::fz_recognize_document()`. */
/**
	Given a magic find a document handler that can handle a
	document of this type.

	magic: Can be a filename extension (including initial period) or
	a mimetype.
*/
FZ_FUNCTION const ::fz_document_handler *ll_fz_recognize_document(const char *magic);

/** Low-level wrapper for `::fz_recognize_document_content()`. */
/**
	Given a filename find a document handler that can handle a
	document of this type.

	filename: The filename of the document. This will be opened and sampled
	to check data.
*/
FZ_FUNCTION const ::fz_document_handler *ll_fz_recognize_document_content(const char *filename);

/** Low-level wrapper for `::fz_recognize_document_stream_and_dir_content()`. */
/**
	Given a magic find a document handler that can handle a
	document of this type.

	stream: the file stream to sample. May be NULL if the document is
	a directory.

	dir: an fz_archive representing the directory from which the
	stream was opened (or NULL).

	magic: Can be a filename extension (including initial period) or
	a mimetype.
*/
FZ_FUNCTION const ::fz_document_handler *ll_fz_recognize_document_stream_and_dir_content(::fz_stream *stream, ::fz_archive *dir, const char *magic);

/** Low-level wrapper for `::fz_recognize_document_stream_content()`. */
/**
	Given a magic find a document handler that can handle a
	document of this type.

	stream: the file stream to sample. May be NULL if the document is
	a directory.

	magic: Can be a filename extension (including initial period) or
	a mimetype.
*/
FZ_FUNCTION const ::fz_document_handler *ll_fz_recognize_document_stream_content(::fz_stream *stream, const char *magic);

/** Low-level wrapper for `::fz_recognize_image_format()`. */
/**
	Recognise image format strings in the first 8 bytes from image
	data.
*/
FZ_FUNCTION int ll_fz_recognize_image_format(unsigned char p[8]);

/** Low-level wrapper for `::fz_rect_from_irect()`. */
/**
	Convert a bbox into a rect.

	For our purposes, a rect can represent all the values we meet in
	a bbox, so nothing can go wrong.

	rect: A place to store the generated rectangle.

	bbox: The bbox to convert.

	Returns rect (updated).
*/
FZ_FUNCTION ::fz_rect ll_fz_rect_from_irect(::fz_irect bbox);

/** Low-level wrapper for `::fz_rect_from_quad()`. */
/**
	Convert a quad to the smallest rect that covers it.
*/
FZ_FUNCTION ::fz_rect ll_fz_rect_from_quad(::fz_quad q);

/** Low-level wrapper for `::fz_rectto()`. */
/**
	Append a 'rectto' command to an open path.

	The rectangle is equivalent to:
		moveto x0 y0
		lineto x1 y0
		lineto x1 y1
		lineto x0 y1
		closepath

	path: The path to modify.

	x0, y0: First corner of the rectangle.

	x1, y1: Second corner of the rectangle.

	Throws exceptions on failure to allocate, or attempting to
	modify a packed path.
*/
FZ_FUNCTION void ll_fz_rectto(::fz_path *path, float x0, float y0, float x1, float y1);

/** Low-level wrapper for `::fz_register_archive_handler()`. */
FZ_FUNCTION void ll_fz_register_archive_handler(const ::fz_archive_handler *handler);

/** Low-level wrapper for `::fz_register_document_handler()`. */
/**
	Register a handler for a document type.

	handler: The handler to register. This must live on for the duration of the
	use of this handler. It will be passed back to the handler for calls so
	the caller can use it to retrieve state.
*/
FZ_FUNCTION void ll_fz_register_document_handler(const ::fz_document_handler *handler);

/** Low-level wrapper for `::fz_register_document_handlers()`. */
/**
	Register handlers for all the standard document types supported in
	this build.
*/
FZ_FUNCTION void ll_fz_register_document_handlers();

/** Low-level wrapper for `::fz_remove_item()`. */
/**
	Remove an item from the store.

	If an item indexed by the given key exists in the store, remove
	it.

	drop: The function used to free the value (to ensure we get a
	value of the correct type).

	key: The key used to find the item to remove.

	type: Functions used to manipulate the key.
*/
FZ_FUNCTION void ll_fz_remove_item(::fz_store_drop_fn *drop, void *key, const ::fz_store_type *type);

/** Low-level wrapper for `::fz_render_flags()`. */
FZ_FUNCTION void ll_fz_render_flags(::fz_device *dev, int set, int clear);

/** Low-level wrapper for `::fz_render_glyph_pixmap()`. */
/**
	Create a pixmap containing a rendered glyph.

	Lookup gid from font, clip it with scissor, and rendering it
	with aa bits of antialiasing into a new pixmap.

	The caller takes ownership of the pixmap and so must free it.

	Note: This function is no longer used for normal rendering
	operations, and is kept around just because we use it in the
	app. It should be considered "at risk" of removal from the API.
*/
FZ_FUNCTION ::fz_pixmap *ll_fz_render_glyph_pixmap(::fz_font *font, int gid, ::fz_matrix *ctm, const ::fz_irect *scissor, int aa);

/** Low-level wrapper for `::fz_render_t3_glyph_direct()`. */
/**
	Nasty PDF interpreter specific hernia, required to allow the
	interpreter to replay glyphs from a type3 font directly into
	the target device.

	This is only used in exceptional circumstances (such as type3
	glyphs that inherit current graphics state, or nested type3
	glyphs).
*/
FZ_FUNCTION void ll_fz_render_t3_glyph_direct(::fz_device *dev, ::fz_font *font, int gid, ::fz_matrix trm, void *gstate, ::fz_default_colorspaces *def_cs);

/** Low-level wrapper for `::fz_rendering_intent_name()`. */
/**
	Map from enumerated rendering intent to string.

	The returned string is static and therefore must not be freed.
*/
FZ_FUNCTION const char *ll_fz_rendering_intent_name(int ri);

/** Low-level wrapper for `::fz_report_error()`. */
FZ_FUNCTION void ll_fz_report_error();

/** Low-level wrapper for `::fz_reset_output()`. */
/**
	Reset a closed output stream. Returns state to
	(broadly) that which it was in when opened. Not
	all outputs can be reset, so this may throw an
	exception.
*/
FZ_FUNCTION void ll_fz_reset_output(::fz_output *arg_1);

/** Low-level wrapper for `::fz_reset_story()`. */
FZ_FUNCTION void ll_fz_reset_story(::fz_story *story);

/** Low-level wrapper for `::fz_resize_buffer()`. */
/**
	Ensure that a buffer has a given capacity,
	truncating data if required.

	capacity: The desired capacity for the buffer. If the current
	size of the buffer contents is smaller than capacity, it is
	truncated.
*/
FZ_FUNCTION void ll_fz_resize_buffer(::fz_buffer *buf, size_t capacity);

/** Low-level wrapper for `::fz_resolve_link()`.

This function has out-params. Python/C# wrappers look like:
	`ll_fz_resolve_link(::fz_document *doc, const char *uri)` => `(fz_location, float xp, float yp)`
*/
/**
	Resolve an internal link to a page number.

	xp, yp: Pointer to store coordinate of destination on the page.

	Returns (-1,-1) if the URI cannot be resolved.
*/
FZ_FUNCTION ::fz_location ll_fz_resolve_link(::fz_document *doc, const char *uri, float *xp, float *yp);

/** Low-level wrapper for `::fz_resolve_link_dest()`. */
/**
	Resolve an internal link to a page number, location, and possible viewing parameters.

	Returns location (-1,-1) if the URI cannot be resolved.
*/
FZ_FUNCTION ::fz_link_dest ll_fz_resolve_link_dest(::fz_document *doc, const char *uri);

/** Low-level wrapper for `::fz_rethrow()`. */
FZ_FUNCTION void ll_fz_rethrow();

/** Low-level wrapper for `::fz_rethrow_if()`. */
/**
	Within an fz_catch() block, rethrow the current exception
	if the errcode of the current exception matches.

	This assumes no intervening use of fz_try/fz_catch.
*/
FZ_FUNCTION void ll_fz_rethrow_if(int errcode);

/** Low-level wrapper for `::fz_rethrow_unless()`. */
FZ_FUNCTION void ll_fz_rethrow_unless(int errcode);

/** Low-level wrapper for `::fz_rotate()`. */
/**
	Create a rotation matrix.

	The returned matrix is of the form
	[ cos(deg) sin(deg) -sin(deg) cos(deg) 0 0 ].

	m: Pointer to place to store matrix

	degrees: Degrees of counter clockwise rotation. Values less
	than zero and greater than 360 are handled as expected.

	Returns m.
*/
FZ_FUNCTION ::fz_matrix ll_fz_rotate(float degrees);

/** Low-level wrapper for `::fz_round_rect()`. */
/**
	Round rectangle coordinates.

	Coordinates in a bounding box are integers, so rounding of the
	rects coordinates takes place. The top left corner is rounded
	upwards and left while the bottom right corner is rounded
	downwards and to the right.

	This differs from fz_irect_from_rect, in that fz_irect_from_rect
	slavishly follows the numbers (i.e any slight over/under
	calculations can cause whole extra pixels to be added).
	fz_round_rect allows for a small amount of rounding error when
	calculating the bbox.
*/
FZ_FUNCTION ::fz_irect ll_fz_round_rect(::fz_rect rect);

/** Low-level wrapper for `::fz_run_display_list()`. */
/**
	(Re)-run a display list through a device.

	list: A display list, created by fz_new_display_list and
	populated with objects from a page by running fz_run_page on a
	device obtained from fz_new_list_device.

	ctm: Transform to apply to display list contents. May include
	for example scaling and rotation, see fz_scale, fz_rotate and
	fz_concat. Set to fz_identity if no transformation is desired.

	scissor: Only the part of the contents of the display list
	visible within this area will be considered when the list is
	run through the device. This does not imply for tile objects
	contained in the display list.

	cookie: Communication mechanism between caller and library
	running the page. Intended for multi-threaded applications,
	while single-threaded applications set cookie to NULL. The
	caller may abort an ongoing page run. Cookie also communicates
	progress information back to the caller. The fields inside
	cookie are continually updated while the page is being run.
*/
FZ_FUNCTION void ll_fz_run_display_list(::fz_display_list *list, ::fz_device *dev, ::fz_matrix ctm, ::fz_rect scissor, ::fz_cookie *cookie);

/** Low-level wrapper for `::fz_run_document_structure()`. */
/**
	Run the document structure through a device.

	doc: Document in question.

	dev: Device obtained from fz_new_*_device.

	cookie: Communication mechanism between caller and library.
	Intended for multi-threaded applications, while
	single-threaded applications set cookie to NULL. The
	caller may abort an ongoing rendering of a page. Cookie also
	communicates progress information back to the caller. The
	fields inside cookie are continually updated while the page is
	rendering.
*/
FZ_FUNCTION void ll_fz_run_document_structure(::fz_document *doc, ::fz_device *dev, ::fz_cookie *cookie);

/** Low-level wrapper for `::fz_run_page()`. */
/**
	Run a page through a device.

	page: Page obtained from fz_load_page.

	dev: Device obtained from fz_new_*_device.

	transform: Transform to apply to page. May include for example
	scaling and rotation, see fz_scale, fz_rotate and fz_concat.
	Set to fz_identity if no transformation is desired.

	cookie: Communication mechanism between caller and library
	rendering the page. Intended for multi-threaded applications,
	while single-threaded applications set cookie to NULL. The
	caller may abort an ongoing rendering of a page. Cookie also
	communicates progress information back to the caller. The
	fields inside cookie are continually updated while the page is
	rendering.
*/
FZ_FUNCTION void ll_fz_run_page(::fz_page *page, ::fz_device *dev, ::fz_matrix transform, ::fz_cookie *cookie);

/** Low-level wrapper for `::fz_run_page_annots()`. */
/**
	Run the annotations on a page through a device.
*/
FZ_FUNCTION void ll_fz_run_page_annots(::fz_page *page, ::fz_device *dev, ::fz_matrix transform, ::fz_cookie *cookie);

/** Low-level wrapper for `::fz_run_page_contents()`. */
/**
	Run a page through a device. Just the main
	page content, without the annotations, if any.

	page: Page obtained from fz_load_page.

	dev: Device obtained from fz_new_*_device.

	transform: Transform to apply to page. May include for example
	scaling and rotation, see fz_scale, fz_rotate and fz_concat.
	Set to fz_identity if no transformation is desired.

	cookie: Communication mechanism between caller and library
	rendering the page. Intended for multi-threaded applications,
	while single-threaded applications set cookie to NULL. The
	caller may abort an ongoing rendering of a page. Cookie also
	communicates progress information back to the caller. The
	fields inside cookie are continually updated while the page is
	rendering.
*/
FZ_FUNCTION void ll_fz_run_page_contents(::fz_page *page, ::fz_device *dev, ::fz_matrix transform, ::fz_cookie *cookie);

/** Low-level wrapper for `::fz_run_page_widgets()`. */
/**
	Run the widgets on a page through a device.
*/
FZ_FUNCTION void ll_fz_run_page_widgets(::fz_page *page, ::fz_device *dev, ::fz_matrix transform, ::fz_cookie *cookie);

/** Low-level wrapper for `::fz_run_t3_glyph()`. */
/**
	Run a glyph from a Type3 font to
	a given device.

	font: The font to find the glyph in.

	gid: The glyph to run.

	trm: The transform to apply.

	dev: The device to render onto.
*/
FZ_FUNCTION void ll_fz_run_t3_glyph(::fz_font *font, int gid, ::fz_matrix trm, struct ::fz_device *dev);

/** Low-level wrapper for `::fz_runeidx()`. */
/**
	Compute the index of a rune in a string.

	str: Pointer to beginning of a string.

	p: Pointer to a char in str.

	Returns the index of the rune pointed to by p in str.
*/
FZ_FUNCTION int ll_fz_runeidx(const char *str, const char *p);

/** Low-level wrapper for `::fz_runelen()`. */
/**
	Count how many chars are required to represent a rune.

	rune: The rune to encode.

	Returns the number of bytes required to represent this run in
	UTF8.
*/
FZ_FUNCTION int ll_fz_runelen(int rune);

/** Low-level wrapper for `::fz_runeptr()`. */
/**
	Obtain a pointer to the char representing the rune
	at a given index.

	str: Pointer to beginning of a string.

	idx: Index of a rune to return a char pointer to.

	Returns a pointer to the char where the desired rune starts,
	or NULL if the string ends before the index is reached.
*/
FZ_FUNCTION const char *ll_fz_runeptr(const char *str, int idx);

/** Low-level wrapper for `::fz_runetochar()`. */
/**
	UTF8 encode a rune to a sequence of chars.

	str: Pointer to a place to put the UTF8 encoded character.

	rune: Pointer to a 'rune'.

	Returns the number of bytes the rune took to output.
*/
FZ_FUNCTION int ll_fz_runetochar(char *str, int rune);

/** Low-level wrapper for `::fz_samples_get()`. */
/**
Provides simple (but slow) access to pixmap data from Python and C#.
*/
FZ_FUNCTION int ll_fz_samples_get(::fz_pixmap *pixmap, int offset);

/** Low-level wrapper for `::fz_samples_set()`. */
/**
Provides simple (but slow) write access to pixmap data from Python and
C#.
*/
FZ_FUNCTION void ll_fz_samples_set(::fz_pixmap *pixmap, int offset, int value);

/** Low-level wrapper for `::fz_save_accelerator()`. */
/**
	Save accelerator data for the document to a given file.
*/
FZ_FUNCTION void ll_fz_save_accelerator(::fz_document *doc, const char *accel);

/** Low-level wrapper for `::fz_save_bitmap_as_pbm()`. */
/**
	Save a bitmap as a pbm.
*/
FZ_FUNCTION void ll_fz_save_bitmap_as_pbm(::fz_bitmap *bitmap, const char *filename);

/** Low-level wrapper for `::fz_save_bitmap_as_pcl()`. */
/**
	Save a bitmap as mono PCL.
*/
FZ_FUNCTION void ll_fz_save_bitmap_as_pcl(::fz_bitmap *bitmap, char *filename, int append, const ::fz_pcl_options *pcl);

/** Low-level wrapper for `::fz_save_bitmap_as_pkm()`. */
/**
	Save a CMYK bitmap as a pkm.
*/
FZ_FUNCTION void ll_fz_save_bitmap_as_pkm(::fz_bitmap *bitmap, const char *filename);

/** Low-level wrapper for `::fz_save_bitmap_as_pwg()`. */
/**
	Save a bitmap as a PWG.
*/
FZ_FUNCTION void ll_fz_save_bitmap_as_pwg(::fz_bitmap *bitmap, char *filename, int append, const ::fz_pwg_options *pwg);

/** Low-level wrapper for `::fz_save_buffer()`. */
/**
	Save the contents of a buffer to a file.
*/
FZ_FUNCTION void ll_fz_save_buffer(::fz_buffer *buf, const char *filename);

/** Low-level wrapper for `::fz_save_pixmap_as_jpeg()`. */
/**
	Save a pixmap as a JPEG.
*/
FZ_FUNCTION void ll_fz_save_pixmap_as_jpeg(::fz_pixmap *pixmap, const char *filename, int quality);

/** Low-level wrapper for `::fz_save_pixmap_as_jpx()`. */
/**
	Save pixmap data as JP2K with no subsampling.

	quality = 100 = lossless
	otherwise for a factor of x compression use 100-x. (so 80 is 1:20 compression)
*/
FZ_FUNCTION void ll_fz_save_pixmap_as_jpx(::fz_pixmap *pixmap, const char *filename, int q);

/** Low-level wrapper for `::fz_save_pixmap_as_pam()`. */
/**
	Save a pixmap as a pnm (greyscale, rgb or cmyk, with or without
	alpha).
*/
FZ_FUNCTION void ll_fz_save_pixmap_as_pam(::fz_pixmap *pixmap, const char *filename);

/** Low-level wrapper for `::fz_save_pixmap_as_pbm()`. */
/**
	Save a pixmap as a pbm. (Performing halftoning).
*/
FZ_FUNCTION void ll_fz_save_pixmap_as_pbm(::fz_pixmap *pixmap, const char *filename);

/** Low-level wrapper for `::fz_save_pixmap_as_pcl()`. */
/**
	Save an (RGB) pixmap as color PCL.
*/
FZ_FUNCTION void ll_fz_save_pixmap_as_pcl(::fz_pixmap *pixmap, char *filename, int append, const ::fz_pcl_options *pcl);

/** Low-level wrapper for `::fz_save_pixmap_as_pclm()`. */
/**
	Save a (Greyscale or RGB) pixmap as pclm.
*/
FZ_FUNCTION void ll_fz_save_pixmap_as_pclm(::fz_pixmap *pixmap, char *filename, int append, const ::fz_pclm_options *options);

/** Low-level wrapper for `::fz_save_pixmap_as_pdfocr()`. */
/**
	Save a (Greyscale or RGB) pixmap as pdfocr.
*/
FZ_FUNCTION void ll_fz_save_pixmap_as_pdfocr(::fz_pixmap *pixmap, char *filename, int append, const ::fz_pdfocr_options *options);

/** Low-level wrapper for `::fz_save_pixmap_as_pkm()`. */
/**
	Save a CMYK pixmap as a pkm. (Performing halftoning).
*/
FZ_FUNCTION void ll_fz_save_pixmap_as_pkm(::fz_pixmap *pixmap, const char *filename);

/** Low-level wrapper for `::fz_save_pixmap_as_png()`. */
/**
	Save a (Greyscale or RGB) pixmap as a png.
*/
FZ_FUNCTION void ll_fz_save_pixmap_as_png(::fz_pixmap *pixmap, const char *filename);

/** Low-level wrapper for `::fz_save_pixmap_as_pnm()`. */
/**
	Save a pixmap as a pnm (greyscale or rgb, no alpha).
*/
FZ_FUNCTION void ll_fz_save_pixmap_as_pnm(::fz_pixmap *pixmap, const char *filename);

/** Low-level wrapper for `::fz_save_pixmap_as_ps()`. */
/**
	Save a (gray, rgb, or cmyk, no alpha) pixmap out as postscript.
*/
FZ_FUNCTION void ll_fz_save_pixmap_as_ps(::fz_pixmap *pixmap, char *filename, int append);

/** Low-level wrapper for `::fz_save_pixmap_as_psd()`. */
/**
	Save a pixmap as a PSD file.
*/
FZ_FUNCTION void ll_fz_save_pixmap_as_psd(::fz_pixmap *pixmap, const char *filename);

/** Low-level wrapper for `::fz_save_pixmap_as_pwg()`. */
/**
	Save a pixmap as a PWG.
*/
FZ_FUNCTION void ll_fz_save_pixmap_as_pwg(::fz_pixmap *pixmap, char *filename, int append, const ::fz_pwg_options *pwg);

/** Low-level wrapper for `::fz_scale()`. */
/**
	Create a scaling matrix.

	The returned matrix is of the form [ sx 0 0 sy 0 0 ].

	m: Pointer to the matrix to populate

	sx, sy: Scaling factors along the X- and Y-axes. A scaling
	factor of 1.0 will not cause any scaling along the relevant
	axis.

	Returns m.
*/
FZ_FUNCTION ::fz_matrix ll_fz_scale(float sx, float sy);

/** Low-level wrapper for `::fz_scale_pixmap()`. */
FZ_FUNCTION ::fz_pixmap *ll_fz_scale_pixmap(::fz_pixmap *src, float x, float y, float w, float h, const ::fz_irect *clip);

/** Low-level wrapper for `::fz_search_chapter_page_number()`.

This function has out-params. Python/C# wrappers look like:
	`ll_fz_search_chapter_page_number(::fz_document *doc, int chapter, int page, const char *needle, ::fz_quad *hit_bbox, int hit_max)` => `(int, int hit_mark)`
*/
FZ_FUNCTION int ll_fz_search_chapter_page_number(::fz_document *doc, int chapter, int page, const char *needle, int *hit_mark, ::fz_quad *hit_bbox, int hit_max);

/** Low-level wrapper for `::fz_search_display_list()`.

This function has out-params. Python/C# wrappers look like:
	`ll_fz_search_display_list(::fz_display_list *list, const char *needle, ::fz_quad *hit_bbox, int hit_max)` => `(int, int hit_mark)`
*/
FZ_FUNCTION int ll_fz_search_display_list(::fz_display_list *list, const char *needle, int *hit_mark, ::fz_quad *hit_bbox, int hit_max);

/** Low-level wrapper for `::fz_search_page()`.

This function has out-params. Python/C# wrappers look like:
	`ll_fz_search_page(::fz_page *page, const char *needle, ::fz_quad *hit_bbox, int hit_max)` => `(int, int hit_mark)`
*/
/**
	Search for the 'needle' text on the page.
	Record the hits in the hit_bbox array and return the number of
	hits. Will stop looking once it has filled hit_max rectangles.
*/
FZ_FUNCTION int ll_fz_search_page(::fz_page *page, const char *needle, int *hit_mark, ::fz_quad *hit_bbox, int hit_max);

/** Low-level wrapper for `::fz_search_page2()`. */
/**
C++ alternative to fz_search_page() that returns information in a std::vector.
*/
FZ_FUNCTION std::vector<fz_search_page2_hit> ll_fz_search_page2(::fz_document *doc, int number, const char *needle, int hit_max);

/** Low-level wrapper for `::fz_search_page_number()`.

This function has out-params. Python/C# wrappers look like:
	`ll_fz_search_page_number(::fz_document *doc, int number, const char *needle, ::fz_quad *hit_bbox, int hit_max)` => `(int, int hit_mark)`
*/
FZ_FUNCTION int ll_fz_search_page_number(::fz_document *doc, int number, const char *needle, int *hit_mark, ::fz_quad *hit_bbox, int hit_max);

/** Low-level wrapper for `::fz_search_stext_page()`.

This function has out-params. Python/C# wrappers look like:
	`ll_fz_search_stext_page(::fz_stext_page *text, const char *needle, ::fz_quad *hit_bbox, int hit_max)` => `(int, int hit_mark)`
*/
/**
	Search for occurrence of 'needle' in text page.

	Return the number of hits and store hit quads in the passed in
	array.

	NOTE: This is an experimental interface and subject to change
	without notice.
*/
FZ_FUNCTION int ll_fz_search_stext_page(::fz_stext_page *text, const char *needle, int *hit_mark, ::fz_quad *hit_bbox, int hit_max);

/** Low-level wrapper for `::fz_seek()`. */
/**
	Seek within a stream.

	stm: The stream to seek within.

	offset: The offset to seek to.

	whence: From where the offset is measured (see fseek).
	SEEK_SET - start of stream.
	SEEK_CUR - current position.
	SEEK_END - end of stream.

*/
FZ_FUNCTION void ll_fz_seek(::fz_stream *stm, int64_t offset, int whence);

/** Low-level wrapper for `::fz_seek_output()`. */
/**
	Seek to the specified file position.
	See fseek for arguments.

	Throw an error on unseekable outputs.
*/
FZ_FUNCTION void ll_fz_seek_output(::fz_output *out, int64_t off, int whence);

/** Low-level wrapper for `::fz_separation_current_behavior()`. */
/**
	Test for the current behavior of a separation.
*/
FZ_FUNCTION ::fz_separation_behavior ll_fz_separation_current_behavior(const ::fz_separations *sep, int separation);

/** Low-level wrapper for `::fz_separation_equivalent()`.

This function has out-params. Python/C# wrappers look like:
	`ll_fz_separation_equivalent(const ::fz_separations *seps, int idx, ::fz_colorspace *dst_cs, ::fz_colorspace *prf, ::fz_color_params color_params)` => float dst_color
*/
/**
	Get the equivalent separation color in a given colorspace.
*/
FZ_FUNCTION void ll_fz_separation_equivalent(const ::fz_separations *seps, int idx, ::fz_colorspace *dst_cs, float *dst_color, ::fz_colorspace *prf, ::fz_color_params color_params);

/** Low-level wrapper for `::fz_separation_name()`. */
FZ_FUNCTION const char *ll_fz_separation_name(const ::fz_separations *sep, int separation);

/** Low-level wrapper for `::fz_set_aa_level()`. */
/**
	Set the number of bits of antialiasing we should
	use (for both text and graphics).

	bits: The number of bits of antialiasing to use (values are
	clamped to within the 0 to 8 range).
*/
FZ_FUNCTION void ll_fz_set_aa_level(int bits);

/** Low-level wrapper for `::fz_set_compressed_image_buffer()`. */
FZ_FUNCTION void ll_fz_set_compressed_image_buffer(::fz_compressed_image *cimg, ::fz_compressed_buffer *buf);

/** Low-level wrapper for `::fz_set_default_cmyk()`. */
FZ_FUNCTION void ll_fz_set_default_cmyk(::fz_default_colorspaces *default_cs, ::fz_colorspace *cs);

/** Low-level wrapper for `::fz_set_default_colorspaces()`. */
FZ_FUNCTION void ll_fz_set_default_colorspaces(::fz_device *dev, ::fz_default_colorspaces *default_cs);

/** Low-level wrapper for `::fz_set_default_gray()`. */
/**
	Set new defaults within the default colorspace structure.

	New references are taken to the new default, and references to
	the old defaults dropped.

	Never throws exceptions.
*/
FZ_FUNCTION void ll_fz_set_default_gray(::fz_default_colorspaces *default_cs, ::fz_colorspace *cs);

/** Low-level wrapper for `::fz_set_default_output_intent()`. */
FZ_FUNCTION void ll_fz_set_default_output_intent(::fz_default_colorspaces *default_cs, ::fz_colorspace *cs);

/** Low-level wrapper for `::fz_set_default_rgb()`. */
FZ_FUNCTION void ll_fz_set_default_rgb(::fz_default_colorspaces *default_cs, ::fz_colorspace *cs);

/** Low-level wrapper for `::fz_set_error_callback()`. */
/**
	Set the error callback. This will be called as part of the
	exception handling.

	The callback must not throw exceptions!
*/
FZ_FUNCTION void ll_fz_set_error_callback(::fz_error_cb *error_cb, void *user);

/** Low-level wrapper for `::fz_set_font_bbox()`. */
/**
	Set the font bbox.

	font: The font to set the bbox for.

	xmin, ymin, xmax, ymax: The bounding box.
*/
FZ_FUNCTION void ll_fz_set_font_bbox(::fz_font *font, float xmin, float ymin, float xmax, float ymax);

/** Low-level wrapper for `::fz_set_font_embedding()`. */
/**
	Control whether a given font should be embedded or not when writing.
*/
FZ_FUNCTION void ll_fz_set_font_embedding(::fz_font *font, int embed);

/** Low-level wrapper for `::fz_set_graphics_aa_level()`. */
/**
	Set the number of bits of antialiasing we
	should use for graphics.

	bits: The number of bits of antialiasing to use (values are
	clamped to within the 0 to 8 range).
*/
FZ_FUNCTION void ll_fz_set_graphics_aa_level(int bits);

/** Low-level wrapper for `::fz_set_graphics_min_line_width()`. */
/**
	Set the minimum line width to be
	used for stroked lines.

	min_line_width: The minimum line width to use (in pixels).
*/
FZ_FUNCTION void ll_fz_set_graphics_min_line_width(float min_line_width);

/** Low-level wrapper for `::fz_set_link_rect()`. */
FZ_FUNCTION void ll_fz_set_link_rect(::fz_link *link, ::fz_rect rect);

/** Low-level wrapper for `::fz_set_link_uri()`. */
FZ_FUNCTION void ll_fz_set_link_uri(::fz_link *link, const char *uri);

/** Low-level wrapper for `::fz_set_metadata()`. */
FZ_FUNCTION void ll_fz_set_metadata(::fz_document *doc, const char *key, const char *value);

/** Low-level wrapper for `::fz_set_pixmap_image_tile()`. */
FZ_FUNCTION void ll_fz_set_pixmap_image_tile(::fz_pixmap_image *cimg, ::fz_pixmap *pix);

/** Low-level wrapper for `::fz_set_pixmap_resolution()`. */
/**
	Set the pixels per inch resolution of the pixmap.
*/
FZ_FUNCTION void ll_fz_set_pixmap_resolution(::fz_pixmap *pix, int xres, int yres);

/** Low-level wrapper for `::fz_set_separation_behavior()`. */
/**
	Control the rendering of a given separation.
*/
FZ_FUNCTION void ll_fz_set_separation_behavior(::fz_separations *sep, int separation, ::fz_separation_behavior behavior);

/** Low-level wrapper for `::fz_set_stddbg()`. */
/**
	Set the output stream to be used for fz_stddbg. Set to NULL to
	reset to default (stderr).
*/
FZ_FUNCTION void ll_fz_set_stddbg(::fz_output *out);

/** Low-level wrapper for `::fz_set_text_aa_level()`. */
/**
	Set the number of bits of antialiasing we
	should use for text.

	bits: The number of bits of antialiasing to use (values are
	clamped to within the 0 to 8 range).
*/
FZ_FUNCTION void ll_fz_set_text_aa_level(int bits);

/** Low-level wrapper for `::fz_set_use_document_css()`. */
/**
	Toggle whether to respect document styles in HTML and EPUB.
*/
FZ_FUNCTION void ll_fz_set_use_document_css(int use);

/** Low-level wrapper for `::fz_set_user_context()`. */
/**
	Set the user field in the context.

	NULL initially, this field can be set to any opaque value
	required by the user. It is copied on clones.
*/
FZ_FUNCTION void ll_fz_set_user_context(void *user);

/** Low-level wrapper for `::fz_set_user_css()`. */
/**
	Set the user stylesheet source text for use with HTML and EPUB.
*/
FZ_FUNCTION void ll_fz_set_user_css(const char *text);

/** Low-level wrapper for `::fz_set_warning_callback()`. */
/**
	Set the warning callback. This will be called as part of the
	exception handling.

	The callback must not throw exceptions!
*/
FZ_FUNCTION void ll_fz_set_warning_callback(::fz_warning_cb *warning_cb, void *user);

/** Low-level wrapper for `::fz_sha256_final()`. */
/**
	MD5 finalization. Ends an MD5 message-digest operation, writing
	the message digest and zeroizing the context.

	Never throws an exception.
*/
FZ_FUNCTION void ll_fz_sha256_final(::fz_sha256 *state, unsigned char digest[32]);

/** Low-level wrapper for `::fz_sha256_init()`. */
/**
	SHA256 initialization. Begins an SHA256 operation, initialising
	the supplied context.

	Never throws an exception.
*/
FZ_FUNCTION void ll_fz_sha256_init(::fz_sha256 *state);

/** Low-level wrapper for `::fz_sha256_update()`. */
/**
	SHA256 block update operation. Continues an SHA256 message-
	digest operation, processing another message block, and updating
	the context.

	Never throws an exception.
*/
FZ_FUNCTION void ll_fz_sha256_update(::fz_sha256 *state, const unsigned char *input, size_t inlen);

/** Low-level wrapper for `::fz_sha384_final()`. */
/**
	SHA384 finalization. Ends an SHA384 message-digest operation,
	writing the message digest and zeroizing the context.

	Never throws an exception.
*/
FZ_FUNCTION void ll_fz_sha384_final(::fz_sha384 *state, unsigned char digest[64]);

/** Low-level wrapper for `::fz_sha384_init()`. */
/**
	SHA384 initialization. Begins an SHA384 operation, initialising
	the supplied context.

	Never throws an exception.
*/
FZ_FUNCTION void ll_fz_sha384_init(::fz_sha384 *state);

/** Low-level wrapper for `::fz_sha384_update()`. */
/**
	SHA384 block update operation. Continues an SHA384 message-
	digest operation, processing another message block, and updating
	the context.

	Never throws an exception.
*/
FZ_FUNCTION void ll_fz_sha384_update(::fz_sha384 *state, const unsigned char *input, size_t inlen);

/** Low-level wrapper for `::fz_sha512_final()`. */
/**
	SHA512 finalization. Ends an SHA512 message-digest operation,
	writing the message digest and zeroizing the context.

	Never throws an exception.
*/
FZ_FUNCTION void ll_fz_sha512_final(::fz_sha512 *state, unsigned char digest[64]);

/** Low-level wrapper for `::fz_sha512_init()`. */
/**
	SHA512 initialization. Begins an SHA512 operation, initialising
	the supplied context.

	Never throws an exception.
*/
FZ_FUNCTION void ll_fz_sha512_init(::fz_sha512 *state);

/** Low-level wrapper for `::fz_sha512_update()`. */
/**
	SHA512 block update operation. Continues an SHA512 message-
	digest operation, processing another message block, and updating
	the context.

	Never throws an exception.
*/
FZ_FUNCTION void ll_fz_sha512_update(::fz_sha512 *state, const unsigned char *input, size_t inlen);

/** Low-level wrapper for `::fz_shear()`. */
/**
	Create a shearing matrix.

	The returned matrix is of the form [ 1 sy sx 1 0 0 ].

	m: pointer to place to store returned matrix

	sx, sy: Shearing factors. A shearing factor of 0.0 will not
	cause any shearing along the relevant axis.

	Returns m.
*/
FZ_FUNCTION ::fz_matrix ll_fz_shear(float sx, float sy);

/** Low-level wrapper for `::fz_show_glyph()`. */
/**
	Add a glyph/unicode value to a text object.

	text: Text object to add to.

	font: The font the glyph should be added in.

	trm: The transform to use for the glyph.

	glyph: The glyph id to add.

	unicode: The unicode character for the glyph.

	cid: The CJK CID value or raw character code.

	wmode: 1 for vertical mode, 0 for horizontal.

	bidi_level: The bidirectional level for this glyph.

	markup_dir: The direction of the text as specified in the
	markup.

	language: The language in use (if known, 0 otherwise)
	(e.g. FZ_LANG_zh_Hans).

	Throws exception on failure to allocate.
*/
FZ_FUNCTION void ll_fz_show_glyph(::fz_text *text, ::fz_font *font, ::fz_matrix trm, int glyph, int unicode, int wmode, int bidi_level, ::fz_bidi_direction markup_dir, ::fz_text_language language);

/** Low-level wrapper for `::fz_show_glyph_aux()`. */
FZ_FUNCTION void ll_fz_show_glyph_aux(::fz_text *text, ::fz_font *font, ::fz_matrix trm, int glyph, int unicode, int cid, int wmode, int bidi_level, ::fz_bidi_direction markup_dir, ::fz_text_language lang);

/** Low-level wrapper for `::fz_show_string()`. */
/**
	Add a UTF8 string to a text object.

	text: Text object to add to.

	font: The font the string should be added in.

	trm: The transform to use.

	s: The utf-8 string to add.

	wmode: 1 for vertical mode, 0 for horizontal.

	bidi_level: The bidirectional level for this glyph.

	markup_dir: The direction of the text as specified in the markup.

	language: The language in use (if known, 0 otherwise)
		(e.g. FZ_LANG_zh_Hans).

	Returns the transform updated with the advance width of the
	string.
*/
FZ_FUNCTION ::fz_matrix ll_fz_show_string(::fz_text *text, ::fz_font *font, ::fz_matrix trm, const char *s, int wmode, int bidi_level, ::fz_bidi_direction markup_dir, ::fz_text_language language);

/** Low-level wrapper for `::fz_shrink_store()`. */
/**
	Evict items from the store until the total size of
	the objects in the store is reduced to a given percentage of its
	current size.

	percent: %age of current size to reduce the store to.

	Returns non zero if we managed to free enough memory, zero
	otherwise.
*/
FZ_FUNCTION int ll_fz_shrink_store(unsigned int percent);

/** Low-level wrapper for `::fz_skip()`. */
/**
	Read from a stream discarding data.

	stm: The stream to read from.

	len: The number of bytes to read.

	Returns the number of bytes read. May throw exceptions.
*/
FZ_FUNCTION size_t ll_fz_skip(::fz_stream *stm, size_t len);

/** Low-level wrapper for `::fz_skip_space()`. */
/**
	Skip over whitespace (bytes <= 32) in a stream.
*/
FZ_FUNCTION void ll_fz_skip_space(::fz_stream *stm);

/** Low-level wrapper for `::fz_skip_string()`. */
/**
	Skip over a given string in a stream. Return 0 if successfully
	skipped, non-zero otherwise. As many characters will be skipped
	over as matched in the string.
*/
FZ_FUNCTION int ll_fz_skip_string(::fz_stream *stm, const char *str);

/** Low-level wrapper for `::fz_slice_buffer()`. */
/**
	Create a new buffer with a (subset of) the data from the buffer.

	start: if >= 0, offset from start of buffer, if < 0 offset from end of buffer.

	end: if >= 0, offset from start of buffer, if < 0 offset from end of buffer.

*/
FZ_FUNCTION ::fz_buffer *ll_fz_slice_buffer(::fz_buffer *buf, int64_t start, int64_t end);

/** Low-level wrapper for `::fz_snap_selection()`. */
FZ_FUNCTION ::fz_quad ll_fz_snap_selection(::fz_stext_page *page, ::fz_point *ap, ::fz_point *bp, int mode);

/** Low-level wrapper for `::fz_start_throw_on_repair()`. */
FZ_FUNCTION void ll_fz_start_throw_on_repair();

/** Low-level wrapper for `::fz_stat_ctime()`. */
FZ_FUNCTION int64_t ll_fz_stat_ctime(const char *path);

/** Low-level wrapper for `::fz_stat_mtime()`. */
FZ_FUNCTION int64_t ll_fz_stat_mtime(const char *path);

/** Low-level wrapper for `::fz_stddbg()`. */
/**
	Retrieve an fz_output for the default debugging stream. On
	Windows this will be OutputDebugString for non-console apps.
	Otherwise, it is always fz_stderr.

	Optionally may be fz_dropped when finished with.
*/
FZ_FUNCTION ::fz_output *ll_fz_stddbg();

/** Low-level wrapper for `::fz_stderr()`. */
/**
	Retrieve an fz_output that directs to stdout.

	Optionally may be fz_dropped when finished with.
*/
FZ_FUNCTION ::fz_output *ll_fz_stderr();

/** Low-level wrapper for `::fz_stdout()`. */
/**
	Retrieve an fz_output that directs to stdout.

	Optionally may be fz_dropped when finished with.
*/
FZ_FUNCTION ::fz_output *ll_fz_stdout();

/** Low-level wrapper for `::fz_store_item()`. */
/**
	Add an item to the store.

	Add an item into the store, returning NULL for success. If an
	item with the same key is found in the store, then our item will
	not be inserted, and the function will return a pointer to that
	value instead. This function takes its own reference to val, as
	required (i.e. the caller maintains ownership of its own
	reference).

	key: The key used to index the item.

	val: The value to store.

	itemsize: The size in bytes of the value (as counted towards the
	store size).

	type: Functions used to manipulate the key.
*/
FZ_FUNCTION void *ll_fz_store_item(void *key, void *val, size_t itemsize, const ::fz_store_type *type);

/** Low-level wrapper for `::fz_store_scavenge()`.

This function has out-params. Python/C# wrappers look like:
	`ll_fz_store_scavenge(size_t size)` => `(int, int phase)`
*/
/**
	Internal function used as part of the scavenging
	allocator; when we fail to allocate memory, before returning a
	failure to the caller, we try to scavenge space within the store
	by evicting at least 'size' bytes. The allocator then retries.

	size: The number of bytes we are trying to have free.

	phase: What phase of the scavenge we are in. Updated on exit.

	Returns non zero if we managed to free any memory.
*/
FZ_FUNCTION int ll_fz_store_scavenge(size_t size, int *phase);

/** Low-level wrapper for `::fz_store_scavenge_external()`.

This function has out-params. Python/C# wrappers look like:
	`ll_fz_store_scavenge_external(size_t size)` => `(int, int phase)`
*/
/**
	External function for callers to use
	to scavenge while trying allocations.

	size: The number of bytes we are trying to have free.

	phase: What phase of the scavenge we are in. Updated on exit.

	Returns non zero if we managed to free any memory.
*/
FZ_FUNCTION int ll_fz_store_scavenge_external(size_t size, int *phase);

/** Low-level wrapper for `::fz_story_document()`. */
FZ_FUNCTION ::fz_xml *ll_fz_story_document(::fz_story *story);

/** Low-level wrapper for `::fz_story_positions()`. */
FZ_FUNCTION void ll_fz_story_positions(::fz_story *story, ::fz_story_position_callback *cb, void *arg);

/** Low-level wrapper for `::fz_story_warnings()`. */
FZ_FUNCTION const char *ll_fz_story_warnings(::fz_story *story);

/** Low-level wrapper for `::fz_strcasecmp()`. */
/**
	Case insensitive (ASCII only) string comparison.
*/
FZ_FUNCTION int ll_fz_strcasecmp(const char *a, const char *b);

/** Low-level wrapper for `::fz_strdup()`. */
/**
	Portable strdup implementation, using fz allocators.
*/
FZ_FUNCTION char *ll_fz_strdup(const char *s);

/** Low-level wrapper for `::fz_stream_filename()`. */
/**
	Return the filename (UTF-8 encoded) from which a stream was opened.

	Returns NULL if the filename is not available (or the stream was
	opened from a source other than a file).
*/
FZ_FUNCTION const char *ll_fz_stream_filename(::fz_stream *stm);

/** Low-level wrapper for `::fz_stream_from_output()`. */
/**
	Obtain the fz_output in the form of a fz_stream.

	This allows data to be read back from some forms of fz_output
	object. When finished reading, the fz_stream should be released
	by calling fz_drop_stream. Until the fz_stream is dropped, no
	further operations should be performed on the fz_output object.
*/
FZ_FUNCTION ::fz_stream *ll_fz_stream_from_output(::fz_output *arg_1);

/** Low-level wrapper for `::fz_string_from_box_type()`. */
FZ_FUNCTION const char *ll_fz_string_from_box_type(::fz_box_type box);

/** Low-level wrapper for `::fz_string_from_buffer()`. */
/**
	Ensure that a buffer's data ends in a
	0 byte, and return a pointer to it.
*/
FZ_FUNCTION const char *ll_fz_string_from_buffer(::fz_buffer *buf);

/** Low-level wrapper for `::fz_string_from_text_language()`. */
/**
	Recover ISO 639 (639-{1,2,3,5}) language specification
	strings losslessly from a 15 bit fz_text_language code.

	No validation is carried out. See note above.
*/
FZ_FUNCTION char *ll_fz_string_from_text_language(char str[8], ::fz_text_language lang);

/** Low-level wrapper for `::fz_string_from_text_language2()`. */
/**
C++ alternative to fz_string_from_text_language() that returns information in a std::string.
*/
FZ_FUNCTION std::string ll_fz_string_from_text_language2(::fz_text_language lang);

/** Low-level wrapper for `::fz_strlcat()`. */
/**
	Concatenate 2 strings, with a maximum length.

	dst: pointer to first string in a buffer of n bytes.

	src: pointer to string to concatenate.

	n: Size (in bytes) of buffer that dst is in.

	Returns the real length that a concatenated dst + src would have
	been (not including terminator).
*/
FZ_FUNCTION size_t ll_fz_strlcat(char *dst, const char *src, size_t n);

/** Low-level wrapper for `::fz_strlcpy()`. */
/**
	Copy at most n-1 chars of a string into a destination
	buffer with null termination, returning the real length of the
	initial string (excluding terminator).

	dst: Destination buffer, at least n bytes long.

	src: C string (non-NULL).

	n: Size of dst buffer in bytes.

	Returns the length (excluding terminator) of src.
*/
FZ_FUNCTION size_t ll_fz_strlcpy(char *dst, const char *src, size_t n);

/** Low-level wrapper for `::fz_strncasecmp()`. */
FZ_FUNCTION int ll_fz_strncasecmp(const char *a, const char *b, size_t n);

/** Low-level wrapper for `::fz_strnlen()`. */
/**
	Return strlen(s), if that is less than maxlen, or maxlen if
	there is no null byte ('\0') among the first maxlen bytes.
*/
FZ_FUNCTION size_t ll_fz_strnlen(const char *s, size_t maxlen);

/** Low-level wrapper for `::fz_stroke_path()`. */
FZ_FUNCTION void ll_fz_stroke_path(::fz_device *dev, const ::fz_path *path, const ::fz_stroke_state *stroke, ::fz_matrix ctm, ::fz_colorspace *colorspace, const float *color, float alpha, ::fz_color_params color_params);

/** Low-level wrapper for `::fz_stroke_text()`. */
FZ_FUNCTION void ll_fz_stroke_text(::fz_device *dev, const ::fz_text *text, const ::fz_stroke_state *stroke, ::fz_matrix ctm, ::fz_colorspace *colorspace, const float *color, float alpha, ::fz_color_params color_params);

/** Low-level wrapper for `::fz_strsep()`.

This function has out-params. Python/C# wrappers look like:
	`ll_fz_strsep(const char *delim)` => `(char *, char *stringp)`
*/
/**
	Given a pointer to a C string (or a pointer to NULL) break
	it at the first occurrence of a delimiter char (from a given
	set).

	stringp: Pointer to a C string pointer (or NULL). Updated on
	exit to point to the first char of the string after the
	delimiter that was found. The string pointed to by stringp will
	be corrupted by this call (as the found delimiter will be
	overwritten by 0).

	delim: A C string of acceptable delimiter characters.

	Returns a pointer to a C string containing the chars of stringp
	up to the first delimiter char (or the end of the string), or
	NULL.
*/
FZ_FUNCTION char *ll_fz_strsep(char **stringp, const char *delim);

/** Low-level wrapper for `::fz_strtof()`.

This function has out-params. Python/C# wrappers look like:
	`ll_fz_strtof(const char *s)` => `(float, char *es)`
*/
/**
	Locale-independent decimal to binary conversion. On overflow
	return (-)INFINITY and set errno to ERANGE. On underflow return
	0 and set errno to ERANGE. Special inputs (case insensitive):
	"NAN", "INF" or "INFINITY".
*/
FZ_FUNCTION float ll_fz_strtof(const char *s, char **es);

/** Low-level wrapper for `::fz_structure_from_string()`. */
FZ_FUNCTION ::fz_structure ll_fz_structure_from_string(const char *str);

/** Low-level wrapper for `::fz_structure_to_string()`. */
FZ_FUNCTION const char *ll_fz_structure_to_string(::fz_structure type);

/** Low-level wrapper for `::fz_subpixel_adjust()`. */
/**
	Perform subpixel quantisation and adjustment on a glyph matrix.

	ctm: On entry, the desired 'ideal' transformation for a glyph.
	On exit, adjusted to a (very similar) transformation quantised
	for subpixel caching.

	subpix_ctm: Initialised by the routine to the transform that
	should be used to render the glyph.

	qe, qf: which subpixel position we quantised to.

	Returns: the size of the glyph.

	Note: This is currently only exposed for use in our app. It
	should be considered "at risk" of removal from the API.
*/
FZ_FUNCTION float ll_fz_subpixel_adjust(::fz_matrix *ctm, ::fz_matrix *subpix_ctm, unsigned char *qe, unsigned char *qf);

/** Low-level wrapper for `::fz_subsample_pixmap()`. */
FZ_FUNCTION void ll_fz_subsample_pixmap(::fz_pixmap *tile, int factor);

/** Low-level wrapper for `::fz_subset_cff_for_gids()`.

This function has out-params. Python/C# wrappers look like:
	`ll_fz_subset_cff_for_gids(::fz_buffer *orig, int num_gids, int symbolic, int cidfont)` => `(fz_buffer *, int gids)`
*/
FZ_FUNCTION ::fz_buffer *ll_fz_subset_cff_for_gids(::fz_buffer *orig, int *gids, int num_gids, int symbolic, int cidfont);

/** Low-level wrapper for `::fz_subset_ttf_for_gids()`.

This function has out-params. Python/C# wrappers look like:
	`ll_fz_subset_ttf_for_gids(::fz_buffer *orig, int num_gids, int symbolic, int cidfont)` => `(fz_buffer *, int gids)`
*/
FZ_FUNCTION ::fz_buffer *ll_fz_subset_ttf_for_gids(::fz_buffer *orig, int *gids, int num_gids, int symbolic, int cidfont);

/** Low-level wrapper for `::fz_sync_bits()`. */
/**
	Called after reading bits to tell the stream
	that we are about to return to reading bytewise. Resyncs
	the stream to whole byte boundaries.
*/
FZ_FUNCTION void ll_fz_sync_bits(::fz_stream *stm);

/** Low-level wrapper for `::fz_tell()`. */
/**
	return the current reading position within a stream
*/
FZ_FUNCTION int64_t ll_fz_tell(::fz_stream *stm);

/** Low-level wrapper for `::fz_tell_output()`. */
/**
	Return the current file position.

	Throw an error on untellable outputs.
*/
FZ_FUNCTION int64_t ll_fz_tell_output(::fz_output *out);

/** Low-level wrapper for `::fz_terminate_buffer()`. */
/**
	Zero-terminate buffer in order to use as a C string.

	This byte is invisible and does not affect the length of the
	buffer as returned by fz_buffer_storage. The zero byte is
	written *after* the data, and subsequent writes will overwrite
	the terminating byte.

	Subsequent changes to the size of the buffer (such as by
	fz_buffer_trim, fz_buffer_grow, fz_resize_buffer, etc) may
	invalidate this.
*/
FZ_FUNCTION void ll_fz_terminate_buffer(::fz_buffer *buf);

/** Low-level wrapper for `::fz_text_aa_level()`. */
/**
	Get the number of bits of antialiasing we are
	using for text. Between 0 and 8.
*/
FZ_FUNCTION int ll_fz_text_aa_level();

/** Low-level wrapper for `::fz_text_language_from_string()`. */
/**
	Convert ISO 639 (639-{1,2,3,5}) language specification
	strings losslessly to a 15 bit fz_text_language code.

	No validation is carried out. Obviously invalid (out
	of spec) codes will be mapped to FZ_LANG_UNSET, but
	well-formed (but undefined) codes will be blithely
	accepted.
*/
FZ_FUNCTION ::fz_text_language ll_fz_text_language_from_string(const char *str);

/** Low-level wrapper for `::fz_tint_pixmap()`. */
/**
	Tint all the pixels in an RGB, BGR, or Gray pixmap.

	black: Map black to this hexadecimal RGB color.

	white: Map white to this hexadecimal RGB color.
*/
FZ_FUNCTION void ll_fz_tint_pixmap(::fz_pixmap *pix, int black, int white);

/** Low-level wrapper for `::fz_tolower()`. */
/**
	Unicode aware tolower and toupper functions.
*/
FZ_FUNCTION int ll_fz_tolower(int c);

/** Low-level wrapper for `::fz_toupper()`. */
FZ_FUNCTION int ll_fz_toupper(int c);

/** Low-level wrapper for `::fz_transform_page()`. */
/**
	Create transform matrix to draw page
	at a given resolution and rotation. Adjusts the scaling
	factors so that the page covers whole number of
	pixels and adjust the page origin to be at 0,0.
*/
FZ_FUNCTION ::fz_matrix ll_fz_transform_page(::fz_rect mediabox, float resolution, float rotate);

/** Low-level wrapper for `::fz_transform_path()`. */
/**
	Transform a path by a given
	matrix.

	path: The path to modify (must not be a packed path).

	transform: The transform to apply.

	Throws exceptions if the path is packed, or on failure
	to allocate.
*/
FZ_FUNCTION void ll_fz_transform_path(::fz_path *path, ::fz_matrix transform);

/** Low-level wrapper for `::fz_transform_point()`. */
/**
	Apply a transformation to a point.

	transform: Transformation matrix to apply. See fz_concat,
	fz_scale, fz_rotate and fz_translate for how to create a
	matrix.

	point: Pointer to point to update.

	Returns transform (unchanged).
*/
FZ_FUNCTION ::fz_point ll_fz_transform_point(::fz_point point, ::fz_matrix m);

/** Low-level wrapper for `::fz_transform_point_xy()`. */
FZ_FUNCTION ::fz_point ll_fz_transform_point_xy(float x, float y, ::fz_matrix m);

/** Low-level wrapper for `::fz_transform_quad()`. */
/**
	Transform a quad by a matrix.
*/
FZ_FUNCTION ::fz_quad ll_fz_transform_quad(::fz_quad q, ::fz_matrix m);

/** Low-level wrapper for `::fz_transform_rect()`. */
/**
	Apply a transform to a rectangle.

	After the four corner points of the axis-aligned rectangle
	have been transformed it may not longer be axis-aligned. So a
	new axis-aligned rectangle is created covering at least the
	area of the transformed rectangle.

	transform: Transformation matrix to apply. See fz_concat,
	fz_scale and fz_rotate for how to create a matrix.

	rect: Rectangle to be transformed. The two special cases
	fz_empty_rect and fz_infinite_rect, may be used but are
	returned unchanged as expected.
*/
FZ_FUNCTION ::fz_rect ll_fz_transform_rect(::fz_rect rect, ::fz_matrix m);

/** Low-level wrapper for `::fz_transform_vector()`. */
/**
	Apply a transformation to a vector.

	transform: Transformation matrix to apply. See fz_concat,
	fz_scale and fz_rotate for how to create a matrix. Any
	translation will be ignored.

	vector: Pointer to vector to update.
*/
FZ_FUNCTION ::fz_point ll_fz_transform_vector(::fz_point vector, ::fz_matrix m);

/** Low-level wrapper for `::fz_translate()`. */
/**
	Create a translation matrix.

	The returned matrix is of the form [ 1 0 0 1 tx ty ].

	m: A place to store the created matrix.

	tx, ty: Translation distances along the X- and Y-axes. A
	translation of 0 will not cause any translation along the
	relevant axis.

	Returns m.
*/
FZ_FUNCTION ::fz_matrix ll_fz_translate(float tx, float ty);

/** Low-level wrapper for `::fz_translate_irect()`. */
FZ_FUNCTION ::fz_irect ll_fz_translate_irect(::fz_irect a, int xoff, int yoff);

/** Low-level wrapper for `::fz_translate_rect()`. */
/**
	Translate bounding box.

	Translate a bbox by a given x and y offset. Allows for overflow.
*/
FZ_FUNCTION ::fz_rect ll_fz_translate_rect(::fz_rect a, float xoff, float yoff);

/** Low-level wrapper for `::fz_tree_archive_add_buffer()`. */
/**
	Add a named buffer to an existing tree archive.

	The tree will take a new reference to the buffer. Ownership
	is not transferred.
*/
FZ_FUNCTION void ll_fz_tree_archive_add_buffer(::fz_archive *arch_, const char *name, ::fz_buffer *buf);

/** Low-level wrapper for `::fz_tree_archive_add_data()`. */
/**
	Add a named block of data to an existing tree archive.

	The data will be copied into a buffer, and so the caller
	may free it as soon as this returns.
*/
FZ_FUNCTION void ll_fz_tree_archive_add_data(::fz_archive *arch_, const char *name, const void *data, size_t size);

/** Low-level wrapper for `::fz_tree_insert()`. */
/**
	Insert a new key/value pair and rebalance the tree.
	Return the new root of the tree after inserting and rebalancing.
	May be called with a NULL root to create a new tree.

	No data is copied into the tree structure; key and value are
	merely kept as pointers.
*/
FZ_FUNCTION ::fz_tree *ll_fz_tree_insert(::fz_tree *root, const char *key, void *value);

/** Low-level wrapper for `::fz_tree_lookup()`. */
/**
	Look for the value of a node in the tree with the given key.

	Simple pointer equivalence is used for key.

	Returns NULL for no match.
*/
FZ_FUNCTION void *ll_fz_tree_lookup(::fz_tree *node, const char *key);

/** Low-level wrapper for `::fz_trim_buffer()`. */
/**
	Trim wasted capacity from a buffer by resizing internal memory.
*/
FZ_FUNCTION void ll_fz_trim_buffer(::fz_buffer *buf);

/** Low-level wrapper for `::fz_trim_path()`. */
/**
	Minimise the internal storage used by a path.

	As paths are constructed, the internal buffers
	grow. To avoid repeated reallocations they
	grow with some spare space. Once a path has
	been fully constructed, this call allows the
	excess space to be trimmed.
*/
FZ_FUNCTION void ll_fz_trim_path(::fz_path *path);

/** Low-level wrapper for `::fz_truncate_output()`. */
/**
	Truncate the output at the current position.

	This allows output streams which have seeked back from the end
	of their storage to be truncated at the current point.
*/
FZ_FUNCTION void ll_fz_truncate_output(::fz_output *arg_1);

/** Low-level wrapper for `::fz_try_invert_matrix()`. */
/**
	Attempt to create an inverse matrix.

	inverse: Place to store inverse matrix.

	matrix: Matrix to invert. A degenerate matrix, where the
	determinant is equal to zero, can not be inverted.

	Returns 1 if matrix is degenerate (singular), or 0 otherwise.
*/
FZ_FUNCTION int ll_fz_try_invert_matrix(::fz_matrix *inv, ::fz_matrix src);

/** Low-level wrapper for `::fz_try_open_archive_entry()`. */
/**
	Opens an archive entry as a stream.

	Returns NULL if a matching entry cannot be found, otherwise
	behaves exactly as fz_open_archive_entry.
*/
FZ_FUNCTION ::fz_stream *ll_fz_try_open_archive_entry(::fz_archive *arch, const char *name);

/** Low-level wrapper for `::fz_try_open_archive_with_stream()`. */
/**
	Open zip or tar archive stream.

	Does the same as fz_open_archive_with_stream, but will not throw
	an error in the event of failing to recognise the format. Will
	still throw errors in other cases though!
*/
FZ_FUNCTION ::fz_archive *ll_fz_try_open_archive_with_stream(::fz_stream *file);

/** Low-level wrapper for `::fz_try_open_file()`. */
/**
	Open the named file and wrap it in a stream.

	Does the same as fz_open_file, but in the event the file
	does not open, it will return NULL rather than throw an
	exception.
*/
FZ_FUNCTION ::fz_stream *ll_fz_try_open_file(const char *name);

/** Low-level wrapper for `::fz_try_parse_xml_archive_entry()`. */
/**
	Try and parse the contents of an archive entry into a tree of xml nodes.

	preserve_white: whether to keep or delete all-whitespace nodes.

	Will return NULL if the archive entry can't be found. Otherwise behaves
	the same as fz_parse_xml_archive_entry. May throw exceptions.
*/
FZ_FUNCTION ::fz_xml *ll_fz_try_parse_xml_archive_entry(::fz_archive *dir, const char *filename, int preserve_white);

/** Low-level wrapper for `::fz_try_read_archive_entry()`. */
/**
	Reads all bytes in an archive entry
	into a buffer.

	name: Entry name to look for, this must be an exact match to
	the entry name in the archive.

	Returns NULL if a matching entry cannot be found. Otherwise behaves
	the same as fz_read_archive_entry. Exceptions may be thrown.
*/
FZ_FUNCTION ::fz_buffer *ll_fz_try_read_archive_entry(::fz_archive *arch, const char *name);

/** Low-level wrapper for `::fz_try_read_file()`. */
/**
	Read all the contents of a file into a buffer.

	Returns NULL if the file does not exist, otherwise
	behaves exactly as fz_read_file.
*/
FZ_FUNCTION ::fz_buffer *ll_fz_try_read_file(const char *filename);

/** Low-level wrapper for `::fz_tune_image_decode()`. */
/**
	Set the tuning function to use for
	image decode.

	image_decode: Function to use.

	arg: Opaque argument to be passed to tuning function.
*/
FZ_FUNCTION void ll_fz_tune_image_decode(::fz_tune_image_decode_fn *image_decode, void *arg);

/** Low-level wrapper for `::fz_tune_image_scale()`. */
/**
	Set the tuning function to use for
	image scaling.

	image_scale: Function to use.

	arg: Opaque argument to be passed to tuning function.
*/
FZ_FUNCTION void ll_fz_tune_image_scale(::fz_tune_image_scale_fn *image_scale, void *arg);

/** Low-level wrapper for `::fz_unicode_from_glyph_name()`. */
FZ_FUNCTION int ll_fz_unicode_from_glyph_name(const char *name);

/** Low-level wrapper for `::fz_unicode_from_glyph_name_strict()`. */
FZ_FUNCTION int ll_fz_unicode_from_glyph_name_strict(const char *name);

/** Low-level wrapper for `::fz_union_rect()`. */
/**
	Compute union of two rectangles.

	Given two rectangles, update the first to be the smallest
	axis-aligned rectangle that encompasses both given rectangles.
	If either rectangle is infinite then the union is also infinite.
	If either rectangle is empty then the union is simply the
	non-empty rectangle. Should both rectangles be empty, then the
	union is also empty.
*/
FZ_FUNCTION ::fz_rect ll_fz_union_rect(::fz_rect a, ::fz_rect b);

/** Low-level wrapper for `::fz_unlock()`. */
/**
	Unlock one of the user supplied mutexes.
*/
FZ_FUNCTION void ll_fz_unlock(int lock);

/** Low-level wrapper for `::fz_unpack_stream()`. */
FZ_FUNCTION ::fz_stream *ll_fz_unpack_stream(::fz_stream *src, int depth, int w, int h, int n, int indexed, int pad, int skip);

/** Low-level wrapper for `::fz_unread_byte()`. */
/**
	Unread the single last byte successfully
	read from a stream. Do not call this without having
	successfully read a byte.

	stm: The stream to operate upon.
*/
FZ_FUNCTION void ll_fz_unread_byte(::fz_stream *stm);

/** Low-level wrapper for `::fz_unshare_stroke_state()`. */
/**
	Given a reference to a (possibly) shared stroke_state structure,
	return a reference to an equivalent stroke_state structure
	that is guaranteed to be unshared (i.e. one that can
	safely be modified).

	shared: The reference to a (possibly) shared structure
	to unshare. Ownership of this reference is passed in
	to this function, even in the case of exceptions being
	thrown.

	Exceptions may be thrown in the event of failure to
	allocate if required.
*/
FZ_FUNCTION ::fz_stroke_state *ll_fz_unshare_stroke_state(::fz_stroke_state *shared);

/** Low-level wrapper for `::fz_unshare_stroke_state_with_dash_len()`. */
/**
	Given a reference to a (possibly) shared stroke_state structure,
	return a reference to a stroke_state structure (with room for a
	given amount of dash data) that is guaranteed to be unshared
	(i.e. one that can safely be modified).

	shared: The reference to a (possibly) shared structure
	to unshare. Ownership of this reference is passed in
	to this function, even in the case of exceptions being
	thrown.

	Exceptions may be thrown in the event of failure to
	allocate if required.
*/
FZ_FUNCTION ::fz_stroke_state *ll_fz_unshare_stroke_state_with_dash_len(::fz_stroke_state *shared, int len);

/** Low-level wrapper for `::fz_urldecode()`. */
/**
	Like fz_decode_uri_component but in-place.
*/
FZ_FUNCTION char *ll_fz_urldecode(char *url);

/** Low-level wrapper for `::fz_use_document_css()`. */
/**
	Return whether to respect document styles in HTML and EPUB.
*/
FZ_FUNCTION int ll_fz_use_document_css();

/** Low-level wrapper for `::fz_user_context()`. */
/**
	Read the user field from the context.
*/
FZ_FUNCTION void *ll_fz_user_context();

/** Low-level wrapper for `::fz_user_css()`. */
/**
	Get the user stylesheet source text.
*/
FZ_FUNCTION const char *ll_fz_user_css();

/** Low-level wrapper for `::fz_utflen()`. */
/**
	Count how many runes the UTF-8 encoded string
	consists of.

	s: The UTF-8 encoded, NUL-terminated text string.

	Returns the number of runes in the string.
*/
FZ_FUNCTION int ll_fz_utflen(const char *s);

/** Low-level wrapper for `::fz_var_imp()`. */
FZ_FUNCTION void ll_fz_var_imp(void *arg_0);

/** Low-level wrapper for `::fz_vlog_error_printf()`. */
FZ_FUNCTION void ll_fz_vlog_error_printf(const char *fmt, va_list ap);

/** Low-level wrapper for `::fz_vsnprintf()`. */
/**
	A vsnprintf work-alike, using our custom formatter.
*/
FZ_FUNCTION size_t ll_fz_vsnprintf(char *buffer, size_t space, const char *fmt, va_list args);

/** Low-level wrapper for `::fz_vthrow()`. */
/**
	Throw an exception.

	This assumes an enclosing fz_try() block within the callstack.
*/
FZ_FUNCTION void ll_fz_vthrow(int errcode, const char *arg_2, va_list ap);

/** Low-level wrapper for `::fz_vwarn()`. */
/**
	Log a warning.

	This goes to the registered warning stream (stderr by
	default).
*/
FZ_FUNCTION void ll_fz_vwarn(const char *fmt, va_list ap);

/** Low-level wrapper for `::fz_walk_path()`. */
/**
	Walk the segments of a path, calling the
	appropriate callback function from a given set for each
	segment of the path.

	path: The path to walk.

	walker: The set of callback functions to use. The first
	4 callback pointers in the set must be non-NULL. The
	subsequent ones can either be supplied, or can be left
	as NULL, in which case the top 4 functions will be
	called as appropriate to simulate them.

	arg: An opaque argument passed in to each callback.

	Exceptions will only be thrown if the underlying callback
	functions throw them.
*/
FZ_FUNCTION void ll_fz_walk_path(const ::fz_path *path, const ::fz_path_walker *walker, void *arg);

/** Low-level wrapper for `::fz_warn()`. */
FZ_FUNCTION void ll_fz_warn(const char *fmt, ...);

/** Low-level wrapper for `::fz_warning_callback()`.

This function has out-params. Python/C# wrappers look like:
	`ll_fz_warning_callback()` => `(fz_warning_cb *, void *user)`
*/
/**
	Retrieve the currently set warning callback, or NULL if none
	has been set. Optionally, if user is non-NULL, the user pointer
	given when the warning callback was set is also passed back to
	the caller.
*/
FZ_FUNCTION ::fz_warning_cb *ll_fz_warning_callback(void **user);

/** Low-level wrapper for `::fz_warp_pixmap()`. */
FZ_FUNCTION ::fz_pixmap *ll_fz_warp_pixmap(::fz_pixmap *src, const ::fz_point points[4], int width, int height);

/** Low-level wrapper for `::fz_windows_1250_from_unicode()`. */
FZ_FUNCTION int ll_fz_windows_1250_from_unicode(int u);

/** Low-level wrapper for `::fz_windows_1251_from_unicode()`. */
FZ_FUNCTION int ll_fz_windows_1251_from_unicode(int u);

/** Low-level wrapper for `::fz_windows_1252_from_unicode()`. */
FZ_FUNCTION int ll_fz_windows_1252_from_unicode(int u);

/** Low-level wrapper for `::fz_write_band()`. */
/**
	Cause a band writer to write the next band
	of data for an image.

	stride: The byte offset from the first byte of the data
	for a pixel to the first byte of the data for the same pixel
	on the row below.

	band_height: The number of lines in this band.

	samples: Pointer to first byte of the data.
*/
FZ_FUNCTION void ll_fz_write_band(::fz_band_writer *writer, int stride, int band_height, const unsigned char *samples);

/** Low-level wrapper for `::fz_write_base64()`. */
/**
	Write a base64 encoded data block, optionally with periodic
	newlines.
*/
FZ_FUNCTION void ll_fz_write_base64(::fz_output *out, const unsigned char *data, size_t size, int newline);

/** Low-level wrapper for `::fz_write_base64_buffer()`. */
/**
	Write a base64 encoded fz_buffer, optionally with periodic
	newlines.
*/
FZ_FUNCTION void ll_fz_write_base64_buffer(::fz_output *out, ::fz_buffer *data, int newline);

/** Low-level wrapper for `::fz_write_bitmap_as_pbm()`. */
/**
	Write a bitmap as a pbm.
*/
FZ_FUNCTION void ll_fz_write_bitmap_as_pbm(::fz_output *out, ::fz_bitmap *bitmap);

/** Low-level wrapper for `::fz_write_bitmap_as_pcl()`. */
/**
	Write a bitmap as mono PCL.
*/
FZ_FUNCTION void ll_fz_write_bitmap_as_pcl(::fz_output *out, const ::fz_bitmap *bitmap, const ::fz_pcl_options *pcl);

/** Low-level wrapper for `::fz_write_bitmap_as_pkm()`. */
/**
	Write a CMYK bitmap as a pkm.
*/
FZ_FUNCTION void ll_fz_write_bitmap_as_pkm(::fz_output *out, ::fz_bitmap *bitmap);

/** Low-level wrapper for `::fz_write_bitmap_as_pwg()`. */
/**
	Write a bitmap as a PWG.
*/
FZ_FUNCTION void ll_fz_write_bitmap_as_pwg(::fz_output *out, const ::fz_bitmap *bitmap, const ::fz_pwg_options *pwg);

/** Low-level wrapper for `::fz_write_bitmap_as_pwg_page()`. */
/**
	Write a bitmap as a PWG page.

	Caller should provide a file header by calling
	fz_write_pwg_file_header, but can then write several pages to
	the same file.
*/
FZ_FUNCTION void ll_fz_write_bitmap_as_pwg_page(::fz_output *out, const ::fz_bitmap *bitmap, const ::fz_pwg_options *pwg);

/** Low-level wrapper for `::fz_write_bits()`. */
/**
	Write num_bits of data to the end of the output stream, assumed to be packed
	most significant bits first.
*/
FZ_FUNCTION void ll_fz_write_bits(::fz_output *out, unsigned int data, int num_bits);

/** Low-level wrapper for `::fz_write_bits_sync()`. */
/**
	Sync to byte boundary after writing bits.
*/
FZ_FUNCTION void ll_fz_write_bits_sync(::fz_output *out);

/** Low-level wrapper for `::fz_write_buffer()`. */
FZ_FUNCTION void ll_fz_write_buffer(::fz_output *out, ::fz_buffer *data);

/** Low-level wrapper for `::fz_write_byte()`. */
FZ_FUNCTION void ll_fz_write_byte(::fz_output *out, unsigned char x);

/** Low-level wrapper for `::fz_write_char()`. */
FZ_FUNCTION void ll_fz_write_char(::fz_output *out, char x);

/** Low-level wrapper for `::fz_write_data()`. */
/**
	Write data to output.

	data: Pointer to data to write.
	size: Size of data to write in bytes.
*/
FZ_FUNCTION void ll_fz_write_data(::fz_output *out, const void *data, size_t size);

/** Low-level wrapper for `::fz_write_document()`. */
/**
	Convenience function to feed all the pages of a document to
	fz_begin_page/fz_run_page/fz_end_page.
*/
FZ_FUNCTION void ll_fz_write_document(::fz_document_writer *wri, ::fz_document *doc);

/** Low-level wrapper for `::fz_write_float_be()`. */
FZ_FUNCTION void ll_fz_write_float_be(::fz_output *out, float f);

/** Low-level wrapper for `::fz_write_float_le()`. */
FZ_FUNCTION void ll_fz_write_float_le(::fz_output *out, float f);

/** Low-level wrapper for `::fz_write_header()`. */
/**
	Cause a band writer to write the header for
	a banded image with the given properties/dimensions etc. This
	also configures the bandwriter for the format of the data to be
	passed in future calls.

	w, h: Width and Height of the entire page.

	n: Number of components (including spots and alphas).

	alpha: Number of alpha components.

	xres, yres: X and Y resolutions in dpi.

	cs: Colorspace (NULL for bitmaps)

	seps: Separation details (or NULL).
*/
FZ_FUNCTION void ll_fz_write_header(::fz_band_writer *writer, int w, int h, int n, int alpha, int xres, int yres, int pagenum, ::fz_colorspace *cs, ::fz_separations *seps);

/** Low-level wrapper for `::fz_write_image_as_data_uri()`. */
/**
	Write image as a data URI (for HTML and SVG output).
*/
FZ_FUNCTION void ll_fz_write_image_as_data_uri(::fz_output *out, ::fz_image *image);

/** Low-level wrapper for `::fz_write_int16_be()`. */
FZ_FUNCTION void ll_fz_write_int16_be(::fz_output *out, int x);

/** Low-level wrapper for `::fz_write_int16_le()`. */
FZ_FUNCTION void ll_fz_write_int16_le(::fz_output *out, int x);

/** Low-level wrapper for `::fz_write_int32_be()`. */
/**
	Write different sized data to an output stream.
*/
FZ_FUNCTION void ll_fz_write_int32_be(::fz_output *out, int x);

/** Low-level wrapper for `::fz_write_int32_le()`. */
FZ_FUNCTION void ll_fz_write_int32_le(::fz_output *out, int x);

/** Low-level wrapper for `::fz_write_pixmap_as_data_uri()`. */
FZ_FUNCTION void ll_fz_write_pixmap_as_data_uri(::fz_output *out, ::fz_pixmap *pixmap);

/** Low-level wrapper for `::fz_write_pixmap_as_jpeg()`. */
/**
	Write a pixmap as a JPEG.
*/
FZ_FUNCTION void ll_fz_write_pixmap_as_jpeg(::fz_output *out, ::fz_pixmap *pix, int quality, int invert_cmyk);

/** Low-level wrapper for `::fz_write_pixmap_as_jpx()`. */
/**
	Pixmap data as JP2K with no subsampling.

	quality = 100 = lossless
	otherwise for a factor of x compression use 100-x. (so 80 is 1:20 compression)
*/
FZ_FUNCTION void ll_fz_write_pixmap_as_jpx(::fz_output *out, ::fz_pixmap *pix, int quality);

/** Low-level wrapper for `::fz_write_pixmap_as_pam()`. */
/**
	Write a pixmap as a pnm (greyscale, rgb or cmyk, with or without
	alpha).
*/
FZ_FUNCTION void ll_fz_write_pixmap_as_pam(::fz_output *out, ::fz_pixmap *pixmap);

/** Low-level wrapper for `::fz_write_pixmap_as_pcl()`. */
/**
	Write an (RGB) pixmap as color PCL.
*/
FZ_FUNCTION void ll_fz_write_pixmap_as_pcl(::fz_output *out, const ::fz_pixmap *pixmap, const ::fz_pcl_options *pcl);

/** Low-level wrapper for `::fz_write_pixmap_as_pclm()`. */
/**
	Write a (Greyscale or RGB) pixmap as pclm.
*/
FZ_FUNCTION void ll_fz_write_pixmap_as_pclm(::fz_output *out, const ::fz_pixmap *pixmap, const ::fz_pclm_options *options);

/** Low-level wrapper for `::fz_write_pixmap_as_pdfocr()`. */
/**
	Write a (Greyscale or RGB) pixmap as pdfocr.
*/
FZ_FUNCTION void ll_fz_write_pixmap_as_pdfocr(::fz_output *out, const ::fz_pixmap *pixmap, const ::fz_pdfocr_options *options);

/** Low-level wrapper for `::fz_write_pixmap_as_png()`. */
/**
	Write a (Greyscale or RGB) pixmap as a png.
*/
FZ_FUNCTION void ll_fz_write_pixmap_as_png(::fz_output *out, const ::fz_pixmap *pixmap);

/** Low-level wrapper for `::fz_write_pixmap_as_pnm()`. */
/**
	Write a pixmap as a pnm (greyscale or rgb, no alpha).
*/
FZ_FUNCTION void ll_fz_write_pixmap_as_pnm(::fz_output *out, ::fz_pixmap *pixmap);

/** Low-level wrapper for `::fz_write_pixmap_as_ps()`. */
/**
	Write a (gray, rgb, or cmyk, no alpha) pixmap out as postscript.
*/
FZ_FUNCTION void ll_fz_write_pixmap_as_ps(::fz_output *out, const ::fz_pixmap *pixmap);

/** Low-level wrapper for `::fz_write_pixmap_as_psd()`. */
/**
	Write a pixmap as a PSD file.
*/
FZ_FUNCTION void ll_fz_write_pixmap_as_psd(::fz_output *out, const ::fz_pixmap *pixmap);

/** Low-level wrapper for `::fz_write_pixmap_as_pwg()`. */
/**
	Write a pixmap as a PWG.
*/
FZ_FUNCTION void ll_fz_write_pixmap_as_pwg(::fz_output *out, const ::fz_pixmap *pixmap, const ::fz_pwg_options *pwg);

/** Low-level wrapper for `::fz_write_pixmap_as_pwg_page()`. */
/**
	Write a pixmap as a PWG page.

	Caller should provide a file header by calling
	fz_write_pwg_file_header, but can then write several pages to
	the same file.
*/
FZ_FUNCTION void ll_fz_write_pixmap_as_pwg_page(::fz_output *out, const ::fz_pixmap *pixmap, const ::fz_pwg_options *pwg);

/** Low-level wrapper for `::fz_write_ps_file_header()`. */
/**
	Write the file level header for ps band writer output.
*/
FZ_FUNCTION void ll_fz_write_ps_file_header(::fz_output *out);

/** Low-level wrapper for `::fz_write_ps_file_trailer()`. */
/**
	Write the file level trailer for ps band writer output.
*/
FZ_FUNCTION void ll_fz_write_ps_file_trailer(::fz_output *out, int pages);

/** Low-level wrapper for `::fz_write_pwg_file_header()`. */
/**
	Output the file header to a pwg stream, ready for pages to follow it.
*/
FZ_FUNCTION void ll_fz_write_pwg_file_header(::fz_output *out);

/** Low-level wrapper for `::fz_write_rune()`. */
/**
	Write a UTF-8 encoded unicode character.
*/
FZ_FUNCTION void ll_fz_write_rune(::fz_output *out, int rune);

/** Low-level wrapper for `::fz_write_stabilized_story()`. */
FZ_FUNCTION void ll_fz_write_stabilized_story(::fz_document_writer *writer, const char *user_css, float em, ::fz_write_story_contentfn contentfn, void *contentfn_ref, ::fz_write_story_rectfn rectfn, void *rectfn_ref, ::fz_write_story_pagefn pagefn, void *pagefn_ref, ::fz_archive *dir);

/** Low-level wrapper for `::fz_write_story()`. */
FZ_FUNCTION void ll_fz_write_story(::fz_document_writer *writer, ::fz_story *story, ::fz_write_story_rectfn rectfn, void *rectfn_ref, ::fz_write_story_positionfn positionfn, void *positionfn_ref, ::fz_write_story_pagefn pagefn, void *pagefn_ref);

/** Low-level wrapper for `::fz_write_stream()`. */
/**
	Copy the stream contents to the output.
*/
FZ_FUNCTION void ll_fz_write_stream(::fz_output *out, ::fz_stream *in);

/** Low-level wrapper for `::fz_write_string()`. */
/**
	Write a string. Does not write zero terminator.
*/
FZ_FUNCTION void ll_fz_write_string(::fz_output *out, const char *s);

/** Low-level wrapper for `::fz_write_uint16_be()`. */
FZ_FUNCTION void ll_fz_write_uint16_be(::fz_output *out, unsigned int x);

/** Low-level wrapper for `::fz_write_uint16_le()`. */
FZ_FUNCTION void ll_fz_write_uint16_le(::fz_output *out, unsigned int x);

/** Low-level wrapper for `::fz_write_uint32_be()`. */
FZ_FUNCTION void ll_fz_write_uint32_be(::fz_output *out, unsigned int x);

/** Low-level wrapper for `::fz_write_uint32_le()`. */
FZ_FUNCTION void ll_fz_write_uint32_le(::fz_output *out, unsigned int x);

/** Low-level wrapper for `::fz_write_vprintf()`. */
/**
	va_list version of fz_write_printf.
*/
FZ_FUNCTION void ll_fz_write_vprintf(::fz_output *out, const char *fmt, va_list ap);

/** Low-level wrapper for `::fz_write_zip_entry()`. */
/**
	Given a buffer of data, (optionally) compress it, and add it to
	the zip file with the given name.
*/
FZ_FUNCTION void ll_fz_write_zip_entry(::fz_zip_writer *zip, const char *name, ::fz_buffer *buf, int compress);

/** Low-level wrapper for `::fz_xml_add_att()`. */
/**
	Add an attribute to an XML node.
*/
FZ_FUNCTION void ll_fz_xml_add_att(::fz_pool *pool, ::fz_xml *node, const char *key, const char *val);

/** Low-level wrapper for `::fz_xml_att()`. */
/**
	Return the value of an attribute of an XML node.
	NULL if the attribute doesn't exist.
*/
FZ_FUNCTION char *ll_fz_xml_att(::fz_xml *item, const char *att);

/** Low-level wrapper for `::fz_xml_att_alt()`. */
/**
	Return the value of an attribute of an XML node.
	If the first attribute doesn't exist, try the second.
	NULL if neither attribute exists.
*/
FZ_FUNCTION char *ll_fz_xml_att_alt(::fz_xml *item, const char *one, const char *two);

/** Low-level wrapper for `::fz_xml_att_eq()`. */
/**
	Check for a matching attribute on an XML node.

	If the node has the requested attribute (name), and the value
	matches (match) then return 1. Otherwise, 0.
*/
FZ_FUNCTION int ll_fz_xml_att_eq(::fz_xml *item, const char *name, const char *match);

/** Low-level wrapper for `::fz_xml_down()`. */
/**
	Return first child of XML node.
*/
FZ_FUNCTION ::fz_xml *ll_fz_xml_down(::fz_xml *item);

/** Low-level wrapper for `::fz_xml_find()`. */
/**
	Search the siblings of XML nodes starting with item looking for
	the first with the given tag.

	Return NULL if none found.
*/
FZ_FUNCTION ::fz_xml *ll_fz_xml_find(::fz_xml *item, const char *tag);

/** Low-level wrapper for `::fz_xml_find_dfs()`. */
/**
	Perform a depth first search from item, returning the first
	child that matches the given tag (or any tag if tag is NULL),
	with the given attribute (if att is non NULL), that matches
	match (if match is non NULL).
*/
FZ_FUNCTION ::fz_xml *ll_fz_xml_find_dfs(::fz_xml *item, const char *tag, const char *att, const char *match);

/** Low-level wrapper for `::fz_xml_find_dfs_top()`. */
/**
	Perform a depth first search from item, returning the first
	child that matches the given tag (or any tag if tag is NULL),
	with the given attribute (if att is non NULL), that matches
	match (if match is non NULL). The search stops if it ever
	reaches the top of the tree, or the declared 'top' item.
*/
FZ_FUNCTION ::fz_xml *ll_fz_xml_find_dfs_top(::fz_xml *item, const char *tag, const char *att, const char *match, ::fz_xml *top);

/** Low-level wrapper for `::fz_xml_find_down()`. */
/**
	Search the siblings of XML nodes starting with the first child
	of item looking for the first with the given tag.

	Return NULL if none found.
*/
FZ_FUNCTION ::fz_xml *ll_fz_xml_find_down(::fz_xml *item, const char *tag);

/** Low-level wrapper for `::fz_xml_find_down_match()`. */
/**
	Search the siblings of XML nodes starting with the first child
	of item looking for the first with the given tag (or any tag if
	tag is NULL), and with a matching attribute.

	Return NULL if none found.
*/
FZ_FUNCTION ::fz_xml *ll_fz_xml_find_down_match(::fz_xml *item, const char *tag, const char *att, const char *match);

/** Low-level wrapper for `::fz_xml_find_match()`. */
/**
	Search the siblings of XML nodes starting with item looking for
	the first with the given tag (or any tag if tag is NULL), and
	with a matching attribute.

	Return NULL if none found.
*/
FZ_FUNCTION ::fz_xml *ll_fz_xml_find_match(::fz_xml *item, const char *tag, const char *att, const char *match);

/** Low-level wrapper for `::fz_xml_find_next()`. */
/**
	Search the siblings of XML nodes starting with the first sibling
	of item looking for the first with the given tag.

	Return NULL if none found.
*/
FZ_FUNCTION ::fz_xml *ll_fz_xml_find_next(::fz_xml *item, const char *tag);

/** Low-level wrapper for `::fz_xml_find_next_dfs()`. */
/**
	Perform a depth first search onwards from item, returning the first
	child that matches the given tag (or any tag if tag is NULL),
	with the given attribute (if att is non NULL), that matches
	match (if match is non NULL).
*/
FZ_FUNCTION ::fz_xml *ll_fz_xml_find_next_dfs(::fz_xml *item, const char *tag, const char *att, const char *match);

/** Low-level wrapper for `::fz_xml_find_next_dfs_top()`. */
/**
	Perform a depth first search onwards from item, returning the first
	child that matches the given tag (or any tag if tag is NULL),
	with the given attribute (if att is non NULL), that matches
	match (if match is non NULL). The search stops if it ever reaches
	the top of the tree, or the declared 'top' item.
*/
FZ_FUNCTION ::fz_xml *ll_fz_xml_find_next_dfs_top(::fz_xml *item, const char *tag, const char *att, const char *match, ::fz_xml *top);

/** Low-level wrapper for `::fz_xml_find_next_match()`. */
/**
	Search the siblings of XML nodes starting with the first sibling
	of item looking for the first with the given tag (or any tag if tag
	is NULL), and with a matching attribute.

	Return NULL if none found.
*/
FZ_FUNCTION ::fz_xml *ll_fz_xml_find_next_match(::fz_xml *item, const char *tag, const char *att, const char *match);

/** Low-level wrapper for `::fz_xml_is_tag()`. */
/**
	Return true if the tag name matches.
*/
FZ_FUNCTION int ll_fz_xml_is_tag(::fz_xml *item, const char *name);

/** Low-level wrapper for `::fz_xml_next()`. */
/**
	Return next sibling of XML node.
*/
FZ_FUNCTION ::fz_xml *ll_fz_xml_next(::fz_xml *item);

/** Low-level wrapper for `::fz_xml_prev()`. */
/**
	Return previous sibling of XML node.
*/
FZ_FUNCTION ::fz_xml *ll_fz_xml_prev(::fz_xml *item);

/** Low-level wrapper for `::fz_xml_root()`. */
/**
	Return the topmost XML node of a document.
*/
FZ_FUNCTION ::fz_xml *ll_fz_xml_root(::fz_xml_doc *xml);

/** Low-level wrapper for `::fz_xml_tag()`. */
/**
	Return tag of XML node. Return NULL for text nodes.
*/
FZ_FUNCTION char *ll_fz_xml_tag(::fz_xml *item);

/** Low-level wrapper for `::fz_xml_text()`. */
/**
	Return the text content of an XML node.
	Return NULL if the node is a tag.
*/
FZ_FUNCTION char *ll_fz_xml_text(::fz_xml *item);

/** Low-level wrapper for `::fz_xml_up()`. */
/**
	Return parent of XML node.
*/
FZ_FUNCTION ::fz_xml *ll_fz_xml_up(::fz_xml *item);

/** Low-level wrapper for `::pdf_abandon_operation()`. */
FZ_FUNCTION void ll_pdf_abandon_operation(::pdf_document *doc);

/** Low-level wrapper for `::pdf_access_alert_event()`. */
FZ_FUNCTION ::pdf_alert_event *ll_pdf_access_alert_event(::pdf_doc_event *evt);

/** Low-level wrapper for `::pdf_access_exec_menu_item_event()`. */
FZ_FUNCTION const char *ll_pdf_access_exec_menu_item_event(::pdf_doc_event *evt);

/** Low-level wrapper for `::pdf_access_launch_url_event()`. */
FZ_FUNCTION ::pdf_launch_url_event *ll_pdf_access_launch_url_event(::pdf_doc_event *evt);

/** Low-level wrapper for `::pdf_access_mail_doc_event()`. */
FZ_FUNCTION ::pdf_mail_doc_event *ll_pdf_access_mail_doc_event(::pdf_doc_event *evt);

/** Low-level wrapper for `::pdf_add_annot_border_dash_item()`. */
FZ_FUNCTION void ll_pdf_add_annot_border_dash_item(::pdf_annot *annot, float length);

/** Low-level wrapper for `::pdf_add_annot_ink_list()`. */
FZ_FUNCTION void ll_pdf_add_annot_ink_list(::pdf_annot *annot, int n, ::fz_point stroke[]);

/** Low-level wrapper for `::pdf_add_annot_ink_list_stroke()`. */
FZ_FUNCTION void ll_pdf_add_annot_ink_list_stroke(::pdf_annot *annot);

/** Low-level wrapper for `::pdf_add_annot_ink_list_stroke_vertex()`. */
FZ_FUNCTION void ll_pdf_add_annot_ink_list_stroke_vertex(::pdf_annot *annot, ::fz_point p);

/** Low-level wrapper for `::pdf_add_annot_quad_point()`. */
FZ_FUNCTION void ll_pdf_add_annot_quad_point(::pdf_annot *annot, ::fz_quad quad);

/** Low-level wrapper for `::pdf_add_annot_vertex()`. */
FZ_FUNCTION void ll_pdf_add_annot_vertex(::pdf_annot *annot, ::fz_point p);

/** Low-level wrapper for `::pdf_add_cid_font()`. */
FZ_FUNCTION ::pdf_obj *ll_pdf_add_cid_font(::pdf_document *doc, ::fz_font *font);

/** Low-level wrapper for `::pdf_add_cjk_font()`. */
FZ_FUNCTION ::pdf_obj *ll_pdf_add_cjk_font(::pdf_document *doc, ::fz_font *font, int script, int wmode, int serif);

/** Low-level wrapper for `::pdf_add_codespace()`. */
FZ_FUNCTION void ll_pdf_add_codespace(::pdf_cmap *cmap, unsigned int low, unsigned int high, size_t n);

/** Low-level wrapper for `::pdf_add_embedded_file()`. */
FZ_FUNCTION ::pdf_obj *ll_pdf_add_embedded_file(::pdf_document *doc, const char *filename, const char *mimetype, ::fz_buffer *contents, int64_t created, int64_t modifed, int add_checksum);

/** Low-level wrapper for `::pdf_add_hmtx()`. */
FZ_FUNCTION void ll_pdf_add_hmtx(::pdf_font_desc *font, int lo, int hi, int w);

/** Low-level wrapper for `::pdf_add_image()`. */
FZ_FUNCTION ::pdf_obj *ll_pdf_add_image(::pdf_document *doc, ::fz_image *image);

/** Low-level wrapper for `::pdf_add_journal_fragment()`. */
FZ_FUNCTION void ll_pdf_add_journal_fragment(::pdf_document *doc, int parent, ::pdf_obj *copy, ::fz_buffer *copy_stream, int newobj);

/** Low-level wrapper for `::pdf_add_new_array()`. */
FZ_FUNCTION ::pdf_obj *ll_pdf_add_new_array(::pdf_document *doc, int initial);

/** Low-level wrapper for `::pdf_add_new_dict()`. */
FZ_FUNCTION ::pdf_obj *ll_pdf_add_new_dict(::pdf_document *doc, int initial);

/** Low-level wrapper for `::pdf_add_object()`. */
FZ_FUNCTION ::pdf_obj *ll_pdf_add_object(::pdf_document *doc, ::pdf_obj *obj);

/** Low-level wrapper for `::pdf_add_object_drop()`. */
FZ_FUNCTION ::pdf_obj *ll_pdf_add_object_drop(::pdf_document *doc, ::pdf_obj *obj);

/** Low-level wrapper for `::pdf_add_page()`. */
FZ_FUNCTION ::pdf_obj *ll_pdf_add_page(::pdf_document *doc, ::fz_rect mediabox, int rotate, ::pdf_obj *resources, ::fz_buffer *contents);

/** Low-level wrapper for `::pdf_add_simple_font()`. */
FZ_FUNCTION ::pdf_obj *ll_pdf_add_simple_font(::pdf_document *doc, ::fz_font *font, int encoding);

/** Low-level wrapper for `::pdf_add_stream()`. */
FZ_FUNCTION ::pdf_obj *ll_pdf_add_stream(::pdf_document *doc, ::fz_buffer *buf, ::pdf_obj *obj, int compressed);

/** Low-level wrapper for `::pdf_add_substitute_font()`. */
FZ_FUNCTION ::pdf_obj *ll_pdf_add_substitute_font(::pdf_document *doc, ::fz_font *font);

/** Low-level wrapper for `::pdf_add_vmtx()`. */
FZ_FUNCTION void ll_pdf_add_vmtx(::pdf_font_desc *font, int lo, int hi, int x, int y, int w);

/** Low-level wrapper for `::pdf_annot_MK_BC()`.

This function has out-params. Python/C# wrappers look like:
	`ll_pdf_annot_MK_BC(::pdf_annot *annot, float color[4])` => int n
*/
FZ_FUNCTION void ll_pdf_annot_MK_BC(::pdf_annot *annot, int *n, float color[4]);

/** Low-level wrapper for `::pdf_annot_MK_BC_rgb()`. */
FZ_FUNCTION int ll_pdf_annot_MK_BC_rgb(::pdf_annot *annot, float rgb[3]);

/** Low-level wrapper for `::pdf_annot_MK_BG()`.

This function has out-params. Python/C# wrappers look like:
	`ll_pdf_annot_MK_BG(::pdf_annot *annot, float color[4])` => int n
*/
FZ_FUNCTION void ll_pdf_annot_MK_BG(::pdf_annot *annot, int *n, float color[4]);

/** Low-level wrapper for `::pdf_annot_MK_BG_rgb()`. */
FZ_FUNCTION int ll_pdf_annot_MK_BG_rgb(::pdf_annot *annot, float rgb[3]);

/** Low-level wrapper for `::pdf_annot_active()`. */
FZ_FUNCTION int ll_pdf_annot_active(::pdf_annot *annot);

/** Low-level wrapper for `::pdf_annot_ap()`. */
FZ_FUNCTION ::pdf_obj *ll_pdf_annot_ap(::pdf_annot *annot);

/** Low-level wrapper for `::pdf_annot_author()`. */
FZ_FUNCTION const char *ll_pdf_annot_author(::pdf_annot *annot);

/** Low-level wrapper for `::pdf_annot_border()`. */
FZ_FUNCTION float ll_pdf_annot_border(::pdf_annot *annot);

/** Low-level wrapper for `::pdf_annot_border_dash_count()`. */
FZ_FUNCTION int ll_pdf_annot_border_dash_count(::pdf_annot *annot);

/** Low-level wrapper for `::pdf_annot_border_dash_item()`. */
FZ_FUNCTION float ll_pdf_annot_border_dash_item(::pdf_annot *annot, int i);

/** Low-level wrapper for `::pdf_annot_border_effect()`. */
FZ_FUNCTION enum pdf_border_effect ll_pdf_annot_border_effect(::pdf_annot *annot);

/** Low-level wrapper for `::pdf_annot_border_effect_intensity()`. */
FZ_FUNCTION float ll_pdf_annot_border_effect_intensity(::pdf_annot *annot);

/** Low-level wrapper for `::pdf_annot_border_style()`. */
FZ_FUNCTION enum pdf_border_style ll_pdf_annot_border_style(::pdf_annot *annot);

/** Low-level wrapper for `::pdf_annot_border_width()`. */
FZ_FUNCTION float ll_pdf_annot_border_width(::pdf_annot *annot);

/** Low-level wrapper for `::pdf_annot_color()`.

This function has out-params. Python/C# wrappers look like:
	`ll_pdf_annot_color(::pdf_annot *annot, float color[4])` => int n
*/
FZ_FUNCTION void ll_pdf_annot_color(::pdf_annot *annot, int *n, float color[4]);

/** Low-level wrapper for `::pdf_annot_contents()`. */
FZ_FUNCTION const char *ll_pdf_annot_contents(::pdf_annot *annot);

/** Low-level wrapper for `::pdf_annot_creation_date()`. */
FZ_FUNCTION int64_t ll_pdf_annot_creation_date(::pdf_annot *annot);

/** Low-level wrapper for `::pdf_annot_default_appearance()`.

This function has out-params. Python/C# wrappers look like:
	`ll_pdf_annot_default_appearance(::pdf_annot *annot, float color[4])` => `(const char *font, float size, int n)`
*/
FZ_FUNCTION void ll_pdf_annot_default_appearance(::pdf_annot *annot, const char **font, float *size, int *n, float color[4]);

/** Low-level wrapper for `::pdf_annot_ensure_local_xref()`. */
FZ_FUNCTION void ll_pdf_annot_ensure_local_xref(::pdf_annot *annot);

/** Low-level wrapper for `::pdf_annot_event_blur()`. */
FZ_FUNCTION void ll_pdf_annot_event_blur(::pdf_annot *annot);

/** Low-level wrapper for `::pdf_annot_event_down()`. */
FZ_FUNCTION void ll_pdf_annot_event_down(::pdf_annot *annot);

/** Low-level wrapper for `::pdf_annot_event_enter()`. */
FZ_FUNCTION void ll_pdf_annot_event_enter(::pdf_annot *annot);

/** Low-level wrapper for `::pdf_annot_event_exit()`. */
FZ_FUNCTION void ll_pdf_annot_event_exit(::pdf_annot *annot);

/** Low-level wrapper for `::pdf_annot_event_focus()`. */
FZ_FUNCTION void ll_pdf_annot_event_focus(::pdf_annot *annot);

/** Low-level wrapper for `::pdf_annot_event_page_close()`. */
FZ_FUNCTION void ll_pdf_annot_event_page_close(::pdf_annot *annot);

/** Low-level wrapper for `::pdf_annot_event_page_invisible()`. */
FZ_FUNCTION void ll_pdf_annot_event_page_invisible(::pdf_annot *annot);

/** Low-level wrapper for `::pdf_annot_event_page_open()`. */
FZ_FUNCTION void ll_pdf_annot_event_page_open(::pdf_annot *annot);

/** Low-level wrapper for `::pdf_annot_event_page_visible()`. */
FZ_FUNCTION void ll_pdf_annot_event_page_visible(::pdf_annot *annot);

/** Low-level wrapper for `::pdf_annot_event_up()`. */
FZ_FUNCTION void ll_pdf_annot_event_up(::pdf_annot *annot);

/** Low-level wrapper for `::pdf_annot_field_event_keystroke()`. */
FZ_FUNCTION int ll_pdf_annot_field_event_keystroke(::pdf_document *doc, ::pdf_annot *annot, ::pdf_keystroke_event *evt);

/** Low-level wrapper for `::pdf_annot_field_flags()`. */
FZ_FUNCTION int ll_pdf_annot_field_flags(::pdf_annot *annot);

/** Low-level wrapper for `::pdf_annot_field_label()`. */
FZ_FUNCTION const char *ll_pdf_annot_field_label(::pdf_annot *widget);

/** Low-level wrapper for `::pdf_annot_field_value()`. */
FZ_FUNCTION const char *ll_pdf_annot_field_value(::pdf_annot *annot);

/** Low-level wrapper for `::pdf_annot_filespec()`. */
FZ_FUNCTION ::pdf_obj *ll_pdf_annot_filespec(::pdf_annot *annot);

/** Low-level wrapper for `::pdf_annot_flags()`. */
FZ_FUNCTION int ll_pdf_annot_flags(::pdf_annot *annot);

/** Low-level wrapper for `::pdf_annot_has_author()`. */
FZ_FUNCTION int ll_pdf_annot_has_author(::pdf_annot *annot);

/** Low-level wrapper for `::pdf_annot_has_border()`. */
FZ_FUNCTION int ll_pdf_annot_has_border(::pdf_annot *annot);

/** Low-level wrapper for `::pdf_annot_has_border_effect()`. */
FZ_FUNCTION int ll_pdf_annot_has_border_effect(::pdf_annot *annot);

/** Low-level wrapper for `::pdf_annot_has_filespec()`. */
FZ_FUNCTION int ll_pdf_annot_has_filespec(::pdf_annot *annot);

/** Low-level wrapper for `::pdf_annot_has_icon_name()`. */
FZ_FUNCTION int ll_pdf_annot_has_icon_name(::pdf_annot *annot);

/** Low-level wrapper for `::pdf_annot_has_ink_list()`. */
FZ_FUNCTION int ll_pdf_annot_has_ink_list(::pdf_annot *annot);

/** Low-level wrapper for `::pdf_annot_has_intent()`. */
FZ_FUNCTION int ll_pdf_annot_has_intent(::pdf_annot *annot);

/** Low-level wrapper for `::pdf_annot_has_interior_color()`. */
FZ_FUNCTION int ll_pdf_annot_has_interior_color(::pdf_annot *annot);

/** Low-level wrapper for `::pdf_annot_has_line()`. */
FZ_FUNCTION int ll_pdf_annot_has_line(::pdf_annot *annot);

/** Low-level wrapper for `::pdf_annot_has_line_ending_styles()`. */
FZ_FUNCTION int ll_pdf_annot_has_line_ending_styles(::pdf_annot *annot);

/** Low-level wrapper for `::pdf_annot_has_open()`. */
FZ_FUNCTION int ll_pdf_annot_has_open(::pdf_annot *annot);

/** Low-level wrapper for `::pdf_annot_has_quad_points()`. */
FZ_FUNCTION int ll_pdf_annot_has_quad_points(::pdf_annot *annot);

/** Low-level wrapper for `::pdf_annot_has_quadding()`. */
FZ_FUNCTION int ll_pdf_annot_has_quadding(::pdf_annot *annot);

/** Low-level wrapper for `::pdf_annot_has_rect()`. */
FZ_FUNCTION int ll_pdf_annot_has_rect(::pdf_annot *annot);

/** Low-level wrapper for `::pdf_annot_has_vertices()`. */
FZ_FUNCTION int ll_pdf_annot_has_vertices(::pdf_annot *annot);

/** Low-level wrapper for `::pdf_annot_hidden_for_editing()`. */
FZ_FUNCTION int ll_pdf_annot_hidden_for_editing(::pdf_annot *annot);

/** Low-level wrapper for `::pdf_annot_hot()`. */
FZ_FUNCTION int ll_pdf_annot_hot(::pdf_annot *annot);

/** Low-level wrapper for `::pdf_annot_icon_name()`. */
FZ_FUNCTION const char *ll_pdf_annot_icon_name(::pdf_annot *annot);

/** Low-level wrapper for `::pdf_annot_ink_list_count()`. */
FZ_FUNCTION int ll_pdf_annot_ink_list_count(::pdf_annot *annot);

/** Low-level wrapper for `::pdf_annot_ink_list_stroke_count()`. */
FZ_FUNCTION int ll_pdf_annot_ink_list_stroke_count(::pdf_annot *annot, int i);

/** Low-level wrapper for `::pdf_annot_ink_list_stroke_vertex()`. */
FZ_FUNCTION ::fz_point ll_pdf_annot_ink_list_stroke_vertex(::pdf_annot *annot, int i, int k);

/** Low-level wrapper for `::pdf_annot_intent()`. */
FZ_FUNCTION enum pdf_intent ll_pdf_annot_intent(::pdf_annot *annot);

/** Low-level wrapper for `::pdf_annot_interior_color()`.

This function has out-params. Python/C# wrappers look like:
	`ll_pdf_annot_interior_color(::pdf_annot *annot, float color[4])` => int n
*/
FZ_FUNCTION void ll_pdf_annot_interior_color(::pdf_annot *annot, int *n, float color[4]);

/** Low-level wrapper for `::pdf_annot_is_open()`. */
FZ_FUNCTION int ll_pdf_annot_is_open(::pdf_annot *annot);

/** Low-level wrapper for `::pdf_annot_is_standard_stamp()`. */
FZ_FUNCTION int ll_pdf_annot_is_standard_stamp(::pdf_annot *annot);

/** Low-level wrapper for `::pdf_annot_language()`. */
FZ_FUNCTION ::fz_text_language ll_pdf_annot_language(::pdf_annot *annot);

/** Low-level wrapper for `::pdf_annot_line()`. */
FZ_FUNCTION void ll_pdf_annot_line(::pdf_annot *annot, ::fz_point *a, ::fz_point *b);

/** Low-level wrapper for `::pdf_annot_line_end_style()`. */
FZ_FUNCTION enum pdf_line_ending ll_pdf_annot_line_end_style(::pdf_annot *annot);

/** Low-level wrapper for `::pdf_annot_line_ending_styles()`.

This function has out-params. Python/C# wrappers look like:
	`ll_pdf_annot_line_ending_styles(::pdf_annot *annot)` => `(enum pdf_line_ending start_style, enum pdf_line_ending end_style)`
*/
FZ_FUNCTION void ll_pdf_annot_line_ending_styles(::pdf_annot *annot, enum pdf_line_ending *start_style, enum pdf_line_ending *end_style);

/** Low-level wrapper for `::pdf_annot_line_start_style()`. */
FZ_FUNCTION enum pdf_line_ending ll_pdf_annot_line_start_style(::pdf_annot *annot);

/** Low-level wrapper for `::pdf_annot_modification_date()`. */
FZ_FUNCTION int64_t ll_pdf_annot_modification_date(::pdf_annot *annot);

/** Low-level wrapper for `::pdf_annot_needs_resynthesis()`. */
FZ_FUNCTION int ll_pdf_annot_needs_resynthesis(::pdf_annot *annot);

/** Low-level wrapper for `::pdf_annot_obj()`. */
FZ_FUNCTION ::pdf_obj *ll_pdf_annot_obj(::pdf_annot *annot);

/** Low-level wrapper for `::pdf_annot_opacity()`. */
FZ_FUNCTION float ll_pdf_annot_opacity(::pdf_annot *annot);

/** Low-level wrapper for `::pdf_annot_page()`. */
FZ_FUNCTION ::pdf_page *ll_pdf_annot_page(::pdf_annot *annot);

/** Low-level wrapper for `::pdf_annot_pop_and_discard_local_xref()`. */
FZ_FUNCTION void ll_pdf_annot_pop_and_discard_local_xref(::pdf_annot *annot);

/** Low-level wrapper for `::pdf_annot_pop_local_xref()`. */
FZ_FUNCTION void ll_pdf_annot_pop_local_xref(::pdf_annot *annot);

/** Low-level wrapper for `::pdf_annot_popup()`. */
FZ_FUNCTION ::fz_rect ll_pdf_annot_popup(::pdf_annot *annot);

/** Low-level wrapper for `::pdf_annot_push_local_xref()`. */
FZ_FUNCTION void ll_pdf_annot_push_local_xref(::pdf_annot *annot);

/** Low-level wrapper for `::pdf_annot_quad_point()`. */
FZ_FUNCTION ::fz_quad ll_pdf_annot_quad_point(::pdf_annot *annot, int i);

/** Low-level wrapper for `::pdf_annot_quad_point_count()`. */
FZ_FUNCTION int ll_pdf_annot_quad_point_count(::pdf_annot *annot);

/** Low-level wrapper for `::pdf_annot_quadding()`. */
FZ_FUNCTION int ll_pdf_annot_quadding(::pdf_annot *annot);

/** Low-level wrapper for `::pdf_annot_rect()`. */
FZ_FUNCTION ::fz_rect ll_pdf_annot_rect(::pdf_annot *annot);

/** Low-level wrapper for `::pdf_annot_request_resynthesis()`. */
FZ_FUNCTION void ll_pdf_annot_request_resynthesis(::pdf_annot *annot);

/** Low-level wrapper for `::pdf_annot_request_synthesis()`. */
FZ_FUNCTION void ll_pdf_annot_request_synthesis(::pdf_annot *annot);

/** Low-level wrapper for `::pdf_annot_transform()`. */
FZ_FUNCTION ::fz_matrix ll_pdf_annot_transform(::pdf_annot *annot);

/** Low-level wrapper for `::pdf_annot_type()`. */
FZ_FUNCTION enum pdf_annot_type ll_pdf_annot_type(::pdf_annot *annot);

/** Low-level wrapper for `::pdf_annot_type_from_string()`. */
FZ_FUNCTION enum pdf_annot_type ll_pdf_annot_type_from_string(const char *subtype);

/** Low-level wrapper for `::pdf_annot_vertex()`. */
FZ_FUNCTION ::fz_point ll_pdf_annot_vertex(::pdf_annot *annot, int i);

/** Low-level wrapper for `::pdf_annot_vertex_count()`. */
FZ_FUNCTION int ll_pdf_annot_vertex_count(::pdf_annot *annot);

/** Low-level wrapper for `::pdf_append_explicit_dest_to_uri()`. */
FZ_FUNCTION char *ll_pdf_append_explicit_dest_to_uri(const char *url, ::fz_link_dest dest);

/** Low-level wrapper for `::pdf_append_named_dest_to_uri()`. */
FZ_FUNCTION char *ll_pdf_append_named_dest_to_uri(const char *url, const char *name);

/** Low-level wrapper for `::pdf_append_token()`. */
FZ_FUNCTION void ll_pdf_append_token(::fz_buffer *buf, int tok, ::pdf_lexbuf *lex);

/** Low-level wrapper for `::pdf_apply_redaction()`. */
FZ_FUNCTION int ll_pdf_apply_redaction(::pdf_annot *annot, ::pdf_redact_options *opts);

/** Low-level wrapper for `::pdf_array_contains()`. */
FZ_FUNCTION int ll_pdf_array_contains(::pdf_obj *array, ::pdf_obj *obj);

/** Low-level wrapper for `::pdf_array_delete()`. */
FZ_FUNCTION void ll_pdf_array_delete(::pdf_obj *array, int index);

/** Low-level wrapper for `::pdf_array_find()`. */
FZ_FUNCTION int ll_pdf_array_find(::pdf_obj *array, ::pdf_obj *obj);

/** Low-level wrapper for `::pdf_array_get()`. */
FZ_FUNCTION ::pdf_obj *ll_pdf_array_get(::pdf_obj *array, int i);

/** Low-level wrapper for `::pdf_array_get_bool()`. */
FZ_FUNCTION int ll_pdf_array_get_bool(::pdf_obj *array, int index);

/** Low-level wrapper for `::pdf_array_get_int()`. */
FZ_FUNCTION int ll_pdf_array_get_int(::pdf_obj *array, int index);

/** Low-level wrapper for `::pdf_array_get_matrix()`. */
FZ_FUNCTION ::fz_matrix ll_pdf_array_get_matrix(::pdf_obj *array, int index);

/** Low-level wrapper for `::pdf_array_get_name()`. */
FZ_FUNCTION const char *ll_pdf_array_get_name(::pdf_obj *array, int index);

/** Low-level wrapper for `::pdf_array_get_real()`. */
FZ_FUNCTION float ll_pdf_array_get_real(::pdf_obj *array, int index);

/** Low-level wrapper for `::pdf_array_get_rect()`. */
FZ_FUNCTION ::fz_rect ll_pdf_array_get_rect(::pdf_obj *array, int index);

/** Low-level wrapper for `::pdf_array_get_string()`.

This function has out-params. Python/C# wrappers look like:
	`ll_pdf_array_get_string(::pdf_obj *array, int index)` => `(const char *, size_t sizep)`
*/
FZ_FUNCTION const char *ll_pdf_array_get_string(::pdf_obj *array, int index, size_t *sizep);

/** Low-level wrapper for `::pdf_array_get_text_string()`. */
FZ_FUNCTION const char *ll_pdf_array_get_text_string(::pdf_obj *array, int index);

/** Low-level wrapper for `::pdf_array_insert()`. */
FZ_FUNCTION void ll_pdf_array_insert(::pdf_obj *array, ::pdf_obj *obj, int index);

/** Low-level wrapper for `::pdf_array_insert_drop()`. */
FZ_FUNCTION void ll_pdf_array_insert_drop(::pdf_obj *array, ::pdf_obj *obj, int index);

/** Low-level wrapper for `::pdf_array_len()`. */
FZ_FUNCTION int ll_pdf_array_len(::pdf_obj *array);

/** Low-level wrapper for `::pdf_array_push()`. */
FZ_FUNCTION void ll_pdf_array_push(::pdf_obj *array, ::pdf_obj *obj);

/** Low-level wrapper for `::pdf_array_push_array()`. */
FZ_FUNCTION ::pdf_obj *ll_pdf_array_push_array(::pdf_obj *array, int initial);

/** Low-level wrapper for `::pdf_array_push_bool()`. */
FZ_FUNCTION void ll_pdf_array_push_bool(::pdf_obj *array, int x);

/** Low-level wrapper for `::pdf_array_push_dict()`. */
FZ_FUNCTION ::pdf_obj *ll_pdf_array_push_dict(::pdf_obj *array, int initial);

/** Low-level wrapper for `::pdf_array_push_drop()`. */
FZ_FUNCTION void ll_pdf_array_push_drop(::pdf_obj *array, ::pdf_obj *obj);

/** Low-level wrapper for `::pdf_array_push_int()`. */
FZ_FUNCTION void ll_pdf_array_push_int(::pdf_obj *array, int64_t x);

/** Low-level wrapper for `::pdf_array_push_name()`. */
FZ_FUNCTION void ll_pdf_array_push_name(::pdf_obj *array, const char *x);

/** Low-level wrapper for `::pdf_array_push_real()`. */
FZ_FUNCTION void ll_pdf_array_push_real(::pdf_obj *array, double x);

/** Low-level wrapper for `::pdf_array_push_string()`. */
FZ_FUNCTION void ll_pdf_array_push_string(::pdf_obj *array, const char *x, size_t n);

/** Low-level wrapper for `::pdf_array_push_text_string()`. */
FZ_FUNCTION void ll_pdf_array_push_text_string(::pdf_obj *array, const char *x);

/** Low-level wrapper for `::pdf_array_put()`. */
FZ_FUNCTION void ll_pdf_array_put(::pdf_obj *array, int i, ::pdf_obj *obj);

/** Low-level wrapper for `::pdf_array_put_array()`. */
FZ_FUNCTION ::pdf_obj *ll_pdf_array_put_array(::pdf_obj *array, int i, int initial);

/** Low-level wrapper for `::pdf_array_put_bool()`. */
FZ_FUNCTION void ll_pdf_array_put_bool(::pdf_obj *array, int i, int x);

/** Low-level wrapper for `::pdf_array_put_dict()`. */
FZ_FUNCTION ::pdf_obj *ll_pdf_array_put_dict(::pdf_obj *array, int i, int initial);

/** Low-level wrapper for `::pdf_array_put_drop()`. */
FZ_FUNCTION void ll_pdf_array_put_drop(::pdf_obj *array, int i, ::pdf_obj *obj);

/** Low-level wrapper for `::pdf_array_put_int()`. */
FZ_FUNCTION void ll_pdf_array_put_int(::pdf_obj *array, int i, int64_t x);

/** Low-level wrapper for `::pdf_array_put_name()`. */
FZ_FUNCTION void ll_pdf_array_put_name(::pdf_obj *array, int i, const char *x);

/** Low-level wrapper for `::pdf_array_put_real()`. */
FZ_FUNCTION void ll_pdf_array_put_real(::pdf_obj *array, int i, double x);

/** Low-level wrapper for `::pdf_array_put_string()`. */
FZ_FUNCTION void ll_pdf_array_put_string(::pdf_obj *array, int i, const char *x, size_t n);

/** Low-level wrapper for `::pdf_array_put_text_string()`. */
FZ_FUNCTION void ll_pdf_array_put_text_string(::pdf_obj *array, int i, const char *x);

/** Low-level wrapper for `::pdf_authenticate_password()`. */
FZ_FUNCTION int ll_pdf_authenticate_password(::pdf_document *doc, const char *pw);

/** Low-level wrapper for `::pdf_bake_document()`. */
FZ_FUNCTION void ll_pdf_bake_document(::pdf_document *doc, int bake_annots, int bake_widgets);

/** Low-level wrapper for `::pdf_begin_implicit_operation()`. */
FZ_FUNCTION void ll_pdf_begin_implicit_operation(::pdf_document *doc);

/** Low-level wrapper for `::pdf_begin_operation()`. */
FZ_FUNCTION void ll_pdf_begin_operation(::pdf_document *doc, const char *operation);

/** Low-level wrapper for `::pdf_bound_annot()`. */
FZ_FUNCTION ::fz_rect ll_pdf_bound_annot(::pdf_annot *annot);

/** Low-level wrapper for `::pdf_bound_page()`. */
FZ_FUNCTION ::fz_rect ll_pdf_bound_page(::pdf_page *page, ::fz_box_type box);

/** Low-level wrapper for `::pdf_bound_widget()`. */
FZ_FUNCTION ::fz_rect ll_pdf_bound_widget(::pdf_annot *widget);

/** Low-level wrapper for `::pdf_button_field_on_state()`. */
FZ_FUNCTION ::pdf_obj *ll_pdf_button_field_on_state(::pdf_obj *field);

/** Low-level wrapper for `::pdf_cache_object()`. */
/**
	Retrieve the pdf_xref_entry for a given object.

	This can cause xref reorganisations (solidifications etc) due to
	repairs, so all held pdf_xref_entries should be considered
	invalid after this call (other than the returned one).
*/
FZ_FUNCTION ::pdf_xref_entry *ll_pdf_cache_object(::pdf_document *doc, int num);

/** Low-level wrapper for `::pdf_calculate_form()`. */
FZ_FUNCTION void ll_pdf_calculate_form(::pdf_document *doc);

/** Low-level wrapper for `::pdf_can_be_saved_incrementally()`. */
FZ_FUNCTION int ll_pdf_can_be_saved_incrementally(::pdf_document *doc);

/** Low-level wrapper for `::pdf_can_redo()`. */
FZ_FUNCTION int ll_pdf_can_redo(::pdf_document *doc);

/** Low-level wrapper for `::pdf_can_undo()`. */
FZ_FUNCTION int ll_pdf_can_undo(::pdf_document *doc);

/** Low-level wrapper for `::pdf_check_certificate()`. */
FZ_FUNCTION ::pdf_signature_error ll_pdf_check_certificate(::pdf_pkcs7_verifier *verifier, ::pdf_document *doc, ::pdf_obj *signature);

/** Low-level wrapper for `::pdf_check_digest()`. */
FZ_FUNCTION ::pdf_signature_error ll_pdf_check_digest(::pdf_pkcs7_verifier *verifier, ::pdf_document *doc, ::pdf_obj *signature);

/** Low-level wrapper for `::pdf_check_widget_certificate()`. */
FZ_FUNCTION ::pdf_signature_error ll_pdf_check_widget_certificate(::pdf_pkcs7_verifier *verifier, ::pdf_annot *widget);

/** Low-level wrapper for `::pdf_check_widget_digest()`. */
FZ_FUNCTION ::pdf_signature_error ll_pdf_check_widget_digest(::pdf_pkcs7_verifier *verifier, ::pdf_annot *widget);

/** Low-level wrapper for `::pdf_choice_field_option()`. */
FZ_FUNCTION const char *ll_pdf_choice_field_option(::pdf_obj *field, int exportval, int i);

/** Low-level wrapper for `::pdf_choice_field_option_count()`. */
FZ_FUNCTION int ll_pdf_choice_field_option_count(::pdf_obj *field);

/** Low-level wrapper for `::pdf_choice_widget_is_multiselect()`. */
FZ_FUNCTION int ll_pdf_choice_widget_is_multiselect(::pdf_annot *tw);

/** Low-level wrapper for `::pdf_choice_widget_options()`. */
FZ_FUNCTION int ll_pdf_choice_widget_options(::pdf_annot *tw, int exportval, const char *opts[]);

/** Low-level wrapper for `::pdf_choice_widget_options2()`. */
/** Swig-friendly wrapper for pdf_choice_widget_options(), returns the
options directly in a vector. */
FZ_FUNCTION std::vector<std::string> ll_pdf_choice_widget_options2(::pdf_annot *tw, int exportval);

/** Low-level wrapper for `::pdf_choice_widget_set_value()`. */
FZ_FUNCTION void ll_pdf_choice_widget_set_value(::pdf_annot *tw, int n, const char *opts[]);

/** Low-level wrapper for `::pdf_choice_widget_value()`. */
FZ_FUNCTION int ll_pdf_choice_widget_value(::pdf_annot *tw, const char *opts[]);

/** Low-level wrapper for `::pdf_clean_file()`. */
FZ_FUNCTION void ll_pdf_clean_file(char *infile, char *outfile, char *password, ::pdf_clean_options *opts, int retainlen, char *retainlist[]);

/** Low-level wrapper for `::pdf_clean_font_name()`. */
FZ_FUNCTION const char *ll_pdf_clean_font_name(const char *fontname);

/** Low-level wrapper for `::pdf_clean_obj()`. */
FZ_FUNCTION void ll_pdf_clean_obj(::pdf_obj *obj);

/** Low-level wrapper for `::pdf_clear_annot_border_dash()`. */
FZ_FUNCTION void ll_pdf_clear_annot_border_dash(::pdf_annot *annot);

/** Low-level wrapper for `::pdf_clear_annot_ink_list()`. */
FZ_FUNCTION void ll_pdf_clear_annot_ink_list(::pdf_annot *annot);

/** Low-level wrapper for `::pdf_clear_annot_quad_points()`. */
FZ_FUNCTION void ll_pdf_clear_annot_quad_points(::pdf_annot *annot);

/** Low-level wrapper for `::pdf_clear_annot_vertices()`. */
FZ_FUNCTION void ll_pdf_clear_annot_vertices(::pdf_annot *annot);

/** Low-level wrapper for `::pdf_clear_signature()`. */
FZ_FUNCTION void ll_pdf_clear_signature(::pdf_annot *widget);

/** Low-level wrapper for `::pdf_clear_xref()`. */
FZ_FUNCTION void ll_pdf_clear_xref(::pdf_document *doc);

/** Low-level wrapper for `::pdf_clear_xref_to_mark()`. */
FZ_FUNCTION void ll_pdf_clear_xref_to_mark(::pdf_document *doc);

/** Low-level wrapper for `::pdf_close_processor()`. */
FZ_FUNCTION void ll_pdf_close_processor(::pdf_processor *proc);

/** Low-level wrapper for `::pdf_cmap_size()`. */
FZ_FUNCTION size_t ll_pdf_cmap_size(::pdf_cmap *cmap);

/** Low-level wrapper for `::pdf_cmap_wmode()`. */
FZ_FUNCTION int ll_pdf_cmap_wmode(::pdf_cmap *cmap);

/** Low-level wrapper for `::pdf_copy_array()`. */
FZ_FUNCTION ::pdf_obj *ll_pdf_copy_array(::pdf_obj *array);

/** Low-level wrapper for `::pdf_copy_dict()`. */
FZ_FUNCTION ::pdf_obj *ll_pdf_copy_dict(::pdf_obj *dict);

/** Low-level wrapper for `::pdf_count_layer_config_ui()`. */
FZ_FUNCTION int ll_pdf_count_layer_config_ui(::pdf_document *doc);

/** Low-level wrapper for `::pdf_count_layer_configs()`. */
FZ_FUNCTION int ll_pdf_count_layer_configs(::pdf_document *doc);

/** Low-level wrapper for `::pdf_count_layers()`. */
FZ_FUNCTION int ll_pdf_count_layers(::pdf_document *doc);

/** Low-level wrapper for `::pdf_count_objects()`. */
FZ_FUNCTION int ll_pdf_count_objects(::pdf_document *doc);

/** Low-level wrapper for `::pdf_count_pages()`. */
FZ_FUNCTION int ll_pdf_count_pages(::pdf_document *doc);

/** Low-level wrapper for `::pdf_count_pages_imp()`. */
FZ_FUNCTION int ll_pdf_count_pages_imp(::fz_document *doc, int chapter);

/** Low-level wrapper for `::pdf_count_q_balance()`.

This function has out-params. Python/C# wrappers look like:
	`ll_pdf_count_q_balance(::pdf_document *doc, ::pdf_obj *res, ::pdf_obj *stm)` => `(int prepend, int append)`
*/
FZ_FUNCTION void ll_pdf_count_q_balance(::pdf_document *doc, ::pdf_obj *res, ::pdf_obj *stm, int *prepend, int *append);

/** Low-level wrapper for `::pdf_count_signatures()`. */
FZ_FUNCTION int ll_pdf_count_signatures(::pdf_document *doc);

/** Low-level wrapper for `::pdf_count_unsaved_versions()`. */
FZ_FUNCTION int ll_pdf_count_unsaved_versions(::pdf_document *doc);

/** Low-level wrapper for `::pdf_count_versions()`. */
FZ_FUNCTION int ll_pdf_count_versions(::pdf_document *doc);

/** Low-level wrapper for `::pdf_create_annot()`. */
FZ_FUNCTION ::pdf_annot *ll_pdf_create_annot(::pdf_page *page, enum pdf_annot_type type);

/** Low-level wrapper for `::pdf_create_annot_raw()`. */
FZ_FUNCTION ::pdf_annot *ll_pdf_create_annot_raw(::pdf_page *page, enum pdf_annot_type type);

/** Low-level wrapper for `::pdf_create_document()`. */
FZ_FUNCTION ::pdf_document *ll_pdf_create_document();

/** Low-level wrapper for `::pdf_create_field_name()`. */
FZ_FUNCTION void ll_pdf_create_field_name(::pdf_document *doc, const char *prefix, char *buf, size_t len);

/** Low-level wrapper for `::pdf_create_link()`. */
FZ_FUNCTION ::fz_link *ll_pdf_create_link(::pdf_page *page, ::fz_rect bbox, const char *uri);

/** Low-level wrapper for `::pdf_create_object()`. */
FZ_FUNCTION int ll_pdf_create_object(::pdf_document *doc);

/** Low-level wrapper for `::pdf_create_signature_widget()`. */
FZ_FUNCTION ::pdf_annot *ll_pdf_create_signature_widget(::pdf_page *page, char *name);

/** Low-level wrapper for `::pdf_crypt_encrypt_metadata()`. */
FZ_FUNCTION int ll_pdf_crypt_encrypt_metadata(::pdf_crypt *crypt);

/** Low-level wrapper for `::pdf_crypt_key()`. */
FZ_FUNCTION unsigned char *ll_pdf_crypt_key(::pdf_crypt *crypt);

/** Low-level wrapper for `::pdf_crypt_length()`. */
FZ_FUNCTION int ll_pdf_crypt_length(::pdf_crypt *crypt);

/** Low-level wrapper for `::pdf_crypt_method()`. */
FZ_FUNCTION const char *ll_pdf_crypt_method(::pdf_crypt *crypt);

/** Low-level wrapper for `::pdf_crypt_obj()`. */
FZ_FUNCTION void ll_pdf_crypt_obj(::pdf_crypt *crypt, ::pdf_obj *obj, int num, int gen);

/** Low-level wrapper for `::pdf_crypt_owner_encryption()`. */
FZ_FUNCTION unsigned char *ll_pdf_crypt_owner_encryption(::pdf_crypt *crypt);

/** Low-level wrapper for `::pdf_crypt_owner_password()`. */
FZ_FUNCTION unsigned char *ll_pdf_crypt_owner_password(::pdf_crypt *crypt);

/** Low-level wrapper for `::pdf_crypt_permissions()`. */
FZ_FUNCTION int ll_pdf_crypt_permissions(::pdf_crypt *crypt);

/** Low-level wrapper for `::pdf_crypt_permissions_encryption()`. */
FZ_FUNCTION unsigned char *ll_pdf_crypt_permissions_encryption(::pdf_crypt *crypt);

/** Low-level wrapper for `::pdf_crypt_revision()`. */
FZ_FUNCTION int ll_pdf_crypt_revision(::pdf_crypt *crypt);

/** Low-level wrapper for `::pdf_crypt_stream_method()`. */
FZ_FUNCTION const char *ll_pdf_crypt_stream_method(::pdf_crypt *crypt);

/** Low-level wrapper for `::pdf_crypt_string_method()`. */
FZ_FUNCTION const char *ll_pdf_crypt_string_method(::pdf_crypt *crypt);

/** Low-level wrapper for `::pdf_crypt_user_encryption()`. */
FZ_FUNCTION unsigned char *ll_pdf_crypt_user_encryption(::pdf_crypt *crypt);

/** Low-level wrapper for `::pdf_crypt_user_password()`. */
FZ_FUNCTION unsigned char *ll_pdf_crypt_user_password(::pdf_crypt *crypt);

/** Low-level wrapper for `::pdf_crypt_version()`. */
FZ_FUNCTION int ll_pdf_crypt_version(::pdf_crypt *crypt);

/** Low-level wrapper for `::pdf_cycle()`. */
FZ_FUNCTION int ll_pdf_cycle(::pdf_cycle_list *here, ::pdf_cycle_list *prev, ::pdf_obj *obj);

/** Low-level wrapper for `::pdf_debug_doc_changes()`. */
FZ_FUNCTION void ll_pdf_debug_doc_changes(::pdf_document *doc);

/** Low-level wrapper for `::pdf_debug_obj()`. */
FZ_FUNCTION void ll_pdf_debug_obj(::pdf_obj *obj);

/** Low-level wrapper for `::pdf_debug_ref()`. */
FZ_FUNCTION void ll_pdf_debug_ref(::pdf_obj *obj);

/** Low-level wrapper for `::pdf_decode_cmap()`.

This function has out-params. Python/C# wrappers look like:
	`ll_pdf_decode_cmap(::pdf_cmap *cmap, unsigned char *s, unsigned char *e)` => `(int, unsigned int cpt)`
*/
FZ_FUNCTION int ll_pdf_decode_cmap(::pdf_cmap *cmap, unsigned char *s, unsigned char *e, unsigned int *cpt);

/** Low-level wrapper for `::pdf_deep_copy_obj()`. */
FZ_FUNCTION ::pdf_obj *ll_pdf_deep_copy_obj(::pdf_obj *obj);

/** Low-level wrapper for `::pdf_delete_annot()`. */
FZ_FUNCTION void ll_pdf_delete_annot(::pdf_page *page, ::pdf_annot *annot);

/** Low-level wrapper for `::pdf_delete_link()`. */
FZ_FUNCTION void ll_pdf_delete_link(::pdf_page *page, ::fz_link *link);

/** Low-level wrapper for `::pdf_delete_object()`. */
FZ_FUNCTION void ll_pdf_delete_object(::pdf_document *doc, int num);

/** Low-level wrapper for `::pdf_delete_page()`. */
FZ_FUNCTION void ll_pdf_delete_page(::pdf_document *doc, int number);

/** Low-level wrapper for `::pdf_delete_page_labels()`. */
FZ_FUNCTION void ll_pdf_delete_page_labels(::pdf_document *doc, int index);

/** Low-level wrapper for `::pdf_delete_page_range()`. */
FZ_FUNCTION void ll_pdf_delete_page_range(::pdf_document *doc, int start, int end);

/** Low-level wrapper for `::pdf_deselect_layer_config_ui()`. */
FZ_FUNCTION void ll_pdf_deselect_layer_config_ui(::pdf_document *doc, int ui);

/** Low-level wrapper for `::pdf_deserialise_journal()`. */
FZ_FUNCTION void ll_pdf_deserialise_journal(::pdf_document *doc, ::fz_stream *stm);

/** Low-level wrapper for `::pdf_dict_del()`. */
FZ_FUNCTION void ll_pdf_dict_del(::pdf_obj *dict, ::pdf_obj *key);

/** Low-level wrapper for `::pdf_dict_dels()`. */
FZ_FUNCTION void ll_pdf_dict_dels(::pdf_obj *dict, const char *key);

/** Low-level wrapper for `::pdf_dict_get()`. */
FZ_FUNCTION ::pdf_obj *ll_pdf_dict_get(::pdf_obj *dict, ::pdf_obj *key);

/** Low-level wrapper for `::pdf_dict_get_bool()`. */
FZ_FUNCTION int ll_pdf_dict_get_bool(::pdf_obj *dict, ::pdf_obj *key);

/** Low-level wrapper for `::pdf_dict_get_bool_default()`. */
FZ_FUNCTION int ll_pdf_dict_get_bool_default(::pdf_obj *dict, ::pdf_obj *key, int def);

/** Low-level wrapper for `::pdf_dict_get_date()`. */
FZ_FUNCTION int64_t ll_pdf_dict_get_date(::pdf_obj *dict, ::pdf_obj *key);

/** Low-level wrapper for `::pdf_dict_get_inheritable()`. */
FZ_FUNCTION ::pdf_obj *ll_pdf_dict_get_inheritable(::pdf_obj *dict, ::pdf_obj *key);

/** Low-level wrapper for `::pdf_dict_get_inheritable_bool()`. */
FZ_FUNCTION int ll_pdf_dict_get_inheritable_bool(::pdf_obj *dict, ::pdf_obj *key);

/** Low-level wrapper for `::pdf_dict_get_inheritable_date()`. */
FZ_FUNCTION int64_t ll_pdf_dict_get_inheritable_date(::pdf_obj *dict, ::pdf_obj *key);

/** Low-level wrapper for `::pdf_dict_get_inheritable_int()`. */
FZ_FUNCTION int ll_pdf_dict_get_inheritable_int(::pdf_obj *dict, ::pdf_obj *key);

/** Low-level wrapper for `::pdf_dict_get_inheritable_int64()`. */
FZ_FUNCTION int64_t ll_pdf_dict_get_inheritable_int64(::pdf_obj *dict, ::pdf_obj *key);

/** Low-level wrapper for `::pdf_dict_get_inheritable_matrix()`. */
FZ_FUNCTION ::fz_matrix ll_pdf_dict_get_inheritable_matrix(::pdf_obj *dict, ::pdf_obj *key);

/** Low-level wrapper for `::pdf_dict_get_inheritable_name()`. */
FZ_FUNCTION const char *ll_pdf_dict_get_inheritable_name(::pdf_obj *dict, ::pdf_obj *key);

/** Low-level wrapper for `::pdf_dict_get_inheritable_real()`. */
FZ_FUNCTION float ll_pdf_dict_get_inheritable_real(::pdf_obj *dict, ::pdf_obj *key);

/** Low-level wrapper for `::pdf_dict_get_inheritable_rect()`. */
FZ_FUNCTION ::fz_rect ll_pdf_dict_get_inheritable_rect(::pdf_obj *dict, ::pdf_obj *key);

/** Low-level wrapper for `::pdf_dict_get_inheritable_string()`.

This function has out-params. Python/C# wrappers look like:
	`ll_pdf_dict_get_inheritable_string(::pdf_obj *dict, ::pdf_obj *key)` => `(const char *, size_t sizep)`
*/
FZ_FUNCTION const char *ll_pdf_dict_get_inheritable_string(::pdf_obj *dict, ::pdf_obj *key, size_t *sizep);

/** Low-level wrapper for `::pdf_dict_get_inheritable_text_string()`. */
FZ_FUNCTION const char *ll_pdf_dict_get_inheritable_text_string(::pdf_obj *dict, ::pdf_obj *key);

/** Low-level wrapper for `::pdf_dict_get_int()`. */
FZ_FUNCTION int ll_pdf_dict_get_int(::pdf_obj *dict, ::pdf_obj *key);

/** Low-level wrapper for `::pdf_dict_get_int64()`. */
FZ_FUNCTION int64_t ll_pdf_dict_get_int64(::pdf_obj *dict, ::pdf_obj *key);

/** Low-level wrapper for `::pdf_dict_get_int_default()`. */
FZ_FUNCTION int ll_pdf_dict_get_int_default(::pdf_obj *dict, ::pdf_obj *key, int def);

/** Low-level wrapper for `::pdf_dict_get_key()`. */
FZ_FUNCTION ::pdf_obj *ll_pdf_dict_get_key(::pdf_obj *dict, int idx);

/** Low-level wrapper for `::pdf_dict_get_matrix()`. */
FZ_FUNCTION ::fz_matrix ll_pdf_dict_get_matrix(::pdf_obj *dict, ::pdf_obj *key);

/** Low-level wrapper for `::pdf_dict_get_name()`. */
FZ_FUNCTION const char *ll_pdf_dict_get_name(::pdf_obj *dict, ::pdf_obj *key);

/** Low-level wrapper for `::pdf_dict_get_put_drop()`.

This function has out-params. Python/C# wrappers look like:
	`ll_pdf_dict_get_put_drop(::pdf_obj *dict, ::pdf_obj *key, ::pdf_obj *val, ::pdf_obj **old_val)` => 
*/
FZ_FUNCTION void ll_pdf_dict_get_put_drop(::pdf_obj *dict, ::pdf_obj *key, ::pdf_obj *val, ::pdf_obj **old_val);

/** Low-level wrapper for `::pdf_dict_get_real()`. */
FZ_FUNCTION float ll_pdf_dict_get_real(::pdf_obj *dict, ::pdf_obj *key);

/** Low-level wrapper for `::pdf_dict_get_real_default()`. */
FZ_FUNCTION float ll_pdf_dict_get_real_default(::pdf_obj *dict, ::pdf_obj *key, float def);

/** Low-level wrapper for `::pdf_dict_get_rect()`. */
FZ_FUNCTION ::fz_rect ll_pdf_dict_get_rect(::pdf_obj *dict, ::pdf_obj *key);

/** Low-level wrapper for `::pdf_dict_get_string()`.

This function has out-params. Python/C# wrappers look like:
	`ll_pdf_dict_get_string(::pdf_obj *dict, ::pdf_obj *key)` => `(const char *, size_t sizep)`
*/
FZ_FUNCTION const char *ll_pdf_dict_get_string(::pdf_obj *dict, ::pdf_obj *key, size_t *sizep);

/** Low-level wrapper for `::pdf_dict_get_text_string()`. */
FZ_FUNCTION const char *ll_pdf_dict_get_text_string(::pdf_obj *dict, ::pdf_obj *key);

/** Low-level wrapper for `::pdf_dict_get_val()`. */
FZ_FUNCTION ::pdf_obj *ll_pdf_dict_get_val(::pdf_obj *dict, int idx);

/** Low-level wrapper for `::pdf_dict_geta()`. */
FZ_FUNCTION ::pdf_obj *ll_pdf_dict_geta(::pdf_obj *dict, ::pdf_obj *key, ::pdf_obj *abbrev);

/** Low-level wrapper for `::pdf_dict_getp()`. */
FZ_FUNCTION ::pdf_obj *ll_pdf_dict_getp(::pdf_obj *dict, const char *path);

/** Low-level wrapper for `::pdf_dict_getp_inheritable()`. */
FZ_FUNCTION ::pdf_obj *ll_pdf_dict_getp_inheritable(::pdf_obj *dict, const char *path);

/** Low-level wrapper for `::pdf_dict_gets()`. */
FZ_FUNCTION ::pdf_obj *ll_pdf_dict_gets(::pdf_obj *dict, const char *key);

/** Low-level wrapper for `::pdf_dict_gets_inheritable()`. */
FZ_FUNCTION ::pdf_obj *ll_pdf_dict_gets_inheritable(::pdf_obj *dict, const char *key);

/** Low-level wrapper for `::pdf_dict_getsa()`. */
FZ_FUNCTION ::pdf_obj *ll_pdf_dict_getsa(::pdf_obj *dict, const char *key, const char *abbrev);

/** Low-level wrapper for `::pdf_dict_len()`. */
FZ_FUNCTION int ll_pdf_dict_len(::pdf_obj *dict);

/** Low-level wrapper for `::pdf_dict_put()`. */
FZ_FUNCTION void ll_pdf_dict_put(::pdf_obj *dict, ::pdf_obj *key, ::pdf_obj *val);

/** Low-level wrapper for `::pdf_dict_put_array()`. */
FZ_FUNCTION ::pdf_obj *ll_pdf_dict_put_array(::pdf_obj *dict, ::pdf_obj *key, int initial);

/** Low-level wrapper for `::pdf_dict_put_bool()`. */
FZ_FUNCTION void ll_pdf_dict_put_bool(::pdf_obj *dict, ::pdf_obj *key, int x);

/** Low-level wrapper for `::pdf_dict_put_date()`. */
FZ_FUNCTION void ll_pdf_dict_put_date(::pdf_obj *dict, ::pdf_obj *key, int64_t time);

/** Low-level wrapper for `::pdf_dict_put_dict()`. */
FZ_FUNCTION ::pdf_obj *ll_pdf_dict_put_dict(::pdf_obj *dict, ::pdf_obj *key, int initial);

/** Low-level wrapper for `::pdf_dict_put_drop()`. */
FZ_FUNCTION void ll_pdf_dict_put_drop(::pdf_obj *dict, ::pdf_obj *key, ::pdf_obj *val);

/** Low-level wrapper for `::pdf_dict_put_int()`. */
FZ_FUNCTION void ll_pdf_dict_put_int(::pdf_obj *dict, ::pdf_obj *key, int64_t x);

/** Low-level wrapper for `::pdf_dict_put_matrix()`. */
FZ_FUNCTION void ll_pdf_dict_put_matrix(::pdf_obj *dict, ::pdf_obj *key, ::fz_matrix x);

/** Low-level wrapper for `::pdf_dict_put_name()`. */
FZ_FUNCTION void ll_pdf_dict_put_name(::pdf_obj *dict, ::pdf_obj *key, const char *x);

/** Low-level wrapper for `::pdf_dict_put_real()`. */
FZ_FUNCTION void ll_pdf_dict_put_real(::pdf_obj *dict, ::pdf_obj *key, double x);

/** Low-level wrapper for `::pdf_dict_put_rect()`. */
FZ_FUNCTION void ll_pdf_dict_put_rect(::pdf_obj *dict, ::pdf_obj *key, ::fz_rect x);

/** Low-level wrapper for `::pdf_dict_put_string()`. */
FZ_FUNCTION void ll_pdf_dict_put_string(::pdf_obj *dict, ::pdf_obj *key, const char *x, size_t n);

/** Low-level wrapper for `::pdf_dict_put_text_string()`. */
FZ_FUNCTION void ll_pdf_dict_put_text_string(::pdf_obj *dict, ::pdf_obj *key, const char *x);

/** Low-level wrapper for `::pdf_dict_put_val_null()`. */
FZ_FUNCTION void ll_pdf_dict_put_val_null(::pdf_obj *obj, int idx);

/** Low-level wrapper for `::pdf_dict_putp()`. */
FZ_FUNCTION void ll_pdf_dict_putp(::pdf_obj *dict, const char *path, ::pdf_obj *val);

/** Low-level wrapper for `::pdf_dict_putp_drop()`. */
FZ_FUNCTION void ll_pdf_dict_putp_drop(::pdf_obj *dict, const char *path, ::pdf_obj *val);

/** Low-level wrapper for `::pdf_dict_puts()`. */
FZ_FUNCTION void ll_pdf_dict_puts(::pdf_obj *dict, const char *key, ::pdf_obj *val);

/** Low-level wrapper for `::pdf_dict_puts_dict()`. */
FZ_FUNCTION ::pdf_obj *ll_pdf_dict_puts_dict(::pdf_obj *dict, const char *key, int initial);

/** Low-level wrapper for `::pdf_dict_puts_drop()`. */
FZ_FUNCTION void ll_pdf_dict_puts_drop(::pdf_obj *dict, const char *key, ::pdf_obj *val);

/** Low-level wrapper for `::pdf_dirty_annot()`. */
FZ_FUNCTION void ll_pdf_dirty_annot(::pdf_annot *annot);

/** Low-level wrapper for `::pdf_dirty_obj()`. */
FZ_FUNCTION void ll_pdf_dirty_obj(::pdf_obj *obj);

/** Low-level wrapper for `::pdf_disable_js()`. */
FZ_FUNCTION void ll_pdf_disable_js(::pdf_document *doc);

/** Low-level wrapper for `::pdf_discard_journal()`. */
FZ_FUNCTION void ll_pdf_discard_journal(::pdf_journal *journal);

/** Low-level wrapper for `::pdf_doc_was_linearized()`. */
FZ_FUNCTION int ll_pdf_doc_was_linearized(::pdf_document *doc);

/** Low-level wrapper for `::pdf_document_event_did_print()`. */
FZ_FUNCTION void ll_pdf_document_event_did_print(::pdf_document *doc);

/** Low-level wrapper for `::pdf_document_event_did_save()`. */
FZ_FUNCTION void ll_pdf_document_event_did_save(::pdf_document *doc);

/** Low-level wrapper for `::pdf_document_event_will_close()`. */
FZ_FUNCTION void ll_pdf_document_event_will_close(::pdf_document *doc);

/** Low-level wrapper for `::pdf_document_event_will_print()`. */
FZ_FUNCTION void ll_pdf_document_event_will_print(::pdf_document *doc);

/** Low-level wrapper for `::pdf_document_event_will_save()`. */
FZ_FUNCTION void ll_pdf_document_event_will_save(::pdf_document *doc);

/** Low-level wrapper for `::pdf_document_from_fz_document()`. */
FZ_FUNCTION ::pdf_document *ll_pdf_document_from_fz_document(::fz_document *ptr);

/** Low-level wrapper for `::pdf_document_language()`. */
FZ_FUNCTION ::fz_text_language ll_pdf_document_language(::pdf_document *doc);

/** Low-level wrapper for `::pdf_document_output_intent()`. */
FZ_FUNCTION ::fz_colorspace *ll_pdf_document_output_intent(::pdf_document *doc);

/** Low-level wrapper for `::pdf_document_permissions()`. */
FZ_FUNCTION int ll_pdf_document_permissions(::pdf_document *doc);

/** Low-level wrapper for `::pdf_drop_annot()`. */
FZ_FUNCTION void ll_pdf_drop_annot(::pdf_annot *annot);

/** Low-level wrapper for `::pdf_drop_cmap()`. */
FZ_FUNCTION void ll_pdf_drop_cmap(::pdf_cmap *cmap);

/** Low-level wrapper for `::pdf_drop_cmap_imp()`. */
FZ_FUNCTION void ll_pdf_drop_cmap_imp(::fz_storable *cmap);

/** Low-level wrapper for `::pdf_drop_crypt()`. */
FZ_FUNCTION void ll_pdf_drop_crypt(::pdf_crypt *crypt);

/** Low-level wrapper for `::pdf_drop_document()`. */
FZ_FUNCTION void ll_pdf_drop_document(::pdf_document *doc);

/** Low-level wrapper for `::pdf_drop_font()`. */
FZ_FUNCTION void ll_pdf_drop_font(::pdf_font_desc *font);

/** Low-level wrapper for `::pdf_drop_function()`. */
FZ_FUNCTION void ll_pdf_drop_function(::pdf_function *func);

/** Low-level wrapper for `::pdf_drop_graft_map()`. */
FZ_FUNCTION void ll_pdf_drop_graft_map(::pdf_graft_map *map);

/** Low-level wrapper for `::pdf_drop_journal()`. */
FZ_FUNCTION void ll_pdf_drop_journal(::pdf_journal *journal);

/** Low-level wrapper for `::pdf_drop_js()`. */
FZ_FUNCTION void ll_pdf_drop_js(::pdf_js *js);

/** Low-level wrapper for `::pdf_drop_local_xref()`. */
FZ_FUNCTION void ll_pdf_drop_local_xref(::pdf_xref *xref);

/** Low-level wrapper for `::pdf_drop_local_xref_and_resources()`. */
FZ_FUNCTION void ll_pdf_drop_local_xref_and_resources(::pdf_document *doc);

/** Low-level wrapper for `::pdf_drop_locked_fields()`. */
FZ_FUNCTION void ll_pdf_drop_locked_fields(::pdf_locked_fields *locked);

/** Low-level wrapper for `::pdf_drop_mark_bits()`. */
FZ_FUNCTION void ll_pdf_drop_mark_bits(::pdf_mark_bits *marks);

/** Low-level wrapper for `::pdf_drop_obj()`. */
FZ_FUNCTION void ll_pdf_drop_obj(::pdf_obj *obj);

/** Low-level wrapper for `::pdf_drop_ocg()`. */
FZ_FUNCTION void ll_pdf_drop_ocg(::pdf_document *doc);

/** Low-level wrapper for `::pdf_drop_page()`. */
FZ_FUNCTION void ll_pdf_drop_page(::pdf_page *page);

/** Low-level wrapper for `::pdf_drop_page_tree()`. */
FZ_FUNCTION void ll_pdf_drop_page_tree(::pdf_document *doc);

/** Low-level wrapper for `::pdf_drop_page_tree_internal()`. */
FZ_FUNCTION void ll_pdf_drop_page_tree_internal(::pdf_document *doc);

/** Low-level wrapper for `::pdf_drop_pattern()`. */
FZ_FUNCTION void ll_pdf_drop_pattern(::pdf_pattern *pat);

/** Low-level wrapper for `::pdf_drop_processor()`. */
FZ_FUNCTION void ll_pdf_drop_processor(::pdf_processor *proc);

/** Low-level wrapper for `::pdf_drop_resource_tables()`. */
FZ_FUNCTION void ll_pdf_drop_resource_tables(::pdf_document *doc);

/** Low-level wrapper for `::pdf_drop_signer()`. */
FZ_FUNCTION void ll_pdf_drop_signer(::pdf_pkcs7_signer *signer);

/** Low-level wrapper for `::pdf_drop_singleton_obj()`. */
FZ_FUNCTION ::pdf_obj *ll_pdf_drop_singleton_obj(::pdf_obj *obj);

/** Low-level wrapper for `::pdf_drop_verifier()`. */
FZ_FUNCTION void ll_pdf_drop_verifier(::pdf_pkcs7_verifier *verifier);

/** Low-level wrapper for `::pdf_drop_widget()`. */
FZ_FUNCTION void ll_pdf_drop_widget(::pdf_annot *widget);

/** Low-level wrapper for `::pdf_edit_text_field_value()`.

This function has out-params. Python/C# wrappers look like:
	`ll_pdf_edit_text_field_value(::pdf_annot *widget, const char *value, const char *change)` => `(int, int selStart, int selEnd, char *newvalue)`
*/
FZ_FUNCTION int ll_pdf_edit_text_field_value(::pdf_annot *widget, const char *value, const char *change, int *selStart, int *selEnd, char **newvalue);

/** Low-level wrapper for `::pdf_empty_store()`. */
FZ_FUNCTION void ll_pdf_empty_store(::pdf_document *doc);

/** Low-level wrapper for `::pdf_enable_journal()`. */
FZ_FUNCTION void ll_pdf_enable_journal(::pdf_document *doc);

/** Low-level wrapper for `::pdf_enable_js()`. */
FZ_FUNCTION void ll_pdf_enable_js(::pdf_document *doc);

/** Low-level wrapper for `::pdf_enable_layer()`. */
FZ_FUNCTION void ll_pdf_enable_layer(::pdf_document *doc, int layer, int enabled);

/** Low-level wrapper for `::pdf_encrypt_data()`. */
FZ_FUNCTION void ll_pdf_encrypt_data(::pdf_crypt *crypt, int num, int gen, void (*fmt_str_out)(::fz_context *, void *, const unsigned char *, size_t ), void *arg, const unsigned char *s, size_t n);

/** Low-level wrapper for `::pdf_encrypted_len()`. */
FZ_FUNCTION size_t ll_pdf_encrypted_len(::pdf_crypt *crypt, int num, int gen, size_t len);

/** Low-level wrapper for `::pdf_end_hmtx()`. */
FZ_FUNCTION void ll_pdf_end_hmtx(::pdf_font_desc *font);

/** Low-level wrapper for `::pdf_end_operation()`. */
FZ_FUNCTION void ll_pdf_end_operation(::pdf_document *doc);

/** Low-level wrapper for `::pdf_end_vmtx()`. */
FZ_FUNCTION void ll_pdf_end_vmtx(::pdf_font_desc *font);

/** Low-level wrapper for `::pdf_ensure_solid_xref()`. */
FZ_FUNCTION void ll_pdf_ensure_solid_xref(::pdf_document *doc, int num);

/** Low-level wrapper for `::pdf_eval_function()`.

This function has out-params. Python/C# wrappers look like:
	`ll_pdf_eval_function(::pdf_function *func, const float *in, int inlen, int outlen)` => float out
*/
FZ_FUNCTION void ll_pdf_eval_function(::pdf_function *func, const float *in, int inlen, float *out, int outlen);

/** Low-level wrapper for `::pdf_event_issue_alert()`. */
FZ_FUNCTION void ll_pdf_event_issue_alert(::pdf_document *doc, ::pdf_alert_event *evt);

/** Low-level wrapper for `::pdf_event_issue_exec_menu_item()`. */
FZ_FUNCTION void ll_pdf_event_issue_exec_menu_item(::pdf_document *doc, const char *item);

/** Low-level wrapper for `::pdf_event_issue_launch_url()`. */
FZ_FUNCTION void ll_pdf_event_issue_launch_url(::pdf_document *doc, const char *url, int new_frame);

/** Low-level wrapper for `::pdf_event_issue_mail_doc()`. */
FZ_FUNCTION void ll_pdf_event_issue_mail_doc(::pdf_document *doc, ::pdf_mail_doc_event *evt);

/** Low-level wrapper for `::pdf_event_issue_print()`. */
FZ_FUNCTION void ll_pdf_event_issue_print(::pdf_document *doc);

/** Low-level wrapper for `::pdf_field_border_style()`. */
FZ_FUNCTION char *ll_pdf_field_border_style(::pdf_obj *field);

/** Low-level wrapper for `::pdf_field_display()`. */
FZ_FUNCTION int ll_pdf_field_display(::pdf_obj *field);

/** Low-level wrapper for `::pdf_field_event_calculate()`. */
FZ_FUNCTION void ll_pdf_field_event_calculate(::pdf_document *doc, ::pdf_obj *field);

/** Low-level wrapper for `::pdf_field_event_format()`. */
FZ_FUNCTION char *ll_pdf_field_event_format(::pdf_document *doc, ::pdf_obj *field);

/** Low-level wrapper for `::pdf_field_event_keystroke()`. */
FZ_FUNCTION int ll_pdf_field_event_keystroke(::pdf_document *doc, ::pdf_obj *field, ::pdf_keystroke_event *evt);

/** Low-level wrapper for `::pdf_field_event_validate()`.

This function has out-params. Python/C# wrappers look like:
	`ll_pdf_field_event_validate(::pdf_document *doc, ::pdf_obj *field, const char *value)` => `(int, char *newvalue)`
*/
FZ_FUNCTION int ll_pdf_field_event_validate(::pdf_document *doc, ::pdf_obj *field, const char *value, char **newvalue);

/** Low-level wrapper for `::pdf_field_flags()`. */
FZ_FUNCTION int ll_pdf_field_flags(::pdf_obj *field);

/** Low-level wrapper for `::pdf_field_label()`. */
FZ_FUNCTION const char *ll_pdf_field_label(::pdf_obj *field);

/** Low-level wrapper for `::pdf_field_reset()`. */
FZ_FUNCTION void ll_pdf_field_reset(::pdf_document *doc, ::pdf_obj *field);

/** Low-level wrapper for `::pdf_field_set_border_style()`. */
FZ_FUNCTION void ll_pdf_field_set_border_style(::pdf_obj *field, const char *text);

/** Low-level wrapper for `::pdf_field_set_button_caption()`. */
FZ_FUNCTION void ll_pdf_field_set_button_caption(::pdf_obj *field, const char *text);

/** Low-level wrapper for `::pdf_field_set_display()`. */
FZ_FUNCTION void ll_pdf_field_set_display(::pdf_obj *field, int d);

/** Low-level wrapper for `::pdf_field_set_fill_color()`. */
FZ_FUNCTION void ll_pdf_field_set_fill_color(::pdf_obj *field, ::pdf_obj *col);

/** Low-level wrapper for `::pdf_field_set_text_color()`. */
FZ_FUNCTION void ll_pdf_field_set_text_color(::pdf_obj *field, ::pdf_obj *col);

/** Low-level wrapper for `::pdf_field_type()`. */
FZ_FUNCTION int ll_pdf_field_type(::pdf_obj *field);

/** Low-level wrapper for `::pdf_field_type_string()`. */
FZ_FUNCTION const char *ll_pdf_field_type_string(::pdf_obj *field);

/** Low-level wrapper for `::pdf_field_value()`. */
FZ_FUNCTION const char *ll_pdf_field_value(::pdf_obj *field);

/** Low-level wrapper for `::pdf_filter_annot_contents()`. */
FZ_FUNCTION void ll_pdf_filter_annot_contents(::pdf_document *doc, ::pdf_annot *annot, ::pdf_filter_options *options);

/** Low-level wrapper for `::pdf_filter_page_contents()`. */
FZ_FUNCTION void ll_pdf_filter_page_contents(::pdf_document *doc, ::pdf_page *page, ::pdf_filter_options *options);

/** Low-level wrapper for `::pdf_filter_xobject_instance()`. */
FZ_FUNCTION ::pdf_obj *ll_pdf_filter_xobject_instance(::pdf_obj *old_xobj, ::pdf_obj *page_res, ::fz_matrix ctm, ::pdf_filter_options *options, ::pdf_cycle_list *cycle_up);

/** Low-level wrapper for `::pdf_find_font_resource()`. */
FZ_FUNCTION ::pdf_obj *ll_pdf_find_font_resource(::pdf_document *doc, int type, int encoding, ::fz_font *item, ::pdf_font_resource_key *key);

/** Low-level wrapper for `::pdf_find_item()`. */
FZ_FUNCTION void *ll_pdf_find_item(::fz_store_drop_fn *drop, ::pdf_obj *key);

/** Low-level wrapper for `::pdf_find_locked_fields()`. */
FZ_FUNCTION ::pdf_locked_fields *ll_pdf_find_locked_fields(::pdf_document *doc, int version);

/** Low-level wrapper for `::pdf_find_locked_fields_for_sig()`. */
FZ_FUNCTION ::pdf_locked_fields *ll_pdf_find_locked_fields_for_sig(::pdf_document *doc, ::pdf_obj *sig);

/** Low-level wrapper for `::pdf_find_version_for_obj()`. */
FZ_FUNCTION int ll_pdf_find_version_for_obj(::pdf_document *doc, ::pdf_obj *obj);

/** Low-level wrapper for `::pdf_first_annot()`. */
FZ_FUNCTION ::pdf_annot *ll_pdf_first_annot(::pdf_page *page);

/** Low-level wrapper for `::pdf_first_widget()`. */
FZ_FUNCTION ::pdf_annot *ll_pdf_first_widget(::pdf_page *page);

/** Low-level wrapper for `::pdf_flatten_inheritable_page_items()`. */
FZ_FUNCTION void ll_pdf_flatten_inheritable_page_items(::pdf_obj *page);

/** Low-level wrapper for `::pdf_font_cid_to_gid()`. */
FZ_FUNCTION int ll_pdf_font_cid_to_gid(::pdf_font_desc *fontdesc, int cid);

/** Low-level wrapper for `::pdf_font_writing_supported()`. */
FZ_FUNCTION int ll_pdf_font_writing_supported(::fz_font *font);

/** Low-level wrapper for `::pdf_forget_xref()`. */
FZ_FUNCTION void ll_pdf_forget_xref(::pdf_document *doc);

/** Low-level wrapper for `::pdf_format_date()`. */
FZ_FUNCTION char *ll_pdf_format_date(int64_t time, char *s, size_t n);

/** Low-level wrapper for `::pdf_format_write_options()`. */
FZ_FUNCTION char *ll_pdf_format_write_options(char *buffer, size_t buffer_len, const ::pdf_write_options *opts);

/** Low-level wrapper for `::pdf_function_size()`. */
FZ_FUNCTION size_t ll_pdf_function_size(::pdf_function *func);

/** Low-level wrapper for `::pdf_get_bound_document()`. */
FZ_FUNCTION ::pdf_document *ll_pdf_get_bound_document(::pdf_obj *obj);

/** Low-level wrapper for `::pdf_get_doc_event_callback_data()`. */
FZ_FUNCTION void *ll_pdf_get_doc_event_callback_data(::pdf_document *doc);

/** Low-level wrapper for `::pdf_get_embedded_file_params()`. */
FZ_FUNCTION void ll_pdf_get_embedded_file_params(::pdf_obj *fs, ::pdf_embedded_file_params *out);

/** Low-level wrapper for `::pdf_get_incremental_xref_entry()`. */
FZ_FUNCTION ::pdf_xref_entry *ll_pdf_get_incremental_xref_entry(::pdf_document *doc, int i);

/** Low-level wrapper for `::pdf_get_indirect_document()`. */
FZ_FUNCTION ::pdf_document *ll_pdf_get_indirect_document(::pdf_obj *obj);

/** Low-level wrapper for `::pdf_get_populating_xref_entry()`. */
FZ_FUNCTION ::pdf_xref_entry *ll_pdf_get_populating_xref_entry(::pdf_document *doc, int i);

/** Low-level wrapper for `::pdf_get_widget_editing_state()`. */
FZ_FUNCTION int ll_pdf_get_widget_editing_state(::pdf_annot *widget);

/** Low-level wrapper for `::pdf_get_xref_entry()`. */
FZ_FUNCTION ::pdf_xref_entry *ll_pdf_get_xref_entry(::pdf_document *doc, int i);

/** Low-level wrapper for `::pdf_get_xref_entry_no_change()`. */
FZ_FUNCTION ::pdf_xref_entry *ll_pdf_get_xref_entry_no_change(::pdf_document *doc, int i);

/** Low-level wrapper for `::pdf_get_xref_entry_no_null()`. */
FZ_FUNCTION ::pdf_xref_entry *ll_pdf_get_xref_entry_no_null(::pdf_document *doc, int i);

/** Low-level wrapper for `::pdf_graft_mapped_object()`. */
FZ_FUNCTION ::pdf_obj *ll_pdf_graft_mapped_object(::pdf_graft_map *map, ::pdf_obj *obj);

/** Low-level wrapper for `::pdf_graft_mapped_page()`. */
FZ_FUNCTION void ll_pdf_graft_mapped_page(::pdf_graft_map *map, int page_to, ::pdf_document *src, int page_from);

/** Low-level wrapper for `::pdf_graft_object()`. */
FZ_FUNCTION ::pdf_obj *ll_pdf_graft_object(::pdf_document *dst, ::pdf_obj *obj);

/** Low-level wrapper for `::pdf_graft_page()`. */
FZ_FUNCTION void ll_pdf_graft_page(::pdf_document *dst, int page_to, ::pdf_document *src, int page_from);

/** Low-level wrapper for `::pdf_has_permission()`. */
FZ_FUNCTION int ll_pdf_has_permission(::pdf_document *doc, ::fz_permission p);

/** Low-level wrapper for `::pdf_has_unsaved_changes()`. */
FZ_FUNCTION int ll_pdf_has_unsaved_changes(::pdf_document *doc);

/** Low-level wrapper for `::pdf_has_unsaved_sigs()`. */
FZ_FUNCTION int ll_pdf_has_unsaved_sigs(::pdf_document *doc);

/** Low-level wrapper for `::pdf_insert_font_resource()`. */
FZ_FUNCTION ::pdf_obj *ll_pdf_insert_font_resource(::pdf_document *doc, ::pdf_font_resource_key *key, ::pdf_obj *obj);

/** Low-level wrapper for `::pdf_insert_page()`. */
FZ_FUNCTION void ll_pdf_insert_page(::pdf_document *doc, int at, ::pdf_obj *page);

/** Low-level wrapper for `::pdf_intent_from_name()`. */
FZ_FUNCTION enum pdf_intent ll_pdf_intent_from_name(::pdf_obj *obj);

/** Low-level wrapper for `::pdf_intent_from_string()`. */
FZ_FUNCTION enum pdf_intent ll_pdf_intent_from_string(const char *str);

/** Low-level wrapper for `::pdf_invalidate_xfa()`. */
FZ_FUNCTION void ll_pdf_invalidate_xfa(::pdf_document *doc);

/** Low-level wrapper for `::pdf_is_array()`. */
FZ_FUNCTION int ll_pdf_is_array(::pdf_obj *obj);

/** Low-level wrapper for `::pdf_is_bool()`. */
FZ_FUNCTION int ll_pdf_is_bool(::pdf_obj *obj);

/** Low-level wrapper for `::pdf_is_dict()`. */
FZ_FUNCTION int ll_pdf_is_dict(::pdf_obj *obj);

/** Low-level wrapper for `::pdf_is_embedded_file()`. */
FZ_FUNCTION int ll_pdf_is_embedded_file(::pdf_obj *fs);

/** Low-level wrapper for `::pdf_is_field_locked()`. */
FZ_FUNCTION int ll_pdf_is_field_locked(::pdf_locked_fields *locked, const char *name);

/** Low-level wrapper for `::pdf_is_indirect()`. */
FZ_FUNCTION int ll_pdf_is_indirect(::pdf_obj *obj);

/** Low-level wrapper for `::pdf_is_int()`. */
FZ_FUNCTION int ll_pdf_is_int(::pdf_obj *obj);

/** Low-level wrapper for `::pdf_is_jpx_image()`. */
FZ_FUNCTION int ll_pdf_is_jpx_image(::pdf_obj *dict);

/** Low-level wrapper for `::pdf_is_local_object()`. */
FZ_FUNCTION int ll_pdf_is_local_object(::pdf_document *doc, ::pdf_obj *obj);

/** Low-level wrapper for `::pdf_is_name()`. */
FZ_FUNCTION int ll_pdf_is_name(::pdf_obj *obj);

/** Low-level wrapper for `::pdf_is_null()`. */
FZ_FUNCTION int ll_pdf_is_null(::pdf_obj *obj);

/** Low-level wrapper for `::pdf_is_number()`. */
FZ_FUNCTION int ll_pdf_is_number(::pdf_obj *obj);

/** Low-level wrapper for `::pdf_is_ocg_hidden()`. */
FZ_FUNCTION int ll_pdf_is_ocg_hidden(::pdf_document *doc, ::pdf_obj *rdb, const char *usage, ::pdf_obj *ocg);

/** Low-level wrapper for `::pdf_is_real()`. */
FZ_FUNCTION int ll_pdf_is_real(::pdf_obj *obj);

/** Low-level wrapper for `::pdf_is_stream()`. */
FZ_FUNCTION int ll_pdf_is_stream(::pdf_obj *obj);

/** Low-level wrapper for `::pdf_is_string()`. */
FZ_FUNCTION int ll_pdf_is_string(::pdf_obj *obj);

/** Low-level wrapper for `::pdf_is_tint_colorspace()`. */
FZ_FUNCTION int ll_pdf_is_tint_colorspace(::fz_colorspace *cs);

/** Low-level wrapper for `::pdf_js_event_init()`. */
FZ_FUNCTION void ll_pdf_js_event_init(::pdf_js *js, ::pdf_obj *target, const char *value, int willCommit);

/** Low-level wrapper for `::pdf_js_event_init_keystroke()`. */
FZ_FUNCTION void ll_pdf_js_event_init_keystroke(::pdf_js *js, ::pdf_obj *target, ::pdf_keystroke_event *evt);

/** Low-level wrapper for `::pdf_js_event_result()`. */
FZ_FUNCTION int ll_pdf_js_event_result(::pdf_js *js);

/** Low-level wrapper for `::pdf_js_event_result_keystroke()`. */
FZ_FUNCTION int ll_pdf_js_event_result_keystroke(::pdf_js *js, ::pdf_keystroke_event *evt);

/** Low-level wrapper for `::pdf_js_event_result_validate()`.

This function has out-params. Python/C# wrappers look like:
	`ll_pdf_js_event_result_validate(::pdf_js *js)` => `(int, char *newvalue)`
*/
FZ_FUNCTION int ll_pdf_js_event_result_validate(::pdf_js *js, char **newvalue);

/** Low-level wrapper for `::pdf_js_event_value()`. */
FZ_FUNCTION char *ll_pdf_js_event_value(::pdf_js *js);

/** Low-level wrapper for `::pdf_js_execute()`.

This function has out-params. Python/C# wrappers look like:
	`ll_pdf_js_execute(::pdf_js *js, const char *name, const char *code)` => char *result
*/
FZ_FUNCTION void ll_pdf_js_execute(::pdf_js *js, const char *name, const char *code, char **result);

/** Low-level wrapper for `::pdf_js_get_console()`. */
FZ_FUNCTION ::pdf_js_console *ll_pdf_js_get_console(::pdf_document *doc);

/** Low-level wrapper for `::pdf_js_set_console()`. */
FZ_FUNCTION void ll_pdf_js_set_console(::pdf_document *doc, ::pdf_js_console *console, void *user);

/** Low-level wrapper for `::pdf_js_supported()`. */
FZ_FUNCTION int ll_pdf_js_supported(::pdf_document *doc);

/** Low-level wrapper for `::pdf_keep_annot()`. */
FZ_FUNCTION ::pdf_annot *ll_pdf_keep_annot(::pdf_annot *annot);

/** Low-level wrapper for `::pdf_keep_cmap()`. */
FZ_FUNCTION ::pdf_cmap *ll_pdf_keep_cmap(::pdf_cmap *cmap);

/** Low-level wrapper for `::pdf_keep_document()`. */
FZ_FUNCTION ::pdf_document *ll_pdf_keep_document(::pdf_document *doc);

/** Low-level wrapper for `::pdf_keep_font()`. */
FZ_FUNCTION ::pdf_font_desc *ll_pdf_keep_font(::pdf_font_desc *fontdesc);

/** Low-level wrapper for `::pdf_keep_function()`. */
FZ_FUNCTION ::pdf_function *ll_pdf_keep_function(::pdf_function *func);

/** Low-level wrapper for `::pdf_keep_graft_map()`. */
FZ_FUNCTION ::pdf_graft_map *ll_pdf_keep_graft_map(::pdf_graft_map *map);

/** Low-level wrapper for `::pdf_keep_obj()`. */
FZ_FUNCTION ::pdf_obj *ll_pdf_keep_obj(::pdf_obj *obj);

/** Low-level wrapper for `::pdf_keep_page()`. */
FZ_FUNCTION ::pdf_page *ll_pdf_keep_page(::pdf_page *page);

/** Low-level wrapper for `::pdf_keep_pattern()`. */
FZ_FUNCTION ::pdf_pattern *ll_pdf_keep_pattern(::pdf_pattern *pat);

/** Low-level wrapper for `::pdf_keep_processor()`. */
FZ_FUNCTION ::pdf_processor *ll_pdf_keep_processor(::pdf_processor *proc);

/** Low-level wrapper for `::pdf_keep_widget()`. */
FZ_FUNCTION ::pdf_annot *ll_pdf_keep_widget(::pdf_annot *widget);

/** Low-level wrapper for `::pdf_layer_config_info()`. */
FZ_FUNCTION void ll_pdf_layer_config_info(::pdf_document *doc, int config_num, ::pdf_layer_config *info);

/** Low-level wrapper for `::pdf_layer_config_ui_info()`. */
FZ_FUNCTION void ll_pdf_layer_config_ui_info(::pdf_document *doc, int ui, ::pdf_layer_config_ui *info);

/** Low-level wrapper for `::pdf_layer_is_enabled()`. */
FZ_FUNCTION int ll_pdf_layer_is_enabled(::pdf_document *doc, int layer);

/** Low-level wrapper for `::pdf_layer_name()`. */
FZ_FUNCTION const char *ll_pdf_layer_name(::pdf_document *doc, int layer);

/** Low-level wrapper for `::pdf_layout_fit_text()`. */
FZ_FUNCTION ::fz_text *ll_pdf_layout_fit_text(::fz_font *font, ::fz_text_language lang, const char *str, ::fz_rect bounds);

/** Low-level wrapper for `::pdf_layout_text_widget()`. */
FZ_FUNCTION ::fz_layout_block *ll_pdf_layout_text_widget(::pdf_annot *annot);

/** Low-level wrapper for `::pdf_lex()`. */
FZ_FUNCTION ::pdf_token ll_pdf_lex(::fz_stream *f, ::pdf_lexbuf *lexbuf);

/** Low-level wrapper for `::pdf_lex_no_string()`. */
FZ_FUNCTION ::pdf_token ll_pdf_lex_no_string(::fz_stream *f, ::pdf_lexbuf *lexbuf);

/** Low-level wrapper for `::pdf_lexbuf_fin()`. */
FZ_FUNCTION void ll_pdf_lexbuf_fin(::pdf_lexbuf *lexbuf);

/** Low-level wrapper for `::pdf_lexbuf_grow()`. */
FZ_FUNCTION ptrdiff_t ll_pdf_lexbuf_grow(::pdf_lexbuf *lexbuf);

/** Low-level wrapper for `::pdf_lexbuf_init()`. */
FZ_FUNCTION void ll_pdf_lexbuf_init(::pdf_lexbuf *lexbuf, int size);

/** Low-level wrapper for `::pdf_line_ending_from_name()`. */
FZ_FUNCTION enum pdf_line_ending ll_pdf_line_ending_from_name(::pdf_obj *end);

/** Low-level wrapper for `::pdf_line_ending_from_string()`. */
FZ_FUNCTION enum pdf_line_ending ll_pdf_line_ending_from_string(const char *end);

/** Low-level wrapper for `::pdf_load_builtin_cmap()`. */
FZ_FUNCTION ::pdf_cmap *ll_pdf_load_builtin_cmap(const char *name);

/** Low-level wrapper for `::pdf_load_cmap()`. */
FZ_FUNCTION ::pdf_cmap *ll_pdf_load_cmap(::fz_stream *file);

/** Low-level wrapper for `::pdf_load_colorspace()`. */
FZ_FUNCTION ::fz_colorspace *ll_pdf_load_colorspace(::pdf_obj *obj);

/** Low-level wrapper for `::pdf_load_compressed_inline_image()`. */
FZ_FUNCTION void ll_pdf_load_compressed_inline_image(::pdf_document *doc, ::pdf_obj *dict, int length, ::fz_stream *cstm, int indexed, ::fz_compressed_image *image);

/** Low-level wrapper for `::pdf_load_compressed_stream()`. */
FZ_FUNCTION ::fz_compressed_buffer *ll_pdf_load_compressed_stream(::pdf_document *doc, int num, size_t worst_case);

/** Low-level wrapper for `::pdf_load_default_colorspaces()`. */
FZ_FUNCTION ::fz_default_colorspaces *ll_pdf_load_default_colorspaces(::pdf_document *doc, ::pdf_page *page);

/** Low-level wrapper for `::pdf_load_embedded_cmap()`. */
FZ_FUNCTION ::pdf_cmap *ll_pdf_load_embedded_cmap(::pdf_document *doc, ::pdf_obj *ref);

/** Low-level wrapper for `::pdf_load_embedded_file_contents()`. */
FZ_FUNCTION ::fz_buffer *ll_pdf_load_embedded_file_contents(::pdf_obj *fs);

/** Low-level wrapper for `::pdf_load_encoding()`.

This function has out-params. Python/C# wrappers look like:
	`ll_pdf_load_encoding(const char *encoding)` => const char *estrings
*/
FZ_FUNCTION void ll_pdf_load_encoding(const char **estrings, const char *encoding);

/** Low-level wrapper for `::pdf_load_field_name()`. */
FZ_FUNCTION char *ll_pdf_load_field_name(::pdf_obj *field);


/** Alternative to `ll_pdf_load_field_name()` that returns a std::string. */
FZ_FUNCTION std::string ll_pdf_load_field_name2(pdf_obj* field);

/** Low-level wrapper for `::pdf_load_font()`. */
FZ_FUNCTION ::pdf_font_desc *ll_pdf_load_font(::pdf_document *doc, ::pdf_obj *rdb, ::pdf_obj *obj);

/** Low-level wrapper for `::pdf_load_function()`. */
FZ_FUNCTION ::pdf_function *ll_pdf_load_function(::pdf_obj *ref, int in, int out);

/** Low-level wrapper for `::pdf_load_hail_mary_font()`. */
FZ_FUNCTION ::pdf_font_desc *ll_pdf_load_hail_mary_font(::pdf_document *doc);

/** Low-level wrapper for `::pdf_load_image()`. */
FZ_FUNCTION ::fz_image *ll_pdf_load_image(::pdf_document *doc, ::pdf_obj *obj);

/** Low-level wrapper for `::pdf_load_inline_image()`. */
FZ_FUNCTION ::fz_image *ll_pdf_load_inline_image(::pdf_document *doc, ::pdf_obj *rdb, ::pdf_obj *dict, ::fz_stream *file);

/** Low-level wrapper for `::pdf_load_journal()`. */
FZ_FUNCTION void ll_pdf_load_journal(::pdf_document *doc, const char *filename);

/** Low-level wrapper for `::pdf_load_link_annots()`. */
FZ_FUNCTION ::fz_link *ll_pdf_load_link_annots(::pdf_document *arg_1, ::pdf_page *arg_2, ::pdf_obj *annots, int pagenum, ::fz_matrix page_ctm);

/** Low-level wrapper for `::pdf_load_links()`. */
FZ_FUNCTION ::fz_link *ll_pdf_load_links(::pdf_page *page);

/** Low-level wrapper for `::pdf_load_name_tree()`. */
FZ_FUNCTION ::pdf_obj *ll_pdf_load_name_tree(::pdf_document *doc, ::pdf_obj *which);

/** Low-level wrapper for `::pdf_load_object()`. */
/**
	Load a given object.

	This can cause xref reorganisations (solidifications etc) due to
	repairs, so all held pdf_xref_entries should be considered
	invalid after this call (other than the returned one).
*/
FZ_FUNCTION ::pdf_obj *ll_pdf_load_object(::pdf_document *doc, int num);

/** Low-level wrapper for `::pdf_load_outline()`. */
FZ_FUNCTION ::fz_outline *ll_pdf_load_outline(::pdf_document *doc);

/** Low-level wrapper for `::pdf_load_page()`. */
FZ_FUNCTION ::pdf_page *ll_pdf_load_page(::pdf_document *doc, int number);

/** Low-level wrapper for `::pdf_load_page_imp()`. */
FZ_FUNCTION ::fz_page *ll_pdf_load_page_imp(::fz_document *doc, int chapter, int number);

/** Low-level wrapper for `::pdf_load_page_tree()`. */
FZ_FUNCTION void ll_pdf_load_page_tree(::pdf_document *doc);

/** Low-level wrapper for `::pdf_load_pattern()`. */
FZ_FUNCTION ::pdf_pattern *ll_pdf_load_pattern(::pdf_document *doc, ::pdf_obj *obj);

/** Low-level wrapper for `::pdf_load_raw_stream()`. */
FZ_FUNCTION ::fz_buffer *ll_pdf_load_raw_stream(::pdf_obj *ref);

/** Low-level wrapper for `::pdf_load_raw_stream_number()`. */
FZ_FUNCTION ::fz_buffer *ll_pdf_load_raw_stream_number(::pdf_document *doc, int num);

/** Low-level wrapper for `::pdf_load_shading()`. */
FZ_FUNCTION ::fz_shade *ll_pdf_load_shading(::pdf_document *doc, ::pdf_obj *obj);

/** Low-level wrapper for `::pdf_load_stream()`. */
FZ_FUNCTION ::fz_buffer *ll_pdf_load_stream(::pdf_obj *ref);

/** Low-level wrapper for `::pdf_load_stream_number()`. */
FZ_FUNCTION ::fz_buffer *ll_pdf_load_stream_number(::pdf_document *doc, int num);

/** Low-level wrapper for `::pdf_load_stream_or_string_as_utf8()`. */
FZ_FUNCTION char *ll_pdf_load_stream_or_string_as_utf8(::pdf_obj *src);

/** Low-level wrapper for `::pdf_load_system_cmap()`. */
FZ_FUNCTION ::pdf_cmap *ll_pdf_load_system_cmap(const char *name);

/** Low-level wrapper for `::pdf_load_to_unicode()`.

This function has out-params. Python/C# wrappers look like:
	`ll_pdf_load_to_unicode(::pdf_document *doc, ::pdf_font_desc *font, char *collection, ::pdf_obj *cmapstm)` => const char *strings
*/
FZ_FUNCTION void ll_pdf_load_to_unicode(::pdf_document *doc, ::pdf_font_desc *font, const char **strings, char *collection, ::pdf_obj *cmapstm);

/** Low-level wrapper for `::pdf_load_type3_font()`. */
FZ_FUNCTION ::pdf_font_desc *ll_pdf_load_type3_font(::pdf_document *doc, ::pdf_obj *rdb, ::pdf_obj *obj);

/** Low-level wrapper for `::pdf_load_type3_glyphs()`. */
FZ_FUNCTION void ll_pdf_load_type3_glyphs(::pdf_document *doc, ::pdf_font_desc *fontdesc);

/** Low-level wrapper for `::pdf_load_unencrypted_object()`. */
FZ_FUNCTION ::pdf_obj *ll_pdf_load_unencrypted_object(::pdf_document *doc, int num);

/** Low-level wrapper for `::pdf_lookup_cmap()`. */
FZ_FUNCTION int ll_pdf_lookup_cmap(::pdf_cmap *cmap, unsigned int cpt);

/** Low-level wrapper for `::pdf_lookup_cmap_full()`.

This function has out-params. Python/C# wrappers look like:
	`ll_pdf_lookup_cmap_full(::pdf_cmap *cmap, unsigned int cpt)` => `(int, int out)`
*/
FZ_FUNCTION int ll_pdf_lookup_cmap_full(::pdf_cmap *cmap, unsigned int cpt, int *out);

/** Low-level wrapper for `::pdf_lookup_dest()`. */
FZ_FUNCTION ::pdf_obj *ll_pdf_lookup_dest(::pdf_document *doc, ::pdf_obj *needle);

/** Low-level wrapper for `::pdf_lookup_field()`. */
FZ_FUNCTION ::pdf_obj *ll_pdf_lookup_field(::pdf_obj *form, const char *name);

/** Low-level wrapper for `::pdf_lookup_hmtx()`. */
FZ_FUNCTION ::pdf_hmtx ll_pdf_lookup_hmtx(::pdf_font_desc *font, int cid);

/** Low-level wrapper for `::pdf_lookup_metadata()`. */
FZ_FUNCTION int ll_pdf_lookup_metadata(::pdf_document *doc, const char *key, char *ptr, size_t size);

/** Low-level wrapper for `::pdf_lookup_metadata2()`. */
/**
C++ alternative to `pdf_lookup_metadata()` that returns a `std::string`
or calls `fz_throw()` if not found.
*/
FZ_FUNCTION std::string ll_pdf_lookup_metadata2(::pdf_document *doc, const char *key);

/** Low-level wrapper for `::pdf_lookup_name()`. */
FZ_FUNCTION ::pdf_obj *ll_pdf_lookup_name(::pdf_document *doc, ::pdf_obj *which, ::pdf_obj *needle);

/** Low-level wrapper for `::pdf_lookup_number()`. */
FZ_FUNCTION ::pdf_obj *ll_pdf_lookup_number(::pdf_obj *root, int needle);

/** Low-level wrapper for `::pdf_lookup_page_loc()`.

This function has out-params. Python/C# wrappers look like:
	`ll_pdf_lookup_page_loc(::pdf_document *doc, int needle, ::pdf_obj **parentp)` => `(pdf_obj *, int indexp)`
*/
FZ_FUNCTION ::pdf_obj *ll_pdf_lookup_page_loc(::pdf_document *doc, int needle, ::pdf_obj **parentp, int *indexp);

/** Low-level wrapper for `::pdf_lookup_page_number()`. */
FZ_FUNCTION int ll_pdf_lookup_page_number(::pdf_document *doc, ::pdf_obj *pageobj);

/** Low-level wrapper for `::pdf_lookup_page_obj()`. */
FZ_FUNCTION ::pdf_obj *ll_pdf_lookup_page_obj(::pdf_document *doc, int needle);

/** Low-level wrapper for `::pdf_lookup_substitute_font()`.

This function has out-params. Python/C# wrappers look like:
	`ll_pdf_lookup_substitute_font(int mono, int serif, int bold, int italic)` => `(const unsigned char *, int len)`
*/
FZ_FUNCTION const unsigned char *ll_pdf_lookup_substitute_font(int mono, int serif, int bold, int italic, int *len);

/** Low-level wrapper for `::pdf_lookup_vmtx()`. */
FZ_FUNCTION ::pdf_vmtx ll_pdf_lookup_vmtx(::pdf_font_desc *font, int cid);

/** Low-level wrapper for `::pdf_map_one_to_many()`.

This function has out-params. Python/C# wrappers look like:
	`ll_pdf_map_one_to_many(::pdf_cmap *cmap, unsigned int one, size_t len)` => int many
*/
FZ_FUNCTION void ll_pdf_map_one_to_many(::pdf_cmap *cmap, unsigned int one, int *many, size_t len);

/** Low-level wrapper for `::pdf_map_range_to_range()`. */
FZ_FUNCTION void ll_pdf_map_range_to_range(::pdf_cmap *cmap, unsigned int srclo, unsigned int srchi, int dstlo);

/** Low-level wrapper for `::pdf_mark_bits_reset()`. */
FZ_FUNCTION void ll_pdf_mark_bits_reset(::pdf_mark_bits *marks);

/** Low-level wrapper for `::pdf_mark_bits_set()`. */
FZ_FUNCTION int ll_pdf_mark_bits_set(::pdf_mark_bits *marks, ::pdf_obj *obj);

/** Low-level wrapper for `::pdf_mark_list_check()`. */
FZ_FUNCTION int ll_pdf_mark_list_check(::pdf_mark_list *list, ::pdf_obj *obj);

/** Low-level wrapper for `::pdf_mark_list_free()`. */
FZ_FUNCTION void ll_pdf_mark_list_free(::pdf_mark_list *list);

/** Low-level wrapper for `::pdf_mark_list_init()`. */
FZ_FUNCTION void ll_pdf_mark_list_init(::pdf_mark_list *list);

/** Low-level wrapper for `::pdf_mark_list_pop()`. */
FZ_FUNCTION void ll_pdf_mark_list_pop(::pdf_mark_list *list);

/** Low-level wrapper for `::pdf_mark_list_push()`. */
FZ_FUNCTION int ll_pdf_mark_list_push(::pdf_mark_list *list, ::pdf_obj *obj);

/** Low-level wrapper for `::pdf_mark_obj()`. */
FZ_FUNCTION int ll_pdf_mark_obj(::pdf_obj *obj);

/** Low-level wrapper for `::pdf_mark_xref()`. */
FZ_FUNCTION void ll_pdf_mark_xref(::pdf_document *doc);

/** Low-level wrapper for `::pdf_metadata()`. */
FZ_FUNCTION ::pdf_obj *ll_pdf_metadata(::pdf_document *doc);

/** Low-level wrapper for `::pdf_minimize_document()`. */
FZ_FUNCTION void ll_pdf_minimize_document(::pdf_document *doc);

/** Low-level wrapper for `::pdf_name_eq()`. */
FZ_FUNCTION int ll_pdf_name_eq(::pdf_obj *a, ::pdf_obj *b);

/** Low-level wrapper for `::pdf_name_from_intent()`. */
FZ_FUNCTION ::pdf_obj *ll_pdf_name_from_intent(enum pdf_intent intent);

/** Low-level wrapper for `::pdf_name_from_line_ending()`. */
FZ_FUNCTION ::pdf_obj *ll_pdf_name_from_line_ending(enum pdf_line_ending end);

/** Low-level wrapper for `::pdf_needs_password()`. */
FZ_FUNCTION int ll_pdf_needs_password(::pdf_document *doc);

/** Low-level wrapper for `::pdf_new_action_from_link()`. */
FZ_FUNCTION ::pdf_obj *ll_pdf_new_action_from_link(::pdf_document *doc, const char *uri);

/** Low-level wrapper for `::pdf_new_array()`. */
FZ_FUNCTION ::pdf_obj *ll_pdf_new_array(::pdf_document *doc, int initialcap);

/** Low-level wrapper for `::pdf_new_buffer_processor()`. */
FZ_FUNCTION ::pdf_processor *ll_pdf_new_buffer_processor(::fz_buffer *buffer, int ahxencode, int newlines);

/** Low-level wrapper for `::pdf_new_cmap()`. */
FZ_FUNCTION ::pdf_cmap *ll_pdf_new_cmap();

/** Low-level wrapper for `::pdf_new_color_filter()`. */
FZ_FUNCTION ::pdf_processor *ll_pdf_new_color_filter(::pdf_document *doc, ::pdf_processor *chain, int struct_parents, ::fz_matrix transform, ::pdf_filter_options *options, void *copts);

/** Low-level wrapper for `::pdf_new_crypt()`. */
FZ_FUNCTION ::pdf_crypt *ll_pdf_new_crypt(::pdf_obj *enc, ::pdf_obj *id);

/** Low-level wrapper for `::pdf_new_date()`. */
FZ_FUNCTION ::pdf_obj *ll_pdf_new_date(::pdf_document *doc, int64_t time);

/** Low-level wrapper for `::pdf_new_dest_from_link()`. */
FZ_FUNCTION ::pdf_obj *ll_pdf_new_dest_from_link(::pdf_document *doc, const char *uri, int is_remote);

/** Low-level wrapper for `::pdf_new_dict()`. */
FZ_FUNCTION ::pdf_obj *ll_pdf_new_dict(::pdf_document *doc, int initialcap);

/** Low-level wrapper for `::pdf_new_display_list_from_annot()`. */
FZ_FUNCTION ::fz_display_list *ll_pdf_new_display_list_from_annot(::pdf_annot *annot);

/** Low-level wrapper for `::pdf_new_encrypt()`. */
FZ_FUNCTION ::pdf_crypt *ll_pdf_new_encrypt(const char *opwd_utf8, const char *upwd_utf8, ::pdf_obj *id, int permissions, int algorithm);

/** Low-level wrapper for `::pdf_new_font_desc()`. */
FZ_FUNCTION ::pdf_font_desc *ll_pdf_new_font_desc();

/** Low-level wrapper for `::pdf_new_graft_map()`. */
FZ_FUNCTION ::pdf_graft_map *ll_pdf_new_graft_map(::pdf_document *dst);

/** Low-level wrapper for `::pdf_new_identity_cmap()`. */
FZ_FUNCTION ::pdf_cmap *ll_pdf_new_identity_cmap(int wmode, int bytes);

/** Low-level wrapper for `::pdf_new_indirect()`. */
FZ_FUNCTION ::pdf_obj *ll_pdf_new_indirect(::pdf_document *doc, int num, int gen);

/** Low-level wrapper for `::pdf_new_int()`. */
FZ_FUNCTION ::pdf_obj *ll_pdf_new_int(int64_t i);

/** Low-level wrapper for `::pdf_new_link()`. */
FZ_FUNCTION ::fz_link *ll_pdf_new_link(::pdf_page *page, ::fz_rect rect, const char *uri, ::pdf_obj *obj);

/** Low-level wrapper for `::pdf_new_local_xref()`. */
FZ_FUNCTION ::pdf_xref *ll_pdf_new_local_xref(::pdf_document *doc);

/** Low-level wrapper for `::pdf_new_mark_bits()`. */
FZ_FUNCTION ::pdf_mark_bits *ll_pdf_new_mark_bits(::pdf_document *doc);

/** Low-level wrapper for `::pdf_new_matrix()`. */
FZ_FUNCTION ::pdf_obj *ll_pdf_new_matrix(::pdf_document *doc, ::fz_matrix mtx);

/** Low-level wrapper for `::pdf_new_name()`. */
FZ_FUNCTION ::pdf_obj *ll_pdf_new_name(const char *str);

/** Low-level wrapper for `::pdf_new_outline_iterator()`. */
FZ_FUNCTION ::fz_outline_iterator *ll_pdf_new_outline_iterator(::pdf_document *doc);

/** Low-level wrapper for `::pdf_new_output_processor()`. */
FZ_FUNCTION ::pdf_processor *ll_pdf_new_output_processor(::fz_output *out, int ahxencode, int newlines);

/** Low-level wrapper for `::pdf_new_pdf_device()`. */
FZ_FUNCTION ::fz_device *ll_pdf_new_pdf_device(::pdf_document *doc, ::fz_matrix topctm, ::pdf_obj *resources, ::fz_buffer *contents);

/** Low-level wrapper for `::pdf_new_pixmap_from_annot()`. */
FZ_FUNCTION ::fz_pixmap *ll_pdf_new_pixmap_from_annot(::pdf_annot *annot, ::fz_matrix ctm, ::fz_colorspace *cs, ::fz_separations *seps, int alpha);

/** Low-level wrapper for `::pdf_new_pixmap_from_page_contents_with_separations_and_usage()`. */
FZ_FUNCTION ::fz_pixmap *ll_pdf_new_pixmap_from_page_contents_with_separations_and_usage(::pdf_page *page, ::fz_matrix ctm, ::fz_colorspace *cs, ::fz_separations *seps, int alpha, const char *usage, ::fz_box_type box);

/** Low-level wrapper for `::pdf_new_pixmap_from_page_contents_with_usage()`. */
FZ_FUNCTION ::fz_pixmap *ll_pdf_new_pixmap_from_page_contents_with_usage(::pdf_page *page, ::fz_matrix ctm, ::fz_colorspace *cs, int alpha, const char *usage, ::fz_box_type box);

/** Low-level wrapper for `::pdf_new_pixmap_from_page_with_separations_and_usage()`. */
FZ_FUNCTION ::fz_pixmap *ll_pdf_new_pixmap_from_page_with_separations_and_usage(::pdf_page *page, ::fz_matrix ctm, ::fz_colorspace *cs, ::fz_separations *seps, int alpha, const char *usage, ::fz_box_type box);

/** Low-level wrapper for `::pdf_new_pixmap_from_page_with_usage()`. */
FZ_FUNCTION ::fz_pixmap *ll_pdf_new_pixmap_from_page_with_usage(::pdf_page *page, ::fz_matrix ctm, ::fz_colorspace *cs, int alpha, const char *usage, ::fz_box_type box);

/** Low-level wrapper for `::pdf_new_processor()`. */
FZ_FUNCTION void *ll_pdf_new_processor(int size);

/** Low-level wrapper for `::pdf_new_real()`. */
FZ_FUNCTION ::pdf_obj *ll_pdf_new_real(float f);

/** Low-level wrapper for `::pdf_new_rect()`. */
FZ_FUNCTION ::pdf_obj *ll_pdf_new_rect(::pdf_document *doc, ::fz_rect rect);

/** Low-level wrapper for `::pdf_new_run_processor()`. */
FZ_FUNCTION ::pdf_processor *ll_pdf_new_run_processor(::pdf_document *doc, ::fz_device *dev, ::fz_matrix ctm, int struct_parent, const char *usage, ::pdf_gstate *gstate, ::fz_default_colorspaces *default_cs, ::fz_cookie *cookie);

/** Low-level wrapper for `::pdf_new_sanitize_filter()`. */
FZ_FUNCTION ::pdf_processor *ll_pdf_new_sanitize_filter(::pdf_document *doc, ::pdf_processor *chain, int struct_parents, ::fz_matrix transform, ::pdf_filter_options *options, void *sopts);

/** Low-level wrapper for `::pdf_new_stext_page_from_annot()`. */
FZ_FUNCTION ::fz_stext_page *ll_pdf_new_stext_page_from_annot(::pdf_annot *annot, const ::fz_stext_options *options);

/** Low-level wrapper for `::pdf_new_string()`. */
FZ_FUNCTION ::pdf_obj *ll_pdf_new_string(const char *str, size_t len);

/** Low-level wrapper for `::pdf_new_text_string()`. */
FZ_FUNCTION ::pdf_obj *ll_pdf_new_text_string(const char *s);

/** Low-level wrapper for `::pdf_new_uri_from_explicit_dest()`. */
FZ_FUNCTION char *ll_pdf_new_uri_from_explicit_dest(::fz_link_dest dest);

/** Low-level wrapper for `::pdf_new_uri_from_path_and_explicit_dest()`. */
FZ_FUNCTION char *ll_pdf_new_uri_from_path_and_explicit_dest(const char *path, ::fz_link_dest dest);

/** Low-level wrapper for `::pdf_new_uri_from_path_and_named_dest()`. */
FZ_FUNCTION char *ll_pdf_new_uri_from_path_and_named_dest(const char *path, const char *name);

/** Low-level wrapper for `::pdf_new_utf8_from_pdf_stream_obj()`. */
FZ_FUNCTION char *ll_pdf_new_utf8_from_pdf_stream_obj(::pdf_obj *src);

/** Low-level wrapper for `::pdf_new_utf8_from_pdf_string()`. */
FZ_FUNCTION char *ll_pdf_new_utf8_from_pdf_string(const char *srcptr, size_t srclen);

/** Low-level wrapper for `::pdf_new_utf8_from_pdf_string_obj()`. */
FZ_FUNCTION char *ll_pdf_new_utf8_from_pdf_string_obj(::pdf_obj *src);

/** Low-level wrapper for `::pdf_new_xobject()`. */
FZ_FUNCTION ::pdf_obj *ll_pdf_new_xobject(::pdf_document *doc, ::fz_rect bbox, ::fz_matrix matrix, ::pdf_obj *res, ::fz_buffer *buffer);

/** Low-level wrapper for `::pdf_next_annot()`. */
FZ_FUNCTION ::pdf_annot *ll_pdf_next_annot(::pdf_annot *annot);

/** Low-level wrapper for `::pdf_next_widget()`. */
FZ_FUNCTION ::pdf_annot *ll_pdf_next_widget(::pdf_annot *previous);

/** Low-level wrapper for `::pdf_obj_is_dirty()`. */
FZ_FUNCTION int ll_pdf_obj_is_dirty(::pdf_obj *obj);

/** Low-level wrapper for `::pdf_obj_is_incremental()`. */
FZ_FUNCTION int ll_pdf_obj_is_incremental(::pdf_obj *obj);

/** Low-level wrapper for `::pdf_obj_marked()`. */
FZ_FUNCTION int ll_pdf_obj_marked(::pdf_obj *obj);

/** Low-level wrapper for `::pdf_obj_memo()`.

This function has out-params. Python/C# wrappers look like:
	`ll_pdf_obj_memo(::pdf_obj *obj, int bit)` => `(int, int memo)`
*/
FZ_FUNCTION int ll_pdf_obj_memo(::pdf_obj *obj, int bit, int *memo);

/** Low-level wrapper for `::pdf_obj_num_is_stream()`. */
FZ_FUNCTION int ll_pdf_obj_num_is_stream(::pdf_document *doc, int num);

/** Low-level wrapper for `::pdf_obj_parent_num()`. */
FZ_FUNCTION int ll_pdf_obj_parent_num(::pdf_obj *obj);

/** Low-level wrapper for `::pdf_obj_refs()`. */
FZ_FUNCTION int ll_pdf_obj_refs(::pdf_obj *ref);

/** Low-level wrapper for `::pdf_objcmp()`. */
FZ_FUNCTION int ll_pdf_objcmp(::pdf_obj *a, ::pdf_obj *b);

/** Low-level wrapper for `::pdf_objcmp_deep()`. */
FZ_FUNCTION int ll_pdf_objcmp_deep(::pdf_obj *a, ::pdf_obj *b);

/** Low-level wrapper for `::pdf_objcmp_resolve()`. */
FZ_FUNCTION int ll_pdf_objcmp_resolve(::pdf_obj *a, ::pdf_obj *b);

/** Low-level wrapper for `::pdf_open_contents_stream()`. */
FZ_FUNCTION ::fz_stream *ll_pdf_open_contents_stream(::pdf_document *doc, ::pdf_obj *obj);

/** Low-level wrapper for `::pdf_open_crypt()`. */
FZ_FUNCTION ::fz_stream *ll_pdf_open_crypt(::fz_stream *chain, ::pdf_crypt *crypt, int num, int gen);

/** Low-level wrapper for `::pdf_open_crypt_with_filter()`. */
FZ_FUNCTION ::fz_stream *ll_pdf_open_crypt_with_filter(::fz_stream *chain, ::pdf_crypt *crypt, ::pdf_obj *name, int num, int gen);

/** Low-level wrapper for `::pdf_open_document()`. */
FZ_FUNCTION ::pdf_document *ll_pdf_open_document(const char *filename);

/** Low-level wrapper for `::pdf_open_document_with_stream()`. */
FZ_FUNCTION ::pdf_document *ll_pdf_open_document_with_stream(::fz_stream *file);

/** Low-level wrapper for `::pdf_open_inline_stream()`. */
FZ_FUNCTION ::fz_stream *ll_pdf_open_inline_stream(::pdf_document *doc, ::pdf_obj *stmobj, int length, ::fz_stream *chain, ::fz_compression_params *params);

/** Low-level wrapper for `::pdf_open_raw_stream()`. */
FZ_FUNCTION ::fz_stream *ll_pdf_open_raw_stream(::pdf_obj *ref);

/** Low-level wrapper for `::pdf_open_raw_stream_number()`. */
FZ_FUNCTION ::fz_stream *ll_pdf_open_raw_stream_number(::pdf_document *doc, int num);

/** Low-level wrapper for `::pdf_open_stream()`. */
FZ_FUNCTION ::fz_stream *ll_pdf_open_stream(::pdf_obj *ref);

/** Low-level wrapper for `::pdf_open_stream_number()`. */
FZ_FUNCTION ::fz_stream *ll_pdf_open_stream_number(::pdf_document *doc, int num);

/** Low-level wrapper for `::pdf_open_stream_with_offset()`. */
FZ_FUNCTION ::fz_stream *ll_pdf_open_stream_with_offset(::pdf_document *doc, int num, ::pdf_obj *dict, int64_t stm_ofs);

/** Low-level wrapper for `::pdf_page_contents()`. */
FZ_FUNCTION ::pdf_obj *ll_pdf_page_contents(::pdf_page *page);

/** Low-level wrapper for `::pdf_page_event_close()`. */
FZ_FUNCTION void ll_pdf_page_event_close(::pdf_page *page);

/** Low-level wrapper for `::pdf_page_event_open()`. */
FZ_FUNCTION void ll_pdf_page_event_open(::pdf_page *page);

/** Low-level wrapper for `::pdf_page_from_fz_page()`. */
FZ_FUNCTION ::pdf_page *ll_pdf_page_from_fz_page(::fz_page *ptr);

/** Low-level wrapper for `::pdf_page_group()`. */
FZ_FUNCTION ::pdf_obj *ll_pdf_page_group(::pdf_page *page);

/** Low-level wrapper for `::pdf_page_has_transparency()`. */
FZ_FUNCTION int ll_pdf_page_has_transparency(::pdf_page *page);

/** Low-level wrapper for `::pdf_page_label()`. */
FZ_FUNCTION void ll_pdf_page_label(::pdf_document *doc, int page, char *buf, size_t size);

/** Low-level wrapper for `::pdf_page_label_imp()`. */
FZ_FUNCTION void ll_pdf_page_label_imp(::fz_document *doc, int chapter, int page, char *buf, size_t size);

/** Low-level wrapper for `::pdf_page_obj_transform()`. */
FZ_FUNCTION void ll_pdf_page_obj_transform(::pdf_obj *pageobj, ::fz_rect *outbox, ::fz_matrix *outctm);

/** Low-level wrapper for `::pdf_page_obj_transform_box()`. */
FZ_FUNCTION void ll_pdf_page_obj_transform_box(::pdf_obj *pageobj, ::fz_rect *outbox, ::fz_matrix *out, ::fz_box_type box);

/** Low-level wrapper for `::pdf_page_presentation()`.

This function has out-params. Python/C# wrappers look like:
	`ll_pdf_page_presentation(::pdf_page *page, ::fz_transition *transition)` => `(fz_transition *, float duration)`
*/
FZ_FUNCTION ::fz_transition *ll_pdf_page_presentation(::pdf_page *page, ::fz_transition *transition, float *duration);

/** Low-level wrapper for `::pdf_page_resources()`. */
FZ_FUNCTION ::pdf_obj *ll_pdf_page_resources(::pdf_page *page);

/** Low-level wrapper for `::pdf_page_separations()`. */
FZ_FUNCTION ::fz_separations *ll_pdf_page_separations(::pdf_page *page);

/** Low-level wrapper for `::pdf_page_transform()`. */
FZ_FUNCTION void ll_pdf_page_transform(::pdf_page *page, ::fz_rect *mediabox, ::fz_matrix *ctm);

/** Low-level wrapper for `::pdf_page_transform_box()`. */
FZ_FUNCTION void ll_pdf_page_transform_box(::pdf_page *page, ::fz_rect *mediabox, ::fz_matrix *ctm, ::fz_box_type box);

/** Low-level wrapper for `::pdf_page_write()`.

This function has out-params. Python/C# wrappers look like:
	`ll_pdf_page_write(::pdf_document *doc, ::fz_rect mediabox, ::pdf_obj **presources, ::fz_buffer **pcontents)` => `(fz_device *)`
*/
FZ_FUNCTION ::fz_device *ll_pdf_page_write(::pdf_document *doc, ::fz_rect mediabox, ::pdf_obj **presources, ::fz_buffer **pcontents);

/** Low-level wrapper for `::pdf_parse_array()`. */
FZ_FUNCTION ::pdf_obj *ll_pdf_parse_array(::pdf_document *doc, ::fz_stream *f, ::pdf_lexbuf *buf);

/** Low-level wrapper for `::pdf_parse_date()`. */
FZ_FUNCTION int64_t ll_pdf_parse_date(const char *s);

/** Low-level wrapper for `::pdf_parse_default_appearance()`.

This function has out-params. Python/C# wrappers look like:
	`ll_pdf_parse_default_appearance(const char *da, float color[4])` => `(const char *font, float size, int n)`
*/
FZ_FUNCTION void ll_pdf_parse_default_appearance(const char *da, const char **font, float *size, int *n, float color[4]);

/** Low-level wrapper for `::pdf_parse_dict()`. */
FZ_FUNCTION ::pdf_obj *ll_pdf_parse_dict(::pdf_document *doc, ::fz_stream *f, ::pdf_lexbuf *buf);

/** Low-level wrapper for `::pdf_parse_ind_obj()`.

This function has out-params. Python/C# wrappers look like:
	`ll_pdf_parse_ind_obj(::pdf_document *doc, ::fz_stream *f)` => `(pdf_obj *, int num, int gen, int64_t stm_ofs, int try_repair)`
*/
FZ_FUNCTION ::pdf_obj *ll_pdf_parse_ind_obj(::pdf_document *doc, ::fz_stream *f, int *num, int *gen, int64_t *stm_ofs, int *try_repair);

/** Low-level wrapper for `::pdf_parse_journal_obj()`.

This function has out-params. Python/C# wrappers look like:
	`ll_pdf_parse_journal_obj(::pdf_document *doc, ::fz_stream *stm, ::fz_buffer **ostm)` => `(pdf_obj *, int onum, int newobj)`
*/
FZ_FUNCTION ::pdf_obj *ll_pdf_parse_journal_obj(::pdf_document *doc, ::fz_stream *stm, int *onum, ::fz_buffer **ostm, int *newobj);

/** Low-level wrapper for `::pdf_parse_stm_obj()`. */
FZ_FUNCTION ::pdf_obj *ll_pdf_parse_stm_obj(::pdf_document *doc, ::fz_stream *f, ::pdf_lexbuf *buf);

/** Low-level wrapper for `::pdf_parse_write_options()`. */
FZ_FUNCTION ::pdf_write_options *ll_pdf_parse_write_options(::pdf_write_options *opts, const char *args);

/** Low-level wrapper for `::pdf_pin_document()`. */
FZ_FUNCTION ::pdf_document *ll_pdf_pin_document(::pdf_obj *obj);

/** Low-level wrapper for `::pdf_preview_signature_as_display_list()`. */
FZ_FUNCTION ::fz_display_list *ll_pdf_preview_signature_as_display_list(float w, float h, ::fz_text_language lang, ::pdf_pkcs7_signer *signer, int appearance_flags, ::fz_image *graphic, const char *reason, const char *location);

/** Low-level wrapper for `::pdf_preview_signature_as_pixmap()`. */
FZ_FUNCTION ::fz_pixmap *ll_pdf_preview_signature_as_pixmap(int w, int h, ::fz_text_language lang, ::pdf_pkcs7_signer *signer, int appearance_flags, ::fz_image *graphic, const char *reason, const char *location);

/** Low-level wrapper for `::pdf_print_crypt()`. */
FZ_FUNCTION void ll_pdf_print_crypt(::fz_output *out, ::pdf_crypt *crypt);

/** Low-level wrapper for `::pdf_print_default_appearance()`. */
FZ_FUNCTION void ll_pdf_print_default_appearance(char *buf, int nbuf, const char *font, float size, int n, const float *color);

/** Low-level wrapper for `::pdf_print_encrypted_obj()`.

This function has out-params. Python/C# wrappers look like:
	`ll_pdf_print_encrypted_obj(::fz_output *out, ::pdf_obj *obj, int tight, int ascii, ::pdf_crypt *crypt, int num, int gen)` => int sep
*/
FZ_FUNCTION void ll_pdf_print_encrypted_obj(::fz_output *out, ::pdf_obj *obj, int tight, int ascii, ::pdf_crypt *crypt, int num, int gen, int *sep);

/** Low-level wrapper for `::pdf_print_font()`. */
FZ_FUNCTION void ll_pdf_print_font(::fz_output *out, ::pdf_font_desc *fontdesc);

/** Low-level wrapper for `::pdf_print_obj()`. */
FZ_FUNCTION void ll_pdf_print_obj(::fz_output *out, ::pdf_obj *obj, int tight, int ascii);

/** Low-level wrapper for `::pdf_process_annot()`. */
FZ_FUNCTION void ll_pdf_process_annot(::pdf_processor *proc, ::pdf_annot *annot, ::fz_cookie *cookie);

/** Low-level wrapper for `::pdf_process_contents()`.

This function has out-params. Python/C# wrappers look like:
	`ll_pdf_process_contents(::pdf_processor *proc, ::pdf_document *doc, ::pdf_obj *res, ::pdf_obj *stm, ::fz_cookie *cookie, ::pdf_obj **out_res)` => 
*/
FZ_FUNCTION void ll_pdf_process_contents(::pdf_processor *proc, ::pdf_document *doc, ::pdf_obj *res, ::pdf_obj *stm, ::fz_cookie *cookie, ::pdf_obj **out_res);

/** Low-level wrapper for `::pdf_process_glyph()`. */
FZ_FUNCTION void ll_pdf_process_glyph(::pdf_processor *proc, ::pdf_document *doc, ::pdf_obj *resources, ::fz_buffer *contents);

/** Low-level wrapper for `::pdf_process_raw_contents()`. */
FZ_FUNCTION void ll_pdf_process_raw_contents(::pdf_processor *proc, ::pdf_document *doc, ::pdf_obj *rdb, ::pdf_obj *stmobj, ::fz_cookie *cookie);

/** Low-level wrapper for `::pdf_processor_pop_resources()`. */
FZ_FUNCTION ::pdf_obj *ll_pdf_processor_pop_resources(::pdf_processor *proc);

/** Low-level wrapper for `::pdf_processor_push_resources()`. */
FZ_FUNCTION void ll_pdf_processor_push_resources(::pdf_processor *proc, ::pdf_obj *res);

/** Low-level wrapper for `::pdf_progressive_advance()`. */
FZ_FUNCTION ::pdf_obj *ll_pdf_progressive_advance(::pdf_document *doc, int pagenum);

/** Low-level wrapper for `::pdf_purge_local_font_resources()`. */
FZ_FUNCTION void ll_pdf_purge_local_font_resources(::pdf_document *doc);

/** Low-level wrapper for `::pdf_purge_locals_from_store()`. */
FZ_FUNCTION void ll_pdf_purge_locals_from_store(::pdf_document *doc);

/** Low-level wrapper for `::pdf_read_journal()`. */
FZ_FUNCTION void ll_pdf_read_journal(::pdf_document *doc, ::fz_stream *stm);

/** Low-level wrapper for `::pdf_read_ocg()`. */
FZ_FUNCTION ::pdf_ocg_descriptor *ll_pdf_read_ocg(::pdf_document *doc);

/** Low-level wrapper for `::pdf_rearrange_pages()`. */
FZ_FUNCTION void ll_pdf_rearrange_pages(::pdf_document *doc, int count, const int *pages);

/** Low-level wrapper for `::pdf_rearrange_pages2()`. */
/** Swig-friendly wrapper for pdf_rearrange_pages(). */
FZ_FUNCTION void ll_pdf_rearrange_pages2(::pdf_document *doc, const std::vector<int> &pages);

/** Low-level wrapper for `::pdf_recolor_shade()`. */
/**
	Recolor a shade.
*/
FZ_FUNCTION ::pdf_obj *ll_pdf_recolor_shade(::pdf_obj *shade, ::pdf_shade_recolorer *reshade, void *opaque);

/** Low-level wrapper for `::pdf_redact_page()`. */
FZ_FUNCTION int ll_pdf_redact_page(::pdf_document *doc, ::pdf_page *page, ::pdf_redact_options *opts);

/** Low-level wrapper for `::pdf_redo()`. */
FZ_FUNCTION void ll_pdf_redo(::pdf_document *doc);

/** Low-level wrapper for `::pdf_remove_item()`. */
FZ_FUNCTION void ll_pdf_remove_item(::fz_store_drop_fn *drop, ::pdf_obj *key);

/** Low-level wrapper for `::pdf_repair_obj()`.

This function has out-params. Python/C# wrappers look like:
	`ll_pdf_repair_obj(::pdf_document *doc, ::pdf_lexbuf *buf, ::pdf_obj **encrypt, ::pdf_obj **id, ::pdf_obj **page, ::pdf_obj **root)` => `(int, int64_t stmofsp, int64_t stmlenp, int64_t tmpofs)`
*/
FZ_FUNCTION int ll_pdf_repair_obj(::pdf_document *doc, ::pdf_lexbuf *buf, int64_t *stmofsp, int64_t *stmlenp, ::pdf_obj **encrypt, ::pdf_obj **id, ::pdf_obj **page, int64_t *tmpofs, ::pdf_obj **root);

/** Low-level wrapper for `::pdf_repair_obj_stms()`. */
FZ_FUNCTION void ll_pdf_repair_obj_stms(::pdf_document *doc);

/** Low-level wrapper for `::pdf_repair_trailer()`. */
FZ_FUNCTION void ll_pdf_repair_trailer(::pdf_document *doc);

/** Low-level wrapper for `::pdf_repair_xref()`. */
FZ_FUNCTION void ll_pdf_repair_xref(::pdf_document *doc);

/** Low-level wrapper for `::pdf_replace_xref()`. */
FZ_FUNCTION void ll_pdf_replace_xref(::pdf_document *doc, ::pdf_xref_entry *entries, int n);

/** Low-level wrapper for `::pdf_reset_form()`. */
FZ_FUNCTION void ll_pdf_reset_form(::pdf_document *doc, ::pdf_obj *fields, int exclude);

/** Low-level wrapper for `::pdf_reset_processor()`. */
FZ_FUNCTION void ll_pdf_reset_processor(::pdf_processor *proc);

/** Low-level wrapper for `::pdf_resolve_indirect()`. */
/**
	Resolve an indirect object (or chain of objects).

	This can cause xref reorganisations (solidifications etc) due to
	repairs, so all held pdf_xref_entries should be considered
	invalid after this call (other than the returned one).
*/
FZ_FUNCTION ::pdf_obj *ll_pdf_resolve_indirect(::pdf_obj *ref);

/** Low-level wrapper for `::pdf_resolve_indirect_chain()`. */
FZ_FUNCTION ::pdf_obj *ll_pdf_resolve_indirect_chain(::pdf_obj *ref);

/** Low-level wrapper for `::pdf_resolve_link()`.

This function has out-params. Python/C# wrappers look like:
	`ll_pdf_resolve_link(::pdf_document *doc, const char *uri)` => `(int, float xp, float yp)`
*/
FZ_FUNCTION int ll_pdf_resolve_link(::pdf_document *doc, const char *uri, float *xp, float *yp);

/** Low-level wrapper for `::pdf_resolve_link_dest()`. */
FZ_FUNCTION ::fz_link_dest ll_pdf_resolve_link_dest(::pdf_document *doc, const char *uri);

/** Low-level wrapper for `::pdf_rewrite_images()`. */
FZ_FUNCTION void ll_pdf_rewrite_images(::pdf_document *doc, ::pdf_image_rewriter_options *opts);

/** Low-level wrapper for `::pdf_run_annot()`. */
FZ_FUNCTION void ll_pdf_run_annot(::pdf_annot *annot, ::fz_device *dev, ::fz_matrix ctm, ::fz_cookie *cookie);

/** Low-level wrapper for `::pdf_run_document_structure()`. */
FZ_FUNCTION void ll_pdf_run_document_structure(::pdf_document *doc, ::fz_device *dev, ::fz_cookie *cookie);

/** Low-level wrapper for `::pdf_run_glyph()`. */
FZ_FUNCTION void ll_pdf_run_glyph(::pdf_document *doc, ::pdf_obj *resources, ::fz_buffer *contents, ::fz_device *dev, ::fz_matrix ctm, void *gstate, ::fz_default_colorspaces *default_cs);

/** Low-level wrapper for `::pdf_run_page()`. */
FZ_FUNCTION void ll_pdf_run_page(::pdf_page *page, ::fz_device *dev, ::fz_matrix ctm, ::fz_cookie *cookie);

/** Low-level wrapper for `::pdf_run_page_annots()`. */
FZ_FUNCTION void ll_pdf_run_page_annots(::pdf_page *page, ::fz_device *dev, ::fz_matrix ctm, ::fz_cookie *cookie);

/** Low-level wrapper for `::pdf_run_page_annots_with_usage()`. */
FZ_FUNCTION void ll_pdf_run_page_annots_with_usage(::pdf_page *page, ::fz_device *dev, ::fz_matrix ctm, const char *usage, ::fz_cookie *cookie);

/** Low-level wrapper for `::pdf_run_page_contents()`. */
FZ_FUNCTION void ll_pdf_run_page_contents(::pdf_page *page, ::fz_device *dev, ::fz_matrix ctm, ::fz_cookie *cookie);

/** Low-level wrapper for `::pdf_run_page_contents_with_usage()`. */
FZ_FUNCTION void ll_pdf_run_page_contents_with_usage(::pdf_page *page, ::fz_device *dev, ::fz_matrix ctm, const char *usage, ::fz_cookie *cookie);

/** Low-level wrapper for `::pdf_run_page_widgets()`. */
FZ_FUNCTION void ll_pdf_run_page_widgets(::pdf_page *page, ::fz_device *dev, ::fz_matrix ctm, ::fz_cookie *cookie);

/** Low-level wrapper for `::pdf_run_page_widgets_with_usage()`. */
FZ_FUNCTION void ll_pdf_run_page_widgets_with_usage(::pdf_page *page, ::fz_device *dev, ::fz_matrix ctm, const char *usage, ::fz_cookie *cookie);

/** Low-level wrapper for `::pdf_run_page_with_usage()`. */
FZ_FUNCTION void ll_pdf_run_page_with_usage(::pdf_page *page, ::fz_device *dev, ::fz_matrix ctm, const char *usage, ::fz_cookie *cookie);

/** Low-level wrapper for `::pdf_sample_shade_function()`.

This function has out-params. Python/C# wrappers look like:
	`ll_pdf_sample_shade_function(int n, int funcs, ::pdf_function **func, float t0, float t1)` => `(float samples)`
*/
FZ_FUNCTION void ll_pdf_sample_shade_function(float *samples, int n, int funcs, ::pdf_function **func, float t0, float t1);

/** Low-level wrapper for `::pdf_save_document()`. */
FZ_FUNCTION void ll_pdf_save_document(::pdf_document *doc, const char *filename, const ::pdf_write_options *opts);

/** Low-level wrapper for `::pdf_save_journal()`. */
FZ_FUNCTION void ll_pdf_save_journal(::pdf_document *doc, const char *filename);

/** Low-level wrapper for `::pdf_save_snapshot()`. */
FZ_FUNCTION void ll_pdf_save_snapshot(::pdf_document *doc, const char *filename);

/** Low-level wrapper for `::pdf_select_layer_config()`. */
FZ_FUNCTION void ll_pdf_select_layer_config(::pdf_document *doc, int config_num);

/** Low-level wrapper for `::pdf_select_layer_config_ui()`. */
FZ_FUNCTION void ll_pdf_select_layer_config_ui(::pdf_document *doc, int ui);

/** Low-level wrapper for `::pdf_serialise_journal()`. */
FZ_FUNCTION void ll_pdf_serialise_journal(::pdf_document *doc, ::fz_output *out);

/** Low-level wrapper for `::pdf_set_annot_active()`. */
FZ_FUNCTION void ll_pdf_set_annot_active(::pdf_annot *annot, int active);

/** Low-level wrapper for `::pdf_set_annot_appearance()`. */
FZ_FUNCTION void ll_pdf_set_annot_appearance(::pdf_annot *annot, const char *appearance, const char *state, ::fz_matrix ctm, ::fz_rect bbox, ::pdf_obj *res, ::fz_buffer *contents);

/** Low-level wrapper for `::pdf_set_annot_appearance_from_display_list()`. */
FZ_FUNCTION void ll_pdf_set_annot_appearance_from_display_list(::pdf_annot *annot, const char *appearance, const char *state, ::fz_matrix ctm, ::fz_display_list *list);

/** Low-level wrapper for `::pdf_set_annot_author()`. */
FZ_FUNCTION void ll_pdf_set_annot_author(::pdf_annot *annot, const char *author);

/** Low-level wrapper for `::pdf_set_annot_border()`. */
FZ_FUNCTION void ll_pdf_set_annot_border(::pdf_annot *annot, float width);

/** Low-level wrapper for `::pdf_set_annot_border_effect()`. */
FZ_FUNCTION void ll_pdf_set_annot_border_effect(::pdf_annot *annot, enum pdf_border_effect effect);

/** Low-level wrapper for `::pdf_set_annot_border_effect_intensity()`. */
FZ_FUNCTION void ll_pdf_set_annot_border_effect_intensity(::pdf_annot *annot, float intensity);

/** Low-level wrapper for `::pdf_set_annot_border_style()`. */
FZ_FUNCTION void ll_pdf_set_annot_border_style(::pdf_annot *annot, enum pdf_border_style style);

/** Low-level wrapper for `::pdf_set_annot_border_width()`. */
FZ_FUNCTION void ll_pdf_set_annot_border_width(::pdf_annot *annot, float width);

/** Low-level wrapper for `::pdf_set_annot_color()`. */
FZ_FUNCTION void ll_pdf_set_annot_color(::pdf_annot *annot, int n, const float *color);

/** Low-level wrapper for `::pdf_set_annot_contents()`. */
FZ_FUNCTION void ll_pdf_set_annot_contents(::pdf_annot *annot, const char *text);

/** Low-level wrapper for `::pdf_set_annot_creation_date()`. */
FZ_FUNCTION void ll_pdf_set_annot_creation_date(::pdf_annot *annot, int64_t time);

/** Low-level wrapper for `::pdf_set_annot_default_appearance()`. */
FZ_FUNCTION void ll_pdf_set_annot_default_appearance(::pdf_annot *annot, const char *font, float size, int n, const float *color);

/** Low-level wrapper for `::pdf_set_annot_field_value()`. */
FZ_FUNCTION int ll_pdf_set_annot_field_value(::pdf_document *doc, ::pdf_annot *widget, const char *text, int ignore_trigger_events);

/** Low-level wrapper for `::pdf_set_annot_filespec()`. */
FZ_FUNCTION void ll_pdf_set_annot_filespec(::pdf_annot *annot, ::pdf_obj *obj);

/** Low-level wrapper for `::pdf_set_annot_flags()`. */
FZ_FUNCTION void ll_pdf_set_annot_flags(::pdf_annot *annot, int flags);

/** Low-level wrapper for `::pdf_set_annot_hidden_for_editing()`. */
FZ_FUNCTION void ll_pdf_set_annot_hidden_for_editing(::pdf_annot *annot, int hidden);

/** Low-level wrapper for `::pdf_set_annot_hot()`. */
FZ_FUNCTION void ll_pdf_set_annot_hot(::pdf_annot *annot, int hot);

/** Low-level wrapper for `::pdf_set_annot_icon_name()`. */
FZ_FUNCTION void ll_pdf_set_annot_icon_name(::pdf_annot *annot, const char *name);

/** Low-level wrapper for `::pdf_set_annot_ink_list()`. */
FZ_FUNCTION void ll_pdf_set_annot_ink_list(::pdf_annot *annot, int n, const int *count, const ::fz_point *v);

/** Low-level wrapper for `::pdf_set_annot_intent()`. */
FZ_FUNCTION void ll_pdf_set_annot_intent(::pdf_annot *annot, enum pdf_intent it);

/** Low-level wrapper for `::pdf_set_annot_interior_color()`. */
FZ_FUNCTION void ll_pdf_set_annot_interior_color(::pdf_annot *annot, int n, const float *color);

/** Low-level wrapper for `::pdf_set_annot_is_open()`. */
FZ_FUNCTION void ll_pdf_set_annot_is_open(::pdf_annot *annot, int is_open);

/** Low-level wrapper for `::pdf_set_annot_language()`. */
FZ_FUNCTION void ll_pdf_set_annot_language(::pdf_annot *annot, ::fz_text_language lang);

/** Low-level wrapper for `::pdf_set_annot_line()`. */
FZ_FUNCTION void ll_pdf_set_annot_line(::pdf_annot *annot, ::fz_point a, ::fz_point b);

/** Low-level wrapper for `::pdf_set_annot_line_end_style()`. */
FZ_FUNCTION void ll_pdf_set_annot_line_end_style(::pdf_annot *annot, enum pdf_line_ending e);

/** Low-level wrapper for `::pdf_set_annot_line_ending_styles()`. */
FZ_FUNCTION void ll_pdf_set_annot_line_ending_styles(::pdf_annot *annot, enum pdf_line_ending start_style, enum pdf_line_ending end_style);

/** Low-level wrapper for `::pdf_set_annot_line_start_style()`. */
FZ_FUNCTION void ll_pdf_set_annot_line_start_style(::pdf_annot *annot, enum pdf_line_ending s);

/** Low-level wrapper for `::pdf_set_annot_modification_date()`. */
FZ_FUNCTION void ll_pdf_set_annot_modification_date(::pdf_annot *annot, int64_t time);

/** Low-level wrapper for `::pdf_set_annot_opacity()`. */
FZ_FUNCTION void ll_pdf_set_annot_opacity(::pdf_annot *annot, float opacity);

/** Low-level wrapper for `::pdf_set_annot_popup()`. */
FZ_FUNCTION void ll_pdf_set_annot_popup(::pdf_annot *annot, ::fz_rect rect);

/** Low-level wrapper for `::pdf_set_annot_quad_points()`. */
FZ_FUNCTION void ll_pdf_set_annot_quad_points(::pdf_annot *annot, int n, const ::fz_quad *qv);

/** Low-level wrapper for `::pdf_set_annot_quadding()`. */
FZ_FUNCTION void ll_pdf_set_annot_quadding(::pdf_annot *annot, int q);

/** Low-level wrapper for `::pdf_set_annot_rect()`. */
FZ_FUNCTION void ll_pdf_set_annot_rect(::pdf_annot *annot, ::fz_rect rect);

/** Low-level wrapper for `::pdf_set_annot_resynthesised()`. */
FZ_FUNCTION void ll_pdf_set_annot_resynthesised(::pdf_annot *annot);

/** Low-level wrapper for `::pdf_set_annot_stamp_image()`. */
FZ_FUNCTION void ll_pdf_set_annot_stamp_image(::pdf_annot *annot, ::fz_image *image);

/** Low-level wrapper for `::pdf_set_annot_vertex()`. */
FZ_FUNCTION void ll_pdf_set_annot_vertex(::pdf_annot *annot, int i, ::fz_point p);

/** Low-level wrapper for `::pdf_set_annot_vertices()`. */
FZ_FUNCTION void ll_pdf_set_annot_vertices(::pdf_annot *annot, int n, const ::fz_point *v);

/** Low-level wrapper for `::pdf_set_choice_field_value()`. */
FZ_FUNCTION int ll_pdf_set_choice_field_value(::pdf_annot *widget, const char *value);

/** Low-level wrapper for `::pdf_set_cmap_wmode()`. */
FZ_FUNCTION void ll_pdf_set_cmap_wmode(::pdf_cmap *cmap, int wmode);

/** Low-level wrapper for `::pdf_set_default_hmtx()`. */
FZ_FUNCTION void ll_pdf_set_default_hmtx(::pdf_font_desc *font, int w);

/** Low-level wrapper for `::pdf_set_default_vmtx()`. */
FZ_FUNCTION void ll_pdf_set_default_vmtx(::pdf_font_desc *font, int y, int w);

/** Low-level wrapper for `::pdf_set_doc_event_callback()`. */
FZ_FUNCTION void ll_pdf_set_doc_event_callback(::pdf_document *doc, ::pdf_doc_event_cb *event_cb, ::pdf_free_doc_event_data_cb *free_event_data_cb, void *data);

/** Low-level wrapper for `::pdf_set_document_language()`. */
FZ_FUNCTION void ll_pdf_set_document_language(::pdf_document *doc, ::fz_text_language lang);

/** Low-level wrapper for `::pdf_set_field_value()`. */
FZ_FUNCTION int ll_pdf_set_field_value(::pdf_document *doc, ::pdf_obj *field, const char *text, int ignore_trigger_events);

/** Low-level wrapper for `::pdf_set_font_wmode()`. */
FZ_FUNCTION void ll_pdf_set_font_wmode(::pdf_font_desc *font, int wmode);

/** Low-level wrapper for `::pdf_set_int()`. */
FZ_FUNCTION void ll_pdf_set_int(::pdf_obj *obj, int64_t i);

/** Low-level wrapper for `::pdf_set_layer_config_as_default()`. */
FZ_FUNCTION void ll_pdf_set_layer_config_as_default(::pdf_document *doc);

/** Low-level wrapper for `::pdf_set_obj_memo()`. */
FZ_FUNCTION void ll_pdf_set_obj_memo(::pdf_obj *obj, int bit, int memo);

/** Low-level wrapper for `::pdf_set_obj_parent()`. */
FZ_FUNCTION void ll_pdf_set_obj_parent(::pdf_obj *obj, int num);

/** Low-level wrapper for `::pdf_set_page_box()`. */
FZ_FUNCTION void ll_pdf_set_page_box(::pdf_page *page, ::fz_box_type box, ::fz_rect rect);

/** Low-level wrapper for `::pdf_set_page_labels()`. */
FZ_FUNCTION void ll_pdf_set_page_labels(::pdf_document *doc, int index, ::pdf_page_label_style style, const char *prefix, int start);

/** Low-level wrapper for `::pdf_set_populating_xref_trailer()`. */
FZ_FUNCTION void ll_pdf_set_populating_xref_trailer(::pdf_document *doc, ::pdf_obj *trailer);

/** Low-level wrapper for `::pdf_set_str_len()`. */
FZ_FUNCTION void ll_pdf_set_str_len(::pdf_obj *obj, size_t newlen);

/** Low-level wrapper for `::pdf_set_text_field_value()`. */
FZ_FUNCTION int ll_pdf_set_text_field_value(::pdf_annot *widget, const char *value);

/** Low-level wrapper for `::pdf_set_usecmap()`. */
FZ_FUNCTION void ll_pdf_set_usecmap(::pdf_cmap *cmap, ::pdf_cmap *usecmap);

/** Low-level wrapper for `::pdf_set_widget_editing_state()`. */
FZ_FUNCTION void ll_pdf_set_widget_editing_state(::pdf_annot *widget, int editing);

/** Low-level wrapper for `::pdf_sign_signature()`. */
FZ_FUNCTION void ll_pdf_sign_signature(::pdf_annot *widget, ::pdf_pkcs7_signer *signer, int appearance_flags, ::fz_image *graphic, const char *reason, const char *location);

/** Low-level wrapper for `::pdf_sign_signature_with_appearance()`. */
FZ_FUNCTION void ll_pdf_sign_signature_with_appearance(::pdf_annot *widget, ::pdf_pkcs7_signer *signer, int64_t date, ::fz_display_list *disp_list);

/** Low-level wrapper for `::pdf_signature_appearance_signed()`. */
FZ_FUNCTION ::fz_display_list *ll_pdf_signature_appearance_signed(::fz_rect rect, ::fz_text_language lang, ::fz_image *img, const char *left_text, const char *right_text, int include_logo);

/** Low-level wrapper for `::pdf_signature_appearance_unsigned()`. */
FZ_FUNCTION ::fz_display_list *ll_pdf_signature_appearance_unsigned(::fz_rect rect, ::fz_text_language lang);

/** Low-level wrapper for `::pdf_signature_byte_range()`. */
FZ_FUNCTION int ll_pdf_signature_byte_range(::pdf_document *doc, ::pdf_obj *signature, ::fz_range *byte_range);

/** Low-level wrapper for `::pdf_signature_contents()`.

This function has out-params. Python/C# wrappers look like:
	`ll_pdf_signature_contents(::pdf_document *doc, ::pdf_obj *signature)` => `(size_t, char *contents)`
*/
FZ_FUNCTION size_t ll_pdf_signature_contents(::pdf_document *doc, ::pdf_obj *signature, char **contents);

/** Low-level wrapper for `::pdf_signature_drop_distinguished_name()`. */
FZ_FUNCTION void ll_pdf_signature_drop_distinguished_name(::pdf_pkcs7_distinguished_name *name);

/** Low-level wrapper for `::pdf_signature_error_description()`. */
FZ_FUNCTION char *ll_pdf_signature_error_description(::pdf_signature_error err);

/** Low-level wrapper for `::pdf_signature_format_distinguished_name()`. */
FZ_FUNCTION char *ll_pdf_signature_format_distinguished_name(::pdf_pkcs7_distinguished_name *name);

/** Low-level wrapper for `::pdf_signature_get_signatory()`. */
FZ_FUNCTION ::pdf_pkcs7_distinguished_name *ll_pdf_signature_get_signatory(::pdf_pkcs7_verifier *verifier, ::pdf_document *doc, ::pdf_obj *signature);

/** Low-level wrapper for `::pdf_signature_get_widget_signatory()`. */
FZ_FUNCTION ::pdf_pkcs7_distinguished_name *ll_pdf_signature_get_widget_signatory(::pdf_pkcs7_verifier *verifier, ::pdf_annot *widget);

/** Low-level wrapper for `::pdf_signature_hash_bytes()`. */
FZ_FUNCTION ::fz_stream *ll_pdf_signature_hash_bytes(::pdf_document *doc, ::pdf_obj *signature);

/** Low-level wrapper for `::pdf_signature_incremental_change_since_signing()`. */
FZ_FUNCTION int ll_pdf_signature_incremental_change_since_signing(::pdf_document *doc, ::pdf_obj *signature);

/** Low-level wrapper for `::pdf_signature_info()`. */
FZ_FUNCTION char *ll_pdf_signature_info(const char *name, ::pdf_pkcs7_distinguished_name *dn, const char *reason, const char *location, int64_t date, int include_labels);

/** Low-level wrapper for `::pdf_signature_is_signed()`. */
FZ_FUNCTION int ll_pdf_signature_is_signed(::pdf_document *doc, ::pdf_obj *field);

/** Low-level wrapper for `::pdf_signature_set_value()`. */
FZ_FUNCTION void ll_pdf_signature_set_value(::pdf_document *doc, ::pdf_obj *field, ::pdf_pkcs7_signer *signer, int64_t stime);

/** Low-level wrapper for `::pdf_sort_cmap()`. */
FZ_FUNCTION void ll_pdf_sort_cmap(::pdf_cmap *cmap);

/** Low-level wrapper for `::pdf_sort_dict()`. */
FZ_FUNCTION void ll_pdf_sort_dict(::pdf_obj *dict);

/** Low-level wrapper for `::pdf_specifics()`. */
FZ_FUNCTION ::pdf_document *ll_pdf_specifics(::fz_document *doc);

/** Low-level wrapper for `::pdf_sprint_obj()`.

This function has out-params. Python/C# wrappers look like:
	`ll_pdf_sprint_obj(char *buf, size_t cap, ::pdf_obj *obj, int tight, int ascii)` => `(char *, size_t len)`
*/
FZ_FUNCTION char *ll_pdf_sprint_obj(char *buf, size_t cap, size_t *len, ::pdf_obj *obj, int tight, int ascii);

/** Low-level wrapper for `::pdf_store_item()`. */
FZ_FUNCTION void ll_pdf_store_item(::pdf_obj *key, void *val, size_t itemsize);

/** Low-level wrapper for `::pdf_string_from_annot_type()`. */
FZ_FUNCTION const char *ll_pdf_string_from_annot_type(enum pdf_annot_type type);

/** Low-level wrapper for `::pdf_string_from_intent()`. */
FZ_FUNCTION const char *ll_pdf_string_from_intent(enum pdf_intent intent);

/** Low-level wrapper for `::pdf_string_from_line_ending()`. */
FZ_FUNCTION const char *ll_pdf_string_from_line_ending(enum pdf_line_ending end);

/** Low-level wrapper for `::pdf_structure_type()`. */
FZ_FUNCTION ::fz_structure ll_pdf_structure_type(::pdf_obj *role_map, ::pdf_obj *tag);

/** Low-level wrapper for `::pdf_subset_fonts()`. */
FZ_FUNCTION void ll_pdf_subset_fonts(::pdf_document *doc, int pages_len, const int *pages);

/** Low-level wrapper for `::pdf_subset_fonts2()`. */
/** Swig-friendly wrapper for pdf_subset_fonts(). */
FZ_FUNCTION void ll_pdf_subset_fonts2(::pdf_document *doc, const std::vector<int> &pages);

/** Low-level wrapper for `::pdf_text_widget_format()`. */
FZ_FUNCTION int ll_pdf_text_widget_format(::pdf_annot *tw);

/** Low-level wrapper for `::pdf_text_widget_max_len()`. */
FZ_FUNCTION int ll_pdf_text_widget_max_len(::pdf_annot *tw);

/** Low-level wrapper for `::pdf_to_bool()`. */
FZ_FUNCTION int ll_pdf_to_bool(::pdf_obj *obj);

/** Low-level wrapper for `::pdf_to_bool_default()`. */
FZ_FUNCTION int ll_pdf_to_bool_default(::pdf_obj *obj, int def);

/** Low-level wrapper for `::pdf_to_date()`. */
FZ_FUNCTION int64_t ll_pdf_to_date(::pdf_obj *time);

/** Low-level wrapper for `::pdf_to_gen()`. */
FZ_FUNCTION int ll_pdf_to_gen(::pdf_obj *obj);

/** Low-level wrapper for `::pdf_to_int()`. */
FZ_FUNCTION int ll_pdf_to_int(::pdf_obj *obj);

/** Low-level wrapper for `::pdf_to_int64()`. */
FZ_FUNCTION int64_t ll_pdf_to_int64(::pdf_obj *obj);

/** Low-level wrapper for `::pdf_to_int_default()`. */
FZ_FUNCTION int ll_pdf_to_int_default(::pdf_obj *obj, int def);

/** Low-level wrapper for `::pdf_to_matrix()`. */
FZ_FUNCTION ::fz_matrix ll_pdf_to_matrix(::pdf_obj *array);

/** Low-level wrapper for `::pdf_to_name()`. */
FZ_FUNCTION const char *ll_pdf_to_name(::pdf_obj *obj);

/** Low-level wrapper for `::pdf_to_num()`. */
FZ_FUNCTION int ll_pdf_to_num(::pdf_obj *obj);

/** Low-level wrapper for `::pdf_to_quad()`. */
FZ_FUNCTION ::fz_quad ll_pdf_to_quad(::pdf_obj *array, int offset);

/** Low-level wrapper for `::pdf_to_real()`. */
FZ_FUNCTION float ll_pdf_to_real(::pdf_obj *obj);

/** Low-level wrapper for `::pdf_to_real_default()`. */
FZ_FUNCTION float ll_pdf_to_real_default(::pdf_obj *obj, float def);

/** Low-level wrapper for `::pdf_to_rect()`. */
FZ_FUNCTION ::fz_rect ll_pdf_to_rect(::pdf_obj *array);

/** Low-level wrapper for `::pdf_to_str_buf()`. */
FZ_FUNCTION char *ll_pdf_to_str_buf(::pdf_obj *obj);

/** Low-level wrapper for `::pdf_to_str_len()`. */
FZ_FUNCTION size_t ll_pdf_to_str_len(::pdf_obj *obj);

/** Low-level wrapper for `::pdf_to_string()`.

This function has out-params. Python/C# wrappers look like:
	`ll_pdf_to_string(::pdf_obj *obj)` => `(const char *, size_t sizep)`
*/
FZ_FUNCTION const char *ll_pdf_to_string(::pdf_obj *obj, size_t *sizep);

/** Low-level wrapper for `::pdf_to_text_string()`. */
FZ_FUNCTION const char *ll_pdf_to_text_string(::pdf_obj *obj);

/** Low-level wrapper for `::pdf_toggle_layer_config_ui()`. */
FZ_FUNCTION void ll_pdf_toggle_layer_config_ui(::pdf_document *doc, int ui);

/** Low-level wrapper for `::pdf_toggle_widget()`. */
FZ_FUNCTION int ll_pdf_toggle_widget(::pdf_annot *widget);

/** Low-level wrapper for `::pdf_tos_get_text()`. */
FZ_FUNCTION ::fz_text *ll_pdf_tos_get_text(::pdf_text_object_state *tos);

/** Low-level wrapper for `::pdf_tos_make_trm()`. */
FZ_FUNCTION int ll_pdf_tos_make_trm(::pdf_text_object_state *tos, ::pdf_text_state *text, ::pdf_font_desc *fontdesc, int cid, ::fz_matrix *trm);

/** Low-level wrapper for `::pdf_tos_move_after_char()`. */
FZ_FUNCTION void ll_pdf_tos_move_after_char(::pdf_text_object_state *tos);

/** Low-level wrapper for `::pdf_tos_newline()`. */
FZ_FUNCTION void ll_pdf_tos_newline(::pdf_text_object_state *tos, float leading);

/** Low-level wrapper for `::pdf_tos_reset()`. */
FZ_FUNCTION void ll_pdf_tos_reset(::pdf_text_object_state *tos, int render);

/** Low-level wrapper for `::pdf_tos_restore()`. */
FZ_FUNCTION void ll_pdf_tos_restore(::pdf_text_object_state *tos, ::fz_matrix save[2]);

/** Low-level wrapper for `::pdf_tos_save()`. */
FZ_FUNCTION void ll_pdf_tos_save(::pdf_text_object_state *tos, ::fz_matrix save[2]);

/** Low-level wrapper for `::pdf_tos_set_matrix()`. */
FZ_FUNCTION void ll_pdf_tos_set_matrix(::pdf_text_object_state *tos, float a, float b, float c, float d, float e, float f);

/** Low-level wrapper for `::pdf_tos_translate()`. */
FZ_FUNCTION void ll_pdf_tos_translate(::pdf_text_object_state *tos, float tx, float ty);

/** Low-level wrapper for `::pdf_trailer()`. */
FZ_FUNCTION ::pdf_obj *ll_pdf_trailer(::pdf_document *doc);

/** Low-level wrapper for `::pdf_undo()`. */
FZ_FUNCTION void ll_pdf_undo(::pdf_document *doc);

/** Low-level wrapper for `::pdf_undoredo_state()`.

This function has out-params. Python/C# wrappers look like:
	`ll_pdf_undoredo_state(::pdf_document *doc)` => `(int, int steps)`
*/
FZ_FUNCTION int ll_pdf_undoredo_state(::pdf_document *doc, int *steps);

/** Low-level wrapper for `::pdf_undoredo_step()`. */
FZ_FUNCTION const char *ll_pdf_undoredo_step(::pdf_document *doc, int step);

/** Low-level wrapper for `::pdf_unmark_obj()`. */
FZ_FUNCTION void ll_pdf_unmark_obj(::pdf_obj *obj);

/** Low-level wrapper for `::pdf_update_annot()`. */
FZ_FUNCTION int ll_pdf_update_annot(::pdf_annot *annot);

/** Low-level wrapper for `::pdf_update_default_colorspaces()`. */
FZ_FUNCTION ::fz_default_colorspaces *ll_pdf_update_default_colorspaces(::fz_default_colorspaces *old_cs, ::pdf_obj *res);

/** Low-level wrapper for `::pdf_update_object()`. */
FZ_FUNCTION void ll_pdf_update_object(::pdf_document *doc, int num, ::pdf_obj *obj);

/** Low-level wrapper for `::pdf_update_page()`. */
FZ_FUNCTION int ll_pdf_update_page(::pdf_page *page);

/** Low-level wrapper for `::pdf_update_stream()`. */
FZ_FUNCTION void ll_pdf_update_stream(::pdf_document *doc, ::pdf_obj *ref, ::fz_buffer *buf, int compressed);

/** Low-level wrapper for `::pdf_update_widget()`. */
FZ_FUNCTION int ll_pdf_update_widget(::pdf_annot *widget);

/** Low-level wrapper for `::pdf_update_xobject()`. */
FZ_FUNCTION void ll_pdf_update_xobject(::pdf_document *doc, ::pdf_obj *xobj, ::fz_rect bbox, ::fz_matrix mat, ::pdf_obj *res, ::fz_buffer *buffer);

/** Low-level wrapper for `::pdf_validate_change_history()`. */
FZ_FUNCTION int ll_pdf_validate_change_history(::pdf_document *doc);

/** Low-level wrapper for `::pdf_validate_changes()`. */
FZ_FUNCTION int ll_pdf_validate_changes(::pdf_document *doc, int version);

/** Low-level wrapper for `::pdf_validate_signature()`. */
FZ_FUNCTION int ll_pdf_validate_signature(::pdf_annot *widget);

/** Low-level wrapper for `::pdf_verify_embedded_file_checksum()`. */
FZ_FUNCTION int ll_pdf_verify_embedded_file_checksum(::pdf_obj *fs);

/** Low-level wrapper for `::pdf_version()`. */
FZ_FUNCTION int ll_pdf_version(::pdf_document *doc);

/** Low-level wrapper for `::pdf_walk_tree()`.

This function has out-params. Python/C# wrappers look like:
	`ll_pdf_walk_tree(::pdf_obj *tree, ::pdf_obj *kid_name, void (*arrive)(::fz_context *, ::pdf_obj *, void *, ::pdf_obj **), void (*leave)(::fz_context *, ::pdf_obj *, void *), void *arg, ::pdf_obj **names, ::pdf_obj **values)` => `()`
*/
FZ_FUNCTION void ll_pdf_walk_tree(::pdf_obj *tree, ::pdf_obj *kid_name, void (*arrive)(::fz_context *, ::pdf_obj *, void *, ::pdf_obj **), void (*leave)(::fz_context *, ::pdf_obj *, void *), void *arg, ::pdf_obj **names, ::pdf_obj **values);

/** Low-level wrapper for `::pdf_was_pure_xfa()`. */
FZ_FUNCTION int ll_pdf_was_pure_xfa(::pdf_document *doc);

/** Low-level wrapper for `::pdf_was_repaired()`. */
FZ_FUNCTION int ll_pdf_was_repaired(::pdf_document *doc);

/** Low-level wrapper for `::pdf_widget_is_readonly()`. */
FZ_FUNCTION int ll_pdf_widget_is_readonly(::pdf_annot *widget);

/** Low-level wrapper for `::pdf_widget_is_signed()`. */
FZ_FUNCTION int ll_pdf_widget_is_signed(::pdf_annot *widget);

/** Low-level wrapper for `::pdf_widget_type()`. */
FZ_FUNCTION enum pdf_widget_type ll_pdf_widget_type(::pdf_annot *widget);

/** Low-level wrapper for `::pdf_write_digest()`. */
FZ_FUNCTION void ll_pdf_write_digest(::fz_output *out, ::pdf_obj *byte_range, ::pdf_obj *field, size_t digest_offset, size_t digest_length, ::pdf_pkcs7_signer *signer);

/** Low-level wrapper for `::pdf_write_document()`. */
FZ_FUNCTION void ll_pdf_write_document(::pdf_document *doc, ::fz_output *out, const ::pdf_write_options *opts);

/** Low-level wrapper for `::pdf_write_journal()`. */
FZ_FUNCTION void ll_pdf_write_journal(::pdf_document *doc, ::fz_output *out);

/** Low-level wrapper for `::pdf_write_snapshot()`. */
FZ_FUNCTION void ll_pdf_write_snapshot(::pdf_document *doc, ::fz_output *out);

/** Low-level wrapper for `::pdf_xobject_bbox()`. */
FZ_FUNCTION ::fz_rect ll_pdf_xobject_bbox(::pdf_obj *xobj);

/** Low-level wrapper for `::pdf_xobject_colorspace()`. */
FZ_FUNCTION ::fz_colorspace *ll_pdf_xobject_colorspace(::pdf_obj *xobj);

/** Low-level wrapper for `::pdf_xobject_isolated()`. */
FZ_FUNCTION int ll_pdf_xobject_isolated(::pdf_obj *xobj);

/** Low-level wrapper for `::pdf_xobject_knockout()`. */
FZ_FUNCTION int ll_pdf_xobject_knockout(::pdf_obj *xobj);

/** Low-level wrapper for `::pdf_xobject_matrix()`. */
FZ_FUNCTION ::fz_matrix ll_pdf_xobject_matrix(::pdf_obj *xobj);

/** Low-level wrapper for `::pdf_xobject_resources()`. */
FZ_FUNCTION ::pdf_obj *ll_pdf_xobject_resources(::pdf_obj *xobj);

/** Low-level wrapper for `::pdf_xobject_transparency()`. */
FZ_FUNCTION int ll_pdf_xobject_transparency(::pdf_obj *xobj);

/** Low-level wrapper for `::pdf_xref_ensure_incremental_object()`. */
FZ_FUNCTION int ll_pdf_xref_ensure_incremental_object(::pdf_document *doc, int num);

/** Low-level wrapper for `::pdf_xref_ensure_local_object()`. */
FZ_FUNCTION void ll_pdf_xref_ensure_local_object(::pdf_document *doc, int num);

/** Low-level wrapper for `::pdf_xref_entry_map()`. */
FZ_FUNCTION void ll_pdf_xref_entry_map(::pdf_document *doc, void (*fn)(::fz_context *, ::pdf_xref_entry *, int , ::pdf_document *, void *), void *arg);

/** Low-level wrapper for `::pdf_xref_is_incremental()`. */
FZ_FUNCTION int ll_pdf_xref_is_incremental(::pdf_document *doc, int num);

/** Low-level wrapper for `::pdf_xref_len()`. */
FZ_FUNCTION int ll_pdf_xref_len(::pdf_document *doc);

/** Low-level wrapper for `::pdf_xref_obj_is_unsaved_signature()`. */
FZ_FUNCTION int ll_pdf_xref_obj_is_unsaved_signature(::pdf_document *doc, ::pdf_obj *obj);

/** Low-level wrapper for `::pdf_xref_remove_unsaved_signature()`. */
FZ_FUNCTION void ll_pdf_xref_remove_unsaved_signature(::pdf_document *doc, ::pdf_obj *field);

/** Low-level wrapper for `::pdf_xref_store_unsaved_signature()`. */
FZ_FUNCTION void ll_pdf_xref_store_unsaved_signature(::pdf_document *doc, ::pdf_obj *field, ::pdf_pkcs7_signer *signer);


/* Low-level wrapper for `pdf_dict_getl()`. `keys` must be null-terminated list of `pdf_obj*`'s. */
FZ_FUNCTION pdf_obj* ll_pdf_dict_getlv( pdf_obj* dict, va_list keys);

/* Low-level wrapper for `pdf_dict_getl()`. `...` must be null-terminated list of `pdf_obj*`'s. */
FZ_FUNCTION pdf_obj* ll_pdf_dict_getl( pdf_obj* dict, ...);

/** Returns string containing a fz_aa_context's members, labelled and inside (...), using operator<<. */
FZ_FUNCTION std::string to_string_fz_aa_context(const ::fz_aa_context& s);

/** Returns string containing a fz_aa_context's members, labelled and inside (...), using operator<<.
(Convenience overload). */
FZ_FUNCTION std::string to_string(const ::fz_aa_context& s);

/** Returns string containing a fz_color_params's members, labelled and inside (...), using operator<<. */
FZ_FUNCTION std::string to_string_fz_color_params(const ::fz_color_params& s);

/** Returns string containing a fz_color_params's members, labelled and inside (...), using operator<<.
(Convenience overload). */
FZ_FUNCTION std::string to_string(const ::fz_color_params& s);

/** Returns string containing a fz_cookie's members, labelled and inside (...), using operator<<. */
FZ_FUNCTION std::string to_string_fz_cookie(const ::fz_cookie& s);

/** Returns string containing a fz_cookie's members, labelled and inside (...), using operator<<.
(Convenience overload). */
FZ_FUNCTION std::string to_string(const ::fz_cookie& s);

/** Returns string containing a fz_draw_options's members, labelled and inside (...), using operator<<. */
FZ_FUNCTION std::string to_string_fz_draw_options(const ::fz_draw_options& s);

/** Returns string containing a fz_draw_options's members, labelled and inside (...), using operator<<.
(Convenience overload). */
FZ_FUNCTION std::string to_string(const ::fz_draw_options& s);

/** Returns string containing a fz_install_load_system_font_funcs_args's members, labelled and inside (...), using operator<<. */
FZ_FUNCTION std::string to_string_fz_install_load_system_font_funcs_args(const ::fz_install_load_system_font_funcs_args& s);

/** Returns string containing a fz_install_load_system_font_funcs_args's members, labelled and inside (...), using operator<<.
(Convenience overload). */
FZ_FUNCTION std::string to_string(const ::fz_install_load_system_font_funcs_args& s);

/** Returns string containing a fz_irect's members, labelled and inside (...), using operator<<. */
FZ_FUNCTION std::string to_string_fz_irect(const ::fz_irect& s);

/** Returns string containing a fz_irect's members, labelled and inside (...), using operator<<.
(Convenience overload). */
FZ_FUNCTION std::string to_string(const ::fz_irect& s);

/** Returns string containing a fz_location's members, labelled and inside (...), using operator<<. */
FZ_FUNCTION std::string to_string_fz_location(const ::fz_location& s);

/** Returns string containing a fz_location's members, labelled and inside (...), using operator<<.
(Convenience overload). */
FZ_FUNCTION std::string to_string(const ::fz_location& s);

/** Returns string containing a fz_matrix's members, labelled and inside (...), using operator<<. */
FZ_FUNCTION std::string to_string_fz_matrix(const ::fz_matrix& s);

/** Returns string containing a fz_matrix's members, labelled and inside (...), using operator<<.
(Convenience overload). */
FZ_FUNCTION std::string to_string(const ::fz_matrix& s);

/** Returns string containing a fz_md5's members, labelled and inside (...), using operator<<. */
FZ_FUNCTION std::string to_string_fz_md5(const ::fz_md5& s);

/** Returns string containing a fz_md5's members, labelled and inside (...), using operator<<.
(Convenience overload). */
FZ_FUNCTION std::string to_string(const ::fz_md5& s);

/** Returns string containing a fz_outline_item's members, labelled and inside (...), using operator<<. */
FZ_FUNCTION std::string to_string_fz_outline_item(const ::fz_outline_item& s);

/** Returns string containing a fz_outline_item's members, labelled and inside (...), using operator<<.
(Convenience overload). */
FZ_FUNCTION std::string to_string(const ::fz_outline_item& s);

/** Returns string containing a fz_pdfocr_options's members, labelled and inside (...), using operator<<. */
FZ_FUNCTION std::string to_string_fz_pdfocr_options(const ::fz_pdfocr_options& s);

/** Returns string containing a fz_pdfocr_options's members, labelled and inside (...), using operator<<.
(Convenience overload). */
FZ_FUNCTION std::string to_string(const ::fz_pdfocr_options& s);

/** Returns string containing a fz_point's members, labelled and inside (...), using operator<<. */
FZ_FUNCTION std::string to_string_fz_point(const ::fz_point& s);

/** Returns string containing a fz_point's members, labelled and inside (...), using operator<<.
(Convenience overload). */
FZ_FUNCTION std::string to_string(const ::fz_point& s);

/** Returns string containing a fz_pwg_options's members, labelled and inside (...), using operator<<. */
FZ_FUNCTION std::string to_string_fz_pwg_options(const ::fz_pwg_options& s);

/** Returns string containing a fz_pwg_options's members, labelled and inside (...), using operator<<.
(Convenience overload). */
FZ_FUNCTION std::string to_string(const ::fz_pwg_options& s);

/** Returns string containing a fz_quad's members, labelled and inside (...), using operator<<. */
FZ_FUNCTION std::string to_string_fz_quad(const ::fz_quad& s);

/** Returns string containing a fz_quad's members, labelled and inside (...), using operator<<.
(Convenience overload). */
FZ_FUNCTION std::string to_string(const ::fz_quad& s);

/** Returns string containing a fz_rect's members, labelled and inside (...), using operator<<. */
FZ_FUNCTION std::string to_string_fz_rect(const ::fz_rect& s);

/** Returns string containing a fz_rect's members, labelled and inside (...), using operator<<.
(Convenience overload). */
FZ_FUNCTION std::string to_string(const ::fz_rect& s);

/** Returns string containing a fz_stext_options's members, labelled and inside (...), using operator<<. */
FZ_FUNCTION std::string to_string_fz_stext_options(const ::fz_stext_options& s);

/** Returns string containing a fz_stext_options's members, labelled and inside (...), using operator<<.
(Convenience overload). */
FZ_FUNCTION std::string to_string(const ::fz_stext_options& s);

/** Returns string containing a fz_story_element_position's members, labelled and inside (...), using operator<<. */
FZ_FUNCTION std::string to_string_fz_story_element_position(const ::fz_story_element_position& s);

/** Returns string containing a fz_story_element_position's members, labelled and inside (...), using operator<<.
(Convenience overload). */
FZ_FUNCTION std::string to_string(const ::fz_story_element_position& s);

/** Returns string containing a fz_transition's members, labelled and inside (...), using operator<<. */
FZ_FUNCTION std::string to_string_fz_transition(const ::fz_transition& s);

/** Returns string containing a fz_transition's members, labelled and inside (...), using operator<<.
(Convenience overload). */
FZ_FUNCTION std::string to_string(const ::fz_transition& s);

/** Returns string containing a pdf_clean_options's members, labelled and inside (...), using operator<<. */
FZ_FUNCTION std::string to_string_pdf_clean_options(const ::pdf_clean_options& s);

/** Returns string containing a pdf_clean_options's members, labelled and inside (...), using operator<<.
(Convenience overload). */
FZ_FUNCTION std::string to_string(const ::pdf_clean_options& s);

/** Returns string containing a pdf_filter_factory's members, labelled and inside (...), using operator<<. */
FZ_FUNCTION std::string to_string_pdf_filter_factory(const ::pdf_filter_factory& s);

/** Returns string containing a pdf_filter_factory's members, labelled and inside (...), using operator<<.
(Convenience overload). */
FZ_FUNCTION std::string to_string(const ::pdf_filter_factory& s);

/** Returns string containing a pdf_filter_options's members, labelled and inside (...), using operator<<. */
FZ_FUNCTION std::string to_string_pdf_filter_options(const ::pdf_filter_options& s);

/** Returns string containing a pdf_filter_options's members, labelled and inside (...), using operator<<.
(Convenience overload). */
FZ_FUNCTION std::string to_string(const ::pdf_filter_options& s);

/** Returns string containing a pdf_image_rewriter_options's members, labelled and inside (...), using operator<<. */
FZ_FUNCTION std::string to_string_pdf_image_rewriter_options(const ::pdf_image_rewriter_options& s);

/** Returns string containing a pdf_image_rewriter_options's members, labelled and inside (...), using operator<<.
(Convenience overload). */
FZ_FUNCTION std::string to_string(const ::pdf_image_rewriter_options& s);

/** Returns string containing a pdf_layer_config's members, labelled and inside (...), using operator<<. */
FZ_FUNCTION std::string to_string_pdf_layer_config(const ::pdf_layer_config& s);

/** Returns string containing a pdf_layer_config's members, labelled and inside (...), using operator<<.
(Convenience overload). */
FZ_FUNCTION std::string to_string(const ::pdf_layer_config& s);

/** Returns string containing a pdf_layer_config_ui's members, labelled and inside (...), using operator<<. */
FZ_FUNCTION std::string to_string_pdf_layer_config_ui(const ::pdf_layer_config_ui& s);

/** Returns string containing a pdf_layer_config_ui's members, labelled and inside (...), using operator<<.
(Convenience overload). */
FZ_FUNCTION std::string to_string(const ::pdf_layer_config_ui& s);

/** Returns string containing a pdf_redact_options's members, labelled and inside (...), using operator<<. */
FZ_FUNCTION std::string to_string_pdf_redact_options(const ::pdf_redact_options& s);

/** Returns string containing a pdf_redact_options's members, labelled and inside (...), using operator<<.
(Convenience overload). */
FZ_FUNCTION std::string to_string(const ::pdf_redact_options& s);

/** Returns string containing a pdf_sanitize_filter_options's members, labelled and inside (...), using operator<<. */
FZ_FUNCTION std::string to_string_pdf_sanitize_filter_options(const ::pdf_sanitize_filter_options& s);

/** Returns string containing a pdf_sanitize_filter_options's members, labelled and inside (...), using operator<<.
(Convenience overload). */
FZ_FUNCTION std::string to_string(const ::pdf_sanitize_filter_options& s);

/** Returns string containing a pdf_write_options's members, labelled and inside (...), using operator<<. */
FZ_FUNCTION std::string to_string_pdf_write_options(const ::pdf_write_options& s);

/** Returns string containing a pdf_write_options's members, labelled and inside (...), using operator<<.
(Convenience overload). */
FZ_FUNCTION std::string to_string(const ::pdf_write_options& s);

/** Reinitializes the MuPDF context for single-threaded use, which
is slightly faster when calling code is single threaded.

This should be called before any other use of MuPDF.
*/
FZ_FUNCTION void reinit_singlethreaded();


} /* End of namespace mupdf. */

/** fz_aa_context: writes members, labelled and inside (...), to a stream. */
FZ_FUNCTION std::ostream& operator<< (std::ostream& out, const ::fz_aa_context& rhs);

/** fz_aa_context: comparison function. */
FZ_FUNCTION bool operator==( const ::fz_aa_context& lhs, const ::fz_aa_context& rhs);

/** fz_aa_context: comparison function. */
FZ_FUNCTION bool operator!=( const ::fz_aa_context& lhs, const ::fz_aa_context& rhs);

/** fz_color_params: writes members, labelled and inside (...), to a stream. */
FZ_FUNCTION std::ostream& operator<< (std::ostream& out, const ::fz_color_params& rhs);

/** fz_color_params: comparison function. */
FZ_FUNCTION bool operator==( const ::fz_color_params& lhs, const ::fz_color_params& rhs);

/** fz_color_params: comparison function. */
FZ_FUNCTION bool operator!=( const ::fz_color_params& lhs, const ::fz_color_params& rhs);

/** fz_cookie: writes members, labelled and inside (...), to a stream. */
FZ_FUNCTION std::ostream& operator<< (std::ostream& out, const ::fz_cookie& rhs);

/** fz_cookie: comparison function. */
FZ_FUNCTION bool operator==( const ::fz_cookie& lhs, const ::fz_cookie& rhs);

/** fz_cookie: comparison function. */
FZ_FUNCTION bool operator!=( const ::fz_cookie& lhs, const ::fz_cookie& rhs);

/** fz_draw_options: writes members, labelled and inside (...), to a stream. */
FZ_FUNCTION std::ostream& operator<< (std::ostream& out, const ::fz_draw_options& rhs);

/** fz_draw_options: comparison function. */
FZ_FUNCTION bool operator==( const ::fz_draw_options& lhs, const ::fz_draw_options& rhs);

/** fz_draw_options: comparison function. */
FZ_FUNCTION bool operator!=( const ::fz_draw_options& lhs, const ::fz_draw_options& rhs);

/** fz_install_load_system_font_funcs_args: writes members, labelled and inside (...), to a stream. */
FZ_FUNCTION std::ostream& operator<< (std::ostream& out, const ::fz_install_load_system_font_funcs_args& rhs);

/** fz_install_load_system_font_funcs_args: comparison function. */
FZ_FUNCTION bool operator==( const ::fz_install_load_system_font_funcs_args& lhs, const ::fz_install_load_system_font_funcs_args& rhs);

/** fz_install_load_system_font_funcs_args: comparison function. */
FZ_FUNCTION bool operator!=( const ::fz_install_load_system_font_funcs_args& lhs, const ::fz_install_load_system_font_funcs_args& rhs);

/** fz_irect: writes members, labelled and inside (...), to a stream. */
FZ_FUNCTION std::ostream& operator<< (std::ostream& out, const ::fz_irect& rhs);

/** fz_irect: comparison function. */
FZ_FUNCTION bool operator==( const ::fz_irect& lhs, const ::fz_irect& rhs);

/** fz_irect: comparison function. */
FZ_FUNCTION bool operator!=( const ::fz_irect& lhs, const ::fz_irect& rhs);

/** fz_location: writes members, labelled and inside (...), to a stream. */
FZ_FUNCTION std::ostream& operator<< (std::ostream& out, const ::fz_location& rhs);

/** fz_location: comparison function. */
FZ_FUNCTION bool operator==( const ::fz_location& lhs, const ::fz_location& rhs);

/** fz_location: comparison function. */
FZ_FUNCTION bool operator!=( const ::fz_location& lhs, const ::fz_location& rhs);

/** fz_matrix: writes members, labelled and inside (...), to a stream. */
FZ_FUNCTION std::ostream& operator<< (std::ostream& out, const ::fz_matrix& rhs);

/** fz_matrix: comparison function. */
FZ_FUNCTION bool operator==( const ::fz_matrix& lhs, const ::fz_matrix& rhs);

/** fz_matrix: comparison function. */
FZ_FUNCTION bool operator!=( const ::fz_matrix& lhs, const ::fz_matrix& rhs);

/** fz_md5: writes members, labelled and inside (...), to a stream. */
FZ_FUNCTION std::ostream& operator<< (std::ostream& out, const ::fz_md5& rhs);

/** fz_md5: comparison function. */
FZ_FUNCTION bool operator==( const ::fz_md5& lhs, const ::fz_md5& rhs);

/** fz_md5: comparison function. */
FZ_FUNCTION bool operator!=( const ::fz_md5& lhs, const ::fz_md5& rhs);

/** fz_outline_item: writes members, labelled and inside (...), to a stream. */
FZ_FUNCTION std::ostream& operator<< (std::ostream& out, const ::fz_outline_item& rhs);

/** fz_outline_item: comparison function. */
FZ_FUNCTION bool operator==( const ::fz_outline_item& lhs, const ::fz_outline_item& rhs);

/** fz_outline_item: comparison function. */
FZ_FUNCTION bool operator!=( const ::fz_outline_item& lhs, const ::fz_outline_item& rhs);

/** fz_pdfocr_options: writes members, labelled and inside (...), to a stream. */
FZ_FUNCTION std::ostream& operator<< (std::ostream& out, const ::fz_pdfocr_options& rhs);

/** fz_pdfocr_options: comparison function. */
FZ_FUNCTION bool operator==( const ::fz_pdfocr_options& lhs, const ::fz_pdfocr_options& rhs);

/** fz_pdfocr_options: comparison function. */
FZ_FUNCTION bool operator!=( const ::fz_pdfocr_options& lhs, const ::fz_pdfocr_options& rhs);

/** fz_point: writes members, labelled and inside (...), to a stream. */
FZ_FUNCTION std::ostream& operator<< (std::ostream& out, const ::fz_point& rhs);

/** fz_point: comparison function. */
FZ_FUNCTION bool operator==( const ::fz_point& lhs, const ::fz_point& rhs);

/** fz_point: comparison function. */
FZ_FUNCTION bool operator!=( const ::fz_point& lhs, const ::fz_point& rhs);

/** fz_pwg_options: writes members, labelled and inside (...), to a stream. */
FZ_FUNCTION std::ostream& operator<< (std::ostream& out, const ::fz_pwg_options& rhs);

/** fz_pwg_options: comparison function. */
FZ_FUNCTION bool operator==( const ::fz_pwg_options& lhs, const ::fz_pwg_options& rhs);

/** fz_pwg_options: comparison function. */
FZ_FUNCTION bool operator!=( const ::fz_pwg_options& lhs, const ::fz_pwg_options& rhs);

/** fz_quad: writes members, labelled and inside (...), to a stream. */
FZ_FUNCTION std::ostream& operator<< (std::ostream& out, const ::fz_quad& rhs);

/** fz_quad: comparison function. */
FZ_FUNCTION bool operator==( const ::fz_quad& lhs, const ::fz_quad& rhs);

/** fz_quad: comparison function. */
FZ_FUNCTION bool operator!=( const ::fz_quad& lhs, const ::fz_quad& rhs);

/** fz_rect: writes members, labelled and inside (...), to a stream. */
FZ_FUNCTION std::ostream& operator<< (std::ostream& out, const ::fz_rect& rhs);

/** fz_rect: comparison function. */
FZ_FUNCTION bool operator==( const ::fz_rect& lhs, const ::fz_rect& rhs);

/** fz_rect: comparison function. */
FZ_FUNCTION bool operator!=( const ::fz_rect& lhs, const ::fz_rect& rhs);

/** fz_stext_options: writes members, labelled and inside (...), to a stream. */
FZ_FUNCTION std::ostream& operator<< (std::ostream& out, const ::fz_stext_options& rhs);

/** fz_stext_options: comparison function. */
FZ_FUNCTION bool operator==( const ::fz_stext_options& lhs, const ::fz_stext_options& rhs);

/** fz_stext_options: comparison function. */
FZ_FUNCTION bool operator!=( const ::fz_stext_options& lhs, const ::fz_stext_options& rhs);

/** fz_story_element_position: writes members, labelled and inside (...), to a stream. */
FZ_FUNCTION std::ostream& operator<< (std::ostream& out, const ::fz_story_element_position& rhs);

/** fz_story_element_position: comparison function. */
FZ_FUNCTION bool operator==( const ::fz_story_element_position& lhs, const ::fz_story_element_position& rhs);

/** fz_story_element_position: comparison function. */
FZ_FUNCTION bool operator!=( const ::fz_story_element_position& lhs, const ::fz_story_element_position& rhs);

/** fz_transition: writes members, labelled and inside (...), to a stream. */
FZ_FUNCTION std::ostream& operator<< (std::ostream& out, const ::fz_transition& rhs);

/** fz_transition: comparison function. */
FZ_FUNCTION bool operator==( const ::fz_transition& lhs, const ::fz_transition& rhs);

/** fz_transition: comparison function. */
FZ_FUNCTION bool operator!=( const ::fz_transition& lhs, const ::fz_transition& rhs);

/** pdf_clean_options: writes members, labelled and inside (...), to a stream. */
FZ_FUNCTION std::ostream& operator<< (std::ostream& out, const ::pdf_clean_options& rhs);

/** pdf_clean_options: comparison function. */
FZ_FUNCTION bool operator==( const ::pdf_clean_options& lhs, const ::pdf_clean_options& rhs);

/** pdf_clean_options: comparison function. */
FZ_FUNCTION bool operator!=( const ::pdf_clean_options& lhs, const ::pdf_clean_options& rhs);

/** pdf_filter_factory: writes members, labelled and inside (...), to a stream. */
FZ_FUNCTION std::ostream& operator<< (std::ostream& out, const ::pdf_filter_factory& rhs);

/** pdf_filter_factory: comparison function. */
FZ_FUNCTION bool operator==( const ::pdf_filter_factory& lhs, const ::pdf_filter_factory& rhs);

/** pdf_filter_factory: comparison function. */
FZ_FUNCTION bool operator!=( const ::pdf_filter_factory& lhs, const ::pdf_filter_factory& rhs);

/** pdf_filter_options: writes members, labelled and inside (...), to a stream. */
FZ_FUNCTION std::ostream& operator<< (std::ostream& out, const ::pdf_filter_options& rhs);

/** pdf_filter_options: comparison function. */
FZ_FUNCTION bool operator==( const ::pdf_filter_options& lhs, const ::pdf_filter_options& rhs);

/** pdf_filter_options: comparison function. */
FZ_FUNCTION bool operator!=( const ::pdf_filter_options& lhs, const ::pdf_filter_options& rhs);

/** pdf_image_rewriter_options: writes members, labelled and inside (...), to a stream. */
FZ_FUNCTION std::ostream& operator<< (std::ostream& out, const ::pdf_image_rewriter_options& rhs);

/** pdf_image_rewriter_options: comparison function. */
FZ_FUNCTION bool operator==( const ::pdf_image_rewriter_options& lhs, const ::pdf_image_rewriter_options& rhs);

/** pdf_image_rewriter_options: comparison function. */
FZ_FUNCTION bool operator!=( const ::pdf_image_rewriter_options& lhs, const ::pdf_image_rewriter_options& rhs);

/** pdf_layer_config: writes members, labelled and inside (...), to a stream. */
FZ_FUNCTION std::ostream& operator<< (std::ostream& out, const ::pdf_layer_config& rhs);

/** pdf_layer_config: comparison function. */
FZ_FUNCTION bool operator==( const ::pdf_layer_config& lhs, const ::pdf_layer_config& rhs);

/** pdf_layer_config: comparison function. */
FZ_FUNCTION bool operator!=( const ::pdf_layer_config& lhs, const ::pdf_layer_config& rhs);

/** pdf_layer_config_ui: writes members, labelled and inside (...), to a stream. */
FZ_FUNCTION std::ostream& operator<< (std::ostream& out, const ::pdf_layer_config_ui& rhs);

/** pdf_layer_config_ui: comparison function. */
FZ_FUNCTION bool operator==( const ::pdf_layer_config_ui& lhs, const ::pdf_layer_config_ui& rhs);

/** pdf_layer_config_ui: comparison function. */
FZ_FUNCTION bool operator!=( const ::pdf_layer_config_ui& lhs, const ::pdf_layer_config_ui& rhs);

/** pdf_redact_options: writes members, labelled and inside (...), to a stream. */
FZ_FUNCTION std::ostream& operator<< (std::ostream& out, const ::pdf_redact_options& rhs);

/** pdf_redact_options: comparison function. */
FZ_FUNCTION bool operator==( const ::pdf_redact_options& lhs, const ::pdf_redact_options& rhs);

/** pdf_redact_options: comparison function. */
FZ_FUNCTION bool operator!=( const ::pdf_redact_options& lhs, const ::pdf_redact_options& rhs);

/** pdf_sanitize_filter_options: writes members, labelled and inside (...), to a stream. */
FZ_FUNCTION std::ostream& operator<< (std::ostream& out, const ::pdf_sanitize_filter_options& rhs);

/** pdf_sanitize_filter_options: comparison function. */
FZ_FUNCTION bool operator==( const ::pdf_sanitize_filter_options& lhs, const ::pdf_sanitize_filter_options& rhs);

/** pdf_sanitize_filter_options: comparison function. */
FZ_FUNCTION bool operator!=( const ::pdf_sanitize_filter_options& lhs, const ::pdf_sanitize_filter_options& rhs);

/** pdf_write_options: writes members, labelled and inside (...), to a stream. */
FZ_FUNCTION std::ostream& operator<< (std::ostream& out, const ::pdf_write_options& rhs);

/** pdf_write_options: comparison function. */
FZ_FUNCTION bool operator==( const ::pdf_write_options& lhs, const ::pdf_write_options& rhs);

/** pdf_write_options: comparison function. */
FZ_FUNCTION bool operator!=( const ::pdf_write_options& lhs, const ::pdf_write_options& rhs);

#endif
